<?xml version="1.0"?>
<doc>
    <assembly>
        <name>backend</name>
    </assembly>
    <members>
        <member name="T:backend.Controllers.FormController">
            <summary>
            Controller for managing form submissions
            </summary>
        </member>
        <member name="M:backend.Controllers.FormController.#ctor(backend.Repositories.Interfaces.IUserRepository,backend.Repositories.Interfaces.IParentsRepository,backend.Service.IMailService)">
            <summary>
            Controller for managing form submissions
            </summary>
        </member>
        <member name="M:backend.Controllers.FormController.GetFormByTrackingCode(System.String)">
            <summary>
            Gets form data by tracking code
            </summary>
            <param name="code">The tracking code to look up</param>
            <returns>The user associated with the tracking code</returns>
            <response code="200">Returns the user associated with the tracking code</response>
            <response code="404">If no user is found with the specified tracking code</response>
        </member>
        <member name="M:backend.Controllers.FormController.SubmitForm(backend.Controllers.FormSubmissionDTO)">
            <summary>
            Submits a new form with user and parent information
            </summary>
            <param name="submission">The form submission data containing user and parent information</param>
            <returns>A result containing tracking code and other important information</returns>
            <response code="200">If the form was submitted successfully</response>
            <response code="400">If the form data is invalid</response>
        </member>
        <member name="M:backend.Controllers.FormController.VerifyFormSubmission(System.Guid)">
            <summary>
            Verifies if a form submission has been approved by the parent
            </summary>
            <param name="userId">The ID of the user to verify</param>
            <returns>True if the parent has given approval, otherwise false</returns>
            <response code="200">Returns the approval status</response>
            <response code="404">If the user is not found</response>
            <response code="400">If parent information is missing</response>
        </member>
        <member name="M:backend.Controllers.FormController.GetAgeStatistics">
            <summary>
            Gets age-based statistics for applications
            </summary>
            <returns>Age range statistics including application counts</returns>
            <response code="200">Returns age-based statistics</response>
            <response code="401">If user is not authenticated</response>
            <response code="403">If user is not authorized</response>
        </member>
        <member name="M:backend.Controllers.FormController.ExportToExcel">
            <summary>
            Exports all form submissions to Excel file
            </summary>
            <returns>Excel file containing all applicant data</returns>
            <response code="200">Returns the Excel file</response>
            <response code="401">If user is not authenticated</response>
            <response code="403">If user is not authorized</response>
        </member>
        <member name="M:backend.Controllers.FormController.GetStatusTextTurkish(System.Nullable{System.Int32})">
            <summary>
            Gets status text in Turkish for Excel export
            </summary>
            <param name="status">Status value</param>
            <returns>Turkish status text</returns>
        </member>
        <member name="M:backend.Controllers.FormController.GenerateQRCode(System.String)">
            <summary>
            Generates QR code as base64 string
            </summary>
            <param name="text">Text to encode in QR code</param>
            <returns>Base64 encoded QR code image</returns>
        </member>
        <member name="M:backend.Controllers.FormController.GetTrainingTime(System.DateTime)">
            <summary>
            Gets training time based on birth year
            </summary>
            <param name="birthDate">User's birth date</param>
            <returns>Training time slot as string</returns>
        </member>
        <member name="M:backend.Controllers.FormController.GetEmailTemplate(backend.Models.User)">
            <summary>
            Generates HTML email template with user-specific information
            </summary>
            <param name="user">User entity containing form submission data</param>
            <returns>HTML email template as string</returns>
        </member>
        <member name="T:backend.Controllers.FormSubmissionDTO">
            <summary>
            Data model for form submission containing both user and parent information
            </summary>
        </member>
        <member name="P:backend.Controllers.FormSubmissionDTO.User">
            <summary>
            The user's information
            </summary>
        </member>
        <member name="P:backend.Controllers.FormSubmissionDTO.Parent">
            <summary>
            The parent's information
            </summary>
        </member>
        <member name="T:backend.Controllers.ParentsController">
            <summary>
            Controller for managing parents data
            </summary>
        </member>
        <member name="M:backend.Controllers.ParentsController.#ctor(backend.Repositories.Interfaces.IParentsRepository)">
            <summary>
            Controller for managing parents data
            </summary>
        </member>
        <member name="M:backend.Controllers.ParentsController.GetParents">
            <summary>
            Gets all parents with their associated users
            </summary>
            <returns>A list of all parents with their users</returns>
            <response code="200">Returns the list of parents</response>
        </member>
        <member name="M:backend.Controllers.ParentsController.GetParent(System.Guid)">
            <summary>
            Gets a specific parent by ID with their associated users
            </summary>
            <param name="id">The ID of the parent to retrieve</param>
            <returns>The parent with the specified ID</returns>
            <response code="200">Returns the requested parent</response>
            <response code="404">If the parent is not found</response>
        </member>
        <member name="M:backend.Controllers.ParentsController.PostParent(backend.Models.DTOs.ParentDTO)">
            <summary>
            Creates a new parent
            </summary>
            <param name="parentDto">The parent to create</param>
            <returns>The newly created parent</returns>
            <response code="201">Returns the newly created parent</response>
            <response code="400">If the parent data is invalid</response>
        </member>
        <member name="M:backend.Controllers.ParentsController.PutParent(System.Guid,backend.Models.DTOs.ParentDTO)">
            <summary>
            Updates a specific parent
            </summary>
            <param name="id">The ID of the parent to update</param>
            <param name="parentDto">The updated parent data</param>
            <returns>No content</returns>
            <response code="204">If the update was successful</response>
            <response code="400">If the ID in the URL doesn't match the ID in the parent object</response>
            <response code="404">If the parent is not found</response>
        </member>
        <member name="M:backend.Controllers.ParentsController.DeleteParent(System.Guid)">
            <summary>
            Deletes a specific parent
            </summary>
            <param name="id">The ID of the parent to delete</param>
            <returns>No content</returns>
            <response code="204">If the deletion was successful</response>
            <response code="404">If the parent is not found</response>
        </member>
        <member name="M:backend.Controllers.ParentsController.ApproveParent(System.Guid,System.Boolean)">
            <summary>
            Sets the approval status for a parent
            </summary>
            <param name="id">The ID of the parent</param>
            <param name="approved">Whether the parent approves the registration</param>
            <returns>No content</returns>
            <response code="204">If the approval status was updated successfully</response>
            <response code="404">If the parent is not found</response>
        </member>
        <member name="M:backend.Controllers.RatingController.GetAllRatings">
            <summary>
            Get all ratings (Admin only)
            </summary>
        </member>
        <member name="M:backend.Controllers.RatingController.GetUserRatings(System.Guid)">
            <summary>
            Get ratings for a specific user (Admin only)
            </summary>
        </member>
        <member name="M:backend.Controllers.RatingController.GetMyRatings">
            <summary>
            Get current moderator's ratings
            </summary>
        </member>
        <member name="M:backend.Controllers.RatingController.GetMyRatingForUser(System.Guid)">
            <summary>
            Get current moderator's rating for a specific user
            </summary>
        </member>
        <member name="M:backend.Controllers.RatingController.CreateOrUpdateRating(backend.Models.DTOs.CreateRatingDTO)">
            <summary>
            Create or update rating for a user
            </summary>
        </member>
        <member name="M:backend.Controllers.RatingController.DeleteRating(System.Guid)">
            <summary>
            Delete a rating (Admin only)
            </summary>
        </member>
        <member name="T:backend.Controllers.UserController">
            <summary>
            Controller for managing user data
            </summary>
        </member>
        <member name="M:backend.Controllers.UserController.#ctor(backend.Repositories.Interfaces.IUserRepository)">
            <summary>
            Controller for managing user data
            </summary>
        </member>
        <member name="M:backend.Controllers.UserController.IsAdminOrModeratorUser(backend.Models.User)">
            <summary>
            Checks if a user is an admin or moderator user
            </summary>
            <param name="user">The user to check</param>
            <returns>True if the user is an admin or moderator, false otherwise</returns>
        </member>
        <member name="M:backend.Controllers.UserController.GetUsers">
            <summary>
            Gets all users with their associated parent information
            </summary>
            <returns>A list of all users with parent details</returns>
            <response code="200">Returns the list of users</response>
        </member>
        <member name="M:backend.Controllers.UserController.GetUser(System.Guid)">
            <summary>
            Gets a specific user by ID with parent information
            </summary>
            <param name="id">The ID of the user to retrieve</param>
            <returns>The user with the specified ID</returns>
            <response code="200">Returns the requested user</response>
            <response code="404">If the user is not found</response>
        </member>
        <member name="M:backend.Controllers.UserController.GetUserByTrackingCode(System.String)">
            <summary>
            Gets a user by their tracking code
            </summary>
            <param name="code">The tracking code of the user to retrieve</param>
            <returns>The user with the specified tracking code</returns>
            <response code="200">Returns the requested user</response>
            <response code="404">If the user is not found</response>
        </member>
        <member name="M:backend.Controllers.UserController.PutUser(System.Guid,backend.Models.DTOs.UserWithParentDTO)">
            <summary>
            Updates a specific user
            </summary>
            <param name="id">The ID of the user to update</param>
            <param name="userDto">The updated user data</param>
            <returns>No content</returns>
            <response code="204">If the update was successful</response>
            <response code="400">If the ID in the URL doesn't match the ID in the user object</response>
            <response code="404">If the user is not found</response>
        </member>
        <member name="M:backend.Controllers.UserController.PostUser(backend.Models.DTOs.UserWithParentDTO)">
            <summary>
            Creates a new user
            </summary>
            <param name="userDto">The user to create</param>
            <returns>The newly created user</returns>
            <response code="201">Returns the newly created user</response>
            <response code="400">If the user data is invalid</response>
        </member>
        <member name="M:backend.Controllers.UserController.DeleteUser(System.Guid)">
            <summary>
            Deletes a specific user
            </summary>
            <param name="id">The ID of the user to delete</param>
            <returns>No content</returns>
            <response code="204">If the deletion was successful</response>
            <response code="404">If the user is not found</response>
        </member>
        <member name="T:backend.Migrations.init">
            <inheritdoc />
        </member>
        <member name="M:backend.Migrations.init.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:backend.Migrations.init.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:backend.Migrations.init.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:backend.Models.DTOs.CreateModeratorDTO">
            <summary>
            DTO for creating or updating a moderator user
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.CreateModeratorDTO.FullName">
            <summary>
            The full name of the moderator
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.CreateModeratorDTO.Email">
            <summary>
            The email address of the moderator
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.CreateModeratorDTO.Password">
            <summary>
            The password for the moderator account (optional for updates)
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.LoginDTO.Email">
            <summary>
            The email address of the user for login
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.LoginDTO.Password">
            <summary>
            The password for the user account
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.MailSettings.SmtpPort">
            <summary>
            SMTP portu (TLS için genellikle 587, SSL için 465)
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.MailSettings.SenderEmail">
            <summary>
            Gönderen e-posta adresi
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.MailSettings.SenderPassword">
            <summary>
            Gönderen e-posta şifresi (veya uygulama şifresi)
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.MailSettings.SenderName">
            <summary>
            Gönderenin görünen adı (opsiyonel)
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.UserWithParentDTO.JumpHeight">
            <summary>
            The jump height of the user in centimeters
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.UserWithParentDTO.JumpRecordDate">
            <summary>
            The date when the jump height was recorded
            </summary>
        </member>
        <member name="P:backend.Models.DTOs.UserWithParentDTO.Status">
            <summary>
            Status of the user selection process
            0: Not Decided, 1: Selected, 2: Not Selected
            </summary>
        </member>
        <member name="T:backend.Models.Parents">
            <summary>
            Represents a parent or guardian of a user in the Fenerbahçe form system
            </summary>
        </member>
        <member name="P:backend.Models.Parents.Id">
            <summary>
            The unique identifier for the parent
            </summary>
        </member>
        <member name="P:backend.Models.Parents.FullName">
            <summary>
            The full name of the parent
            </summary>
        </member>
        <member name="P:backend.Models.Parents.MobilePhone">
            <summary>
            The mobile phone number of the parent
            </summary>
        </member>
        <member name="P:backend.Models.Parents.MotherHeight">
            <summary>
            The height of the mother in centimeters
            </summary>
        </member>
        <member name="P:backend.Models.Parents.FatherHeight">
            <summary>
            The height of the father in centimeters
            </summary>
        </member>
        <member name="P:backend.Models.Parents.RelationshipType">
            <summary>
            The relationship type of the parent to the child (e.g., Mother, Father, Guardian)
            </summary>
        </member>
        <member name="P:backend.Models.Parents.ApprovalGiven">
            <summary>
            Indicates whether the parent has given approval for the registration
            </summary>
        </member>
        <member name="P:backend.Models.Parents.ApprovalDate">
            <summary>
            The date when the parent gave their approval
            </summary>
        </member>
        <member name="P:backend.Models.Parents.Users">
            <summary>
            Collection of users associated with this parent
            </summary>
        </member>
        <member name="T:backend.Models.Rating">
            <summary>
            Represents a rating given by a moderator for a user
            </summary>
        </member>
        <member name="P:backend.Models.Rating.Id">
            <summary>
            The unique identifier for the rating
            </summary>
        </member>
        <member name="P:backend.Models.Rating.UserId">
            <summary>
            The ID of the user being rated
            </summary>
        </member>
        <member name="P:backend.Models.Rating.User">
            <summary>
            Navigation property to the user being rated
            </summary>
        </member>
        <member name="P:backend.Models.Rating.ModeratorId">
            <summary>
            The ID of the moderator giving the rating
            </summary>
        </member>
        <member name="P:backend.Models.Rating.Moderator">
            <summary>
            Navigation property to the moderator giving the rating
            </summary>
        </member>
        <member name="P:backend.Models.Rating.ToplaBeceri">
            <summary>
            Total skill score assigned by the moderator
            </summary>
        </member>
        <member name="P:backend.Models.Rating.FizikselOzellik">
            <summary>
            Physical characteristics score assigned by the moderator
            </summary>
        </member>
        <member name="P:backend.Models.Rating.Notes">
            <summary>
            General notes about the user from the moderator
            </summary>
        </member>
        <member name="P:backend.Models.Rating.CreatedAt">
            <summary>
            Date when the rating was created
            </summary>
        </member>
        <member name="P:backend.Models.Rating.UpdatedAt">
            <summary>
            Date when the rating was last updated
            </summary>
        </member>
        <member name="T:backend.Models.User">
            <summary>
            Represents a user registered in the Fenerbahçe form system
            </summary>
        </member>
        <member name="P:backend.Models.User.FullName">
            <summary>
            The full name of the user
            </summary>
        </member>
        <member name="P:backend.Models.User.DateOfBirth">
            <summary>
            The date of birth of the user
            </summary>
        </member>
        <member name="P:backend.Models.User.Height">
            <summary>
            The height of the user in centimeters
            </summary>
        </member>
        <member name="P:backend.Models.User.Weight">
            <summary>
            The weight of the user in kilograms
            </summary>
        </member>
        <member name="P:backend.Models.User.Province">
            <summary>
            The province (il) where the user lives
            </summary>
        </member>
        <member name="P:backend.Models.User.District">
            <summary>
            The district (ilçe) where the user lives
            </summary>
        </member>
        <member name="P:backend.Models.User.ArmSpan">
            <summary>
            The arm span/wingspan of the user in centimeters
            </summary>
        </member>
        <member name="P:backend.Models.User.ShoeSize">
            <summary>
            The shoe size of the user (EU standard)
            </summary>
        </member>
        <member name="P:backend.Models.User.PalmSize">
            <summary>
            The palm size of the user in centimeters
            </summary>
        </member>
        <member name="P:backend.Models.User.HasPlayedForClub">
            <summary>
            Indicates whether the user has played for a club before
            </summary>
        </member>
        <member name="P:backend.Models.User.ClubName">
            <summary>
            The name of the club the user has played for, if applicable
            </summary>
        </member>
        <member name="P:backend.Models.User.HasHealthIssues">
            <summary>
            Indicates whether the user has any health issues
            </summary>
        </member>
        <member name="P:backend.Models.User.HealthIssues">
            <summary>
            Description of health issues, if applicable
            </summary>
        </member>
        <member name="P:backend.Models.User.ParentId">
            <summary>
            The ID of the parent associated with this user
            </summary>
        </member>
        <member name="P:backend.Models.User.Parent">
            <summary>
            Navigation property to the parent of this user
            </summary>
        </member>
        <member name="P:backend.Models.User.TrackingSequence">
            <summary>
            A sequential number used for tracking code generation per birth year.
            Each birth year starts from 1 and increments for each user born in that year.
            This value should be calculated and set when creating a new user based on
            the count of existing users with the same birth year.
            </summary>
        </member>
        <member name="P:backend.Models.User.TrackingCode">
            <summary>
            A unique tracking code generated for this user
            Format: {LastTwoDigitsOfBirthYear}{SequenceNumber:D3}
            Example: 11001 for birth year 2011, sequence 1 (first person born in 2011)
            Example: 11002 for birth year 2011, sequence 2 (second person born in 2011)
            Example: 12001 for birth year 2012, sequence 1 (first person born in 2012)
            Note: TrackingSequence should be calculated per birth year, starting from 1 for each year
            </summary>
        </member>
        <member name="P:backend.Models.User.JumpHeight">
            <summary>
            The jump height of the user in centimeters
            </summary>
        </member>
        <member name="P:backend.Models.User.JumpRecordDate">
            <summary>
            The date when the jump height was recorded
            </summary>
        </member>
        <member name="M:backend.Repositories.UserRepository.GetNextTrackingSequenceForBirthYearAsync(System.Int32)">
            <summary>
            Gets the next tracking sequence number for users born in a specific year.
            Each birth year starts from 1 and increments for each user.
            </summary>
            <param name="birthYear">The birth year to get the next sequence for</param>
            <returns>The next sequence number for that birth year (starting from 1)</returns>
        </member>
        <member name="M:backend.Repositories.UserRepository.GetNextTrackingCode">
            <summary>
            [Obsolete] Use GetNextTrackingSequenceForBirthYearAsync instead for birth year based sequences
            </summary>
        </member>
    </members>
</doc>
