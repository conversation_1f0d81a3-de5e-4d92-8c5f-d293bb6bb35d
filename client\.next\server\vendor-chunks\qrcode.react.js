"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/qrcode.react";
exports.ids = ["vendor-chunks/qrcode.react"];
exports.modules = {

/***/ "(ssr)/./node_modules/qrcode.react/lib/esm/index.js":
/*!****************************************************!*\
  !*** ./node_modules/qrcode.react/lib/esm/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QRCodeCanvas: () => (/* binding */ QRCodeCanvas),\n/* harmony export */   QRCodeSVG: () => (/* binding */ QRCodeSVG)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/index.tsx\n\n\n// src/third-party/qrcodegen/index.ts\n/**\n * @license QR Code generator library (TypeScript)\n * Copyright (c) Project Nayuki.\n * SPDX-License-Identifier: MIT\n */\nvar qrcodegen;\n((qrcodegen2) => {\n  const _QrCode = class _QrCode {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code with the given version number,\n    // error correction level, data codeword bytes, and mask number.\n    // This is a low-level API that most users should not use directly.\n    // A mid-level API is the encodeSegments() function.\n    constructor(version, errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      // The modules of this QR Code (false = light, true = dark).\n      // Immutable after constructor finishes. Accessed through getModule().\n      this.modules = [];\n      // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n      this.isFunction = [];\n      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7)\n        throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      let row = [];\n      for (let i = 0; i < this.size; i++)\n        row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice());\n        this.isFunction.push(row.slice());\n      }\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      if (msk == -1) {\n        let minPenalty = 1e9;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i);\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk);\n      this.drawFormatBits(msk);\n      this.isFunction = [];\n    }\n    /*-- Static factory functions (high level) --*/\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    static encodeText(text, ecl) {\n      const segs = qrcodegen2.QrSegment.makeSegments(text);\n      return _QrCode.encodeSegments(segs, ecl);\n    }\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen2.QrSegment.makeBytes(data);\n      return _QrCode.encodeSegments([seg], ecl);\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7)\n        throw new RangeError(\"Invalid value\");\n      let version;\n      let dataUsedBits;\n      for (version = minVersion; ; version++) {\n        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits2) {\n          dataUsedBits = usedBits;\n          break;\n        }\n        if (version >= maxVersion)\n          throw new RangeError(\"Data too long\");\n      }\n      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {\n        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8)\n          ecl = newEcl;\n      }\n      let bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData())\n          bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)\n        appendBits(padByte, 8, bb);\n      let dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length)\n        dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      return new _QrCode(version, ecl, dataCodewords, mask);\n    }\n    /*-- Accessor methods --*/\n    // Returns the color of the module (pixel) at the given coordinates, which is false\n    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n    // If the given coordinates are out of bounds, then false (light) is returned.\n    getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n    // Modified to expose modules for easy access\n    getModules() {\n      return this.modules;\n    }\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n    // Reads this object's version field, and draws and marks all function modules.\n    drawFunctionPatterns() {\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      this.drawFormatBits(0);\n      this.drawVersion();\n    }\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n    drawFormatBits(mask) {\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask;\n      let rem = data;\n      for (let i = 0; i < 10; i++)\n        rem = rem << 1 ^ (rem >>> 9) * 1335;\n      const bits = (data << 10 | rem) ^ 21522;\n      assert(bits >>> 15 == 0);\n      for (let i = 0; i <= 5; i++)\n        this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++)\n        this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      for (let i = 0; i < 8; i++)\n        this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++)\n        this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true);\n    }\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n    drawVersion() {\n      if (this.version < 7)\n        return;\n      let rem = this.version;\n      for (let i = 0; i < 12; i++)\n        rem = rem << 1 ^ (rem >>> 11) * 7973;\n      const bits = this.version << 12 | rem;\n      assert(bits >>> 18 == 0);\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy));\n          const xx = x + dx;\n          const yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++)\n          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != _QrCode.getNumDataCodewords(ver, ecl))\n        throw new RangeError(\"Invalid argument\");\n      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      let blocks = [];\n      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks)\n          dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      let result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n            result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n    drawCodewords(data) {\n      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8))\n        throw new RangeError(\"Invalid argument\");\n      let i = 0;\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        if (right == 6)\n          right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          for (let j = 0; j < 2; j++) {\n            const x = right - j;\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert;\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n    applyMask(mask) {\n      if (mask < 0 || mask > 7)\n        throw new RangeError(\"Mask value out of range\");\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert)\n            this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n    getPenaltyScore() {\n      let result = 0;\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runX > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runY > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])\n            result += _QrCode.PENALTY_N2;\n        }\n      }\n      let dark = 0;\n      for (const row of this.modules)\n        dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size;\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * _QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888);\n      return result;\n    }\n    /*-- Private helper functions --*/\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of integers.\n    getAlignmentPatternPositions() {\n      if (this.version == 1)\n        return [];\n      else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        let result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step)\n          result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n    static getNumRawDataModules(ver) {\n      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version number out of range\");\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7)\n          result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255)\n        throw new RangeError(\"Degree out of range\");\n      let result = [];\n      for (let i = 0; i < degree - 1; i++)\n        result.push(0);\n      result.push(1);\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        for (let j = 0; j < result.length; j++) {\n          result[j] = _QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length)\n            result[j] ^= result[j + 1];\n        }\n        root = _QrCode.reedSolomonMultiply(root, 2);\n      }\n      return result;\n    }\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n    static reedSolomonComputeRemainder(data, divisor) {\n      let result = divisor.map((_) => 0);\n      for (const b of data) {\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0)\n        throw new RangeError(\"Byte out of range\");\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 285;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size;\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0)\n        currentRunLength += this.size;\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n  };\n  /*-- Constants and tables --*/\n  // The minimum version number supported in the QR Code Model 2 standard.\n  _QrCode.MIN_VERSION = 1;\n  // The maximum version number supported in the QR Code Model 2 standard.\n  _QrCode.MAX_VERSION = 40;\n  // For use in getPenaltyScore(), when evaluating which mask is best.\n  _QrCode.PENALTY_N1 = 3;\n  _QrCode.PENALTY_N2 = 3;\n  _QrCode.PENALTY_N3 = 40;\n  _QrCode.PENALTY_N4 = 10;\n  _QrCode.ECC_CODEWORDS_PER_BLOCK = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Low\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    // Medium\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Quartile\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]\n    // High\n  ];\n  _QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    // Low\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    // Medium\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    // Quartile\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]\n    // High\n  ];\n  let QrCode = _QrCode;\n  qrcodegen2.QrCode = _QrCode;\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0)\n      throw new RangeError(\"Value out of range\");\n    for (let i = len - 1; i >= 0; i--)\n      bb.push(val >>> i & 1);\n  }\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  function assert(cond) {\n    if (!cond)\n      throw new Error(\"Assertion error\");\n  }\n  const _QrSegment = class _QrSegment {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code segment with the given attributes and data.\n    // The character count (numChars) must agree with the mode and the bit buffer length,\n    // but the constraint isn't checked. The given bit buffer is cloned and stored.\n    constructor(mode, numChars, bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0)\n        throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice();\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    static makeBytes(data) {\n      let bb = [];\n      for (const b of data)\n        appendBits(b, 8, bb);\n      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);\n    }\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n    static makeNumeric(digits) {\n      if (!_QrSegment.isNumeric(digits))\n        throw new RangeError(\"String contains non-numeric characters\");\n      let bb = [];\n      for (let i = 0; i < digits.length; ) {\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static makeAlphanumeric(text) {\n      if (!_QrSegment.isAlphanumeric(text))\n        throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      let bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n    static makeSegments(text) {\n      if (text == \"\")\n        return [];\n      else if (_QrSegment.isNumeric(text))\n        return [_QrSegment.makeNumeric(text)];\n      else if (_QrSegment.isAlphanumeric(text))\n        return [_QrSegment.makeAlphanumeric(text)];\n      else\n        return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];\n    }\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n    static makeEci(assignVal) {\n      let bb = [];\n      if (assignVal < 0)\n        throw new RangeError(\"ECI assignment value out of range\");\n      else if (assignVal < 1 << 7)\n        appendBits(assignVal, 8, bb);\n      else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1e6) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else\n        throw new RangeError(\"ECI assignment value out of range\");\n      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);\n    }\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n    static isNumeric(text) {\n      return _QrSegment.NUMERIC_REGEX.test(text);\n    }\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static isAlphanumeric(text) {\n      return _QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    /*-- Methods --*/\n    // Returns a new copy of the data bits of this segment.\n    getData() {\n      return this.bitData.slice();\n    }\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits)\n          return Infinity;\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      let result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\")\n          result.push(str.charCodeAt(i));\n        else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n  };\n  /*-- Constants --*/\n  // Describes precisely all strings that are encodable in numeric mode.\n  _QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n  // Describes precisely all strings that are encodable in alphanumeric mode.\n  _QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n  // The set of all legal characters in alphanumeric mode,\n  // where each character value maps to the index in the string.\n  _QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n  let QrSegment = _QrSegment;\n  qrcodegen2.QrSegment = _QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrCode;\n  ((QrCode2) => {\n    const _Ecc = class _Ecc {\n      // The QR Code can tolerate about 30% erroneous codewords\n      /*-- Constructor and fields --*/\n      constructor(ordinal, formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    };\n    /*-- Constants --*/\n    _Ecc.LOW = new _Ecc(0, 1);\n    // The QR Code can tolerate about  7% erroneous codewords\n    _Ecc.MEDIUM = new _Ecc(1, 0);\n    // The QR Code can tolerate about 15% erroneous codewords\n    _Ecc.QUARTILE = new _Ecc(2, 3);\n    // The QR Code can tolerate about 25% erroneous codewords\n    _Ecc.HIGH = new _Ecc(3, 2);\n    let Ecc = _Ecc;\n    QrCode2.Ecc = _Ecc;\n  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrSegment;\n  ((QrSegment2) => {\n    const _Mode = class _Mode {\n      /*-- Constructor and fields --*/\n      constructor(modeBits, numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      /*-- Method --*/\n      // (Package-private) Returns the bit width of the character count field for a segment in\n      // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    };\n    /*-- Constants --*/\n    _Mode.NUMERIC = new _Mode(1, [10, 12, 14]);\n    _Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);\n    _Mode.BYTE = new _Mode(4, [8, 16, 16]);\n    _Mode.KANJI = new _Mode(8, [8, 10, 12]);\n    _Mode.ECI = new _Mode(7, [0, 0, 0]);\n    let Mode = _Mode;\n    QrSegment2.Mode = _Mode;\n  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar qrcodegen_default = qrcodegen;\n\n// src/index.tsx\n/**\n * @license qrcode.react\n * Copyright (c) Paul O'Shannessy\n * SPDX-License-Identifier: ISC\n */\nvar ERROR_LEVEL_MAP = {\n  L: qrcodegen_default.QrCode.Ecc.LOW,\n  M: qrcodegen_default.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,\n  H: qrcodegen_default.QrCode.Ecc.HIGH\n};\nvar DEFAULT_SIZE = 128;\nvar DEFAULT_LEVEL = \"L\";\nvar DEFAULT_BGCOLOR = \"#FFFFFF\";\nvar DEFAULT_FGCOLOR = \"#000000\";\nvar DEFAULT_INCLUDEMARGIN = false;\nvar DEFAULT_MINVERSION = 1;\nvar SPEC_MARGIN_SIZE = 4;\nvar DEFAULT_MARGIN_SIZE = 0;\nvar DEFAULT_IMG_SCALE = 0.1;\nfunction generatePath(modules, margin = 0) {\n  const ops = [];\n  modules.forEach(function(row, y) {\n    let start = null;\n    row.forEach(function(cell, x) {\n      if (!cell && start !== null) {\n        ops.push(\n          `M${start + margin} ${y + margin}h${x - start}v1H${start + margin}z`\n        );\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(`M${x + margin},${y + margin} h1v1H${x + margin}z`);\n        } else {\n          ops.push(\n            `M${start + margin},${y + margin} h${x + 1 - start}v1H${start + margin}z`\n          );\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join(\"\");\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map((row, y) => {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map((cell, x) => {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nfunction getImageSettings(cells, size, margin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  const numCells = cells.length + margin * 2;\n  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  const scale = numCells / size;\n  const w = (imageSettings.width || defaultSize) * scale;\n  const h = (imageSettings.height || defaultSize) * scale;\n  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  const opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;\n  let excavation = null;\n  if (imageSettings.excavate) {\n    let floorX = Math.floor(x);\n    let floorY = Math.floor(y);\n    let ceilW = Math.ceil(w + x - floorX);\n    let ceilH = Math.ceil(h + y - floorY);\n    excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };\n  }\n  const crossOrigin = imageSettings.crossOrigin;\n  return { x, y, h, w, excavation, opacity, crossOrigin };\n}\nfunction getMarginSize(includeMargin, marginSize) {\n  if (marginSize != null) {\n    return Math.max(Math.floor(marginSize), 0);\n  }\n  return includeMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;\n}\nfunction useQRCode({\n  value,\n  level,\n  minVersion,\n  includeMargin,\n  marginSize,\n  imageSettings,\n  size,\n  boostLevel\n}) {\n  let qrcode = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const values = Array.isArray(value) ? value : [value];\n    const segments = values.reduce((accum, v) => {\n      accum.push(...qrcodegen_default.QrSegment.makeSegments(v));\n      return accum;\n    }, []);\n    return qrcodegen_default.QrCode.encodeSegments(\n      segments,\n      ERROR_LEVEL_MAP[level],\n      minVersion,\n      void 0,\n      void 0,\n      boostLevel\n    );\n  }, [value, level, minVersion, boostLevel]);\n  const { cells, margin, numCells, calculatedImageSettings } = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    let cells2 = qrcode.getModules();\n    const margin2 = getMarginSize(includeMargin, marginSize);\n    const numCells2 = cells2.length + margin2 * 2;\n    const calculatedImageSettings2 = getImageSettings(\n      cells2,\n      size,\n      margin2,\n      imageSettings\n    );\n    return {\n      cells: cells2,\n      margin: margin2,\n      numCells: numCells2,\n      calculatedImageSettings: calculatedImageSettings2\n    };\n  }, [qrcode, size, imageSettings, includeMargin, marginSize]);\n  return {\n    qrcode,\n    margin,\n    cells,\n    numCells,\n    calculatedImageSettings\n  };\n}\nvar SUPPORTS_PATH2D = function() {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nvar QRCodeCanvas = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  function QRCodeCanvas2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      marginSize,\n      imageSettings\n    } = _a, extraProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const _b = extraProps, { style } = _b, otherProps = __objRest(_b, [\"style\"]);\n    const imgSrc = imageSettings == null ? void 0 : imageSettings.src;\n    const _canvas = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const _image = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const setCanvasRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (node) => {\n        _canvas.current = node;\n        if (typeof forwardedRef === \"function\") {\n          forwardedRef(node);\n        } else if (forwardedRef) {\n          forwardedRef.current = node;\n        }\n      },\n      [forwardedRef]\n    );\n    const [isImgLoaded, setIsImageLoaded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (_canvas.current != null) {\n        const canvas = _canvas.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) {\n          return;\n        }\n        let cellsToDraw = cells;\n        const image = _image.current;\n        const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;\n        if (haveImageToRender) {\n          if (calculatedImageSettings.excavation != null) {\n            cellsToDraw = excavateModules(\n              cells,\n              calculatedImageSettings.excavation\n            );\n          }\n        }\n        const pixelRatio = window.devicePixelRatio || 1;\n        canvas.height = canvas.width = size * pixelRatio;\n        const scale = size / numCells * pixelRatio;\n        ctx.scale(scale, scale);\n        ctx.fillStyle = bgColor;\n        ctx.fillRect(0, 0, numCells, numCells);\n        ctx.fillStyle = fgColor;\n        if (SUPPORTS_PATH2D) {\n          ctx.fill(new Path2D(generatePath(cellsToDraw, margin)));\n        } else {\n          cells.forEach(function(row, rdx) {\n            row.forEach(function(cell, cdx) {\n              if (cell) {\n                ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n              }\n            });\n          });\n        }\n        if (calculatedImageSettings) {\n          ctx.globalAlpha = calculatedImageSettings.opacity;\n        }\n        if (haveImageToRender) {\n          ctx.drawImage(\n            image,\n            calculatedImageSettings.x + margin,\n            calculatedImageSettings.y + margin,\n            calculatedImageSettings.w,\n            calculatedImageSettings.h\n          );\n        }\n      }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      setIsImageLoaded(false);\n    }, [imgSrc]);\n    const canvasStyle = __spreadValues({ height: size, width: size }, style);\n    let img = null;\n    if (imgSrc != null) {\n      img = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"img\",\n        {\n          src: imgSrc,\n          key: imgSrc,\n          style: { display: \"none\" },\n          onLoad: () => {\n            setIsImageLoaded(true);\n          },\n          ref: _image,\n          crossOrigin: calculatedImageSettings == null ? void 0 : calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"canvas\",\n      __spreadValues({\n        style: canvasStyle,\n        height: size,\n        width: size,\n        ref: setCanvasRef,\n        role: \"img\"\n      }, otherProps)\n    ), img);\n  }\n);\nQRCodeCanvas.displayName = \"QRCodeCanvas\";\nvar QRCodeSVG = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  function QRCodeSVG2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      title,\n      marginSize,\n      imageSettings\n    } = _a, otherProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"title\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    let cellsToDraw = cells;\n    let image = null;\n    if (imageSettings != null && calculatedImageSettings != null) {\n      if (calculatedImageSettings.excavation != null) {\n        cellsToDraw = excavateModules(\n          cells,\n          calculatedImageSettings.excavation\n        );\n      }\n      image = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"image\",\n        {\n          href: imageSettings.src,\n          height: calculatedImageSettings.h,\n          width: calculatedImageSettings.w,\n          x: calculatedImageSettings.x + margin,\n          y: calculatedImageSettings.y + margin,\n          preserveAspectRatio: \"none\",\n          opacity: calculatedImageSettings.opacity,\n          crossOrigin: calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    const fgPath = generatePath(cellsToDraw, margin);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"svg\",\n      __spreadValues({\n        height: size,\n        width: size,\n        viewBox: `0 0 ${numCells} ${numCells}`,\n        ref: forwardedRef,\n        role: \"img\"\n      }, otherProps),\n      !!title && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", null, title),\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"path\",\n        {\n          fill: bgColor,\n          d: `M0,0 h${numCells}v${numCells}H0z`,\n          shapeRendering: \"crispEdges\"\n        }\n      ),\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { fill: fgColor, d: fgPath, shapeRendering: \"crispEdges\" }),\n      image\n    );\n  }\n);\nQRCodeSVG.displayName = \"QRCodeSVG\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode.react/lib/esm/index.js\n");

/***/ })

};
;