using System;
using System.Net;
using System.Net.Mail;
using backend.Models.DTOs;
using Microsoft.Extensions.Options;

namespace backend.Service;

public class MailService : IMailService
{
    private readonly MailSettings _mailSettings;

    public MailService(IOptions<MailSettings> mailSettings)
    {
        _mailSettings = mailSettings.Value;
    }

    public async Task SendEmailAsync(string to, string subject, string body)
    {
        using (var message = new MailMessage())
        {
            message.From = new MailAddress(
                _mailSettings.SenderEmail,
                _mailSettings.SenderName
            );
            message.To.Add(to);
            message.Subject = subject;
            message.Body = body;
            message.IsBodyHtml = true;

            using (
                var smtp = new SmtpClient(
                    _mailSettings.SmtpServer,
                    _mailSettings.SmtpPort
                )
            )
            {
                smtp.EnableSsl =_mailSettings.EnableSsl;
                smtp.UseDefaultCredentials = _mailSettings.UseDefaultCredentials;
                
                await smtp.SendMailAsync(message);
            }
        }
    }
}
