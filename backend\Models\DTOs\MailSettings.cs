using System;

namespace backend.Models.DTOs;

public class MailSettings
{
    /// <summary>
    /// SMTP portu (TLS için genellikle 587, SSL için 465)
    /// </summary>
    public int SmtpPort { get; set; }

    /// <summary>
    /// Gönderen e-posta adresi
    /// </summary>
    public string SenderEmail { get; set; }

    /// <summary>
    /// Gönderen e-posta şifresi (veya uygulama şifresi)
    /// </summary>
    public string SenderPassword { get; set; }

    /// <summary>
    /// Gönderenin görünen adı (opsiyonel)
    /// </summary>
    public string SenderName { get; set; }

    public string SmtpServer { get; set; }

    public bool UseDefaultCredentials { get; set; }

    public bool EnableSsl { get; set; } 
    

}
