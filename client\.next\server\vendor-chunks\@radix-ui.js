"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxmZW5lcmJhaGNlLWZvcm1cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHByaW1pdGl2ZVxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvcHJpbWl0aXZlL3NyYy9wcmltaXRpdmUudHN4XG5mdW5jdGlvbiBjb21wb3NlRXZlbnRIYW5kbGVycyhvcmlnaW5hbEV2ZW50SGFuZGxlciwgb3VyRXZlbnRIYW5kbGVyLCB7IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgfSA9IHt9KSB7XG4gIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgIG9yaWdpbmFsRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIGlmIChjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPT09IGZhbHNlIHx8ICFldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICByZXR1cm4gb3VyRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIH1cbiAgfTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VFdmVudEhhbmRsZXJzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-arrow/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/arrow.tsx\n\n\n\nvar NAME = \"Arrow\";\nvar Arrow = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWFycm93L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDdUI7QUFDZDtBQUN4QztBQUNBLFlBQVksNkNBQWdCO0FBQzVCLFVBQVUsa0RBQWtEO0FBQzVELHlCQUF5QixzREFBRztBQUM1QixJQUFJLGdFQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsc0RBQUcsY0FBYywwQkFBMEI7QUFDdEc7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBSUU7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcZmVuZXJiYWhjZS1mb3JtXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHJhZGl4LXVpXFxyZWFjdC1hcnJvd1xcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9hcnJvdy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1wcmltaXRpdmVcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIE5BTUUgPSBcIkFycm93XCI7XG52YXIgQXJyb3cgPSBSZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIGNvbnN0IHsgY2hpbGRyZW4sIHdpZHRoID0gMTAsIGhlaWdodCA9IDUsIC4uLmFycm93UHJvcHMgfSA9IHByb3BzO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICBQcmltaXRpdmUuc3ZnLFxuICAgIHtcbiAgICAgIC4uLmFycm93UHJvcHMsXG4gICAgICByZWY6IGZvcndhcmRlZFJlZixcbiAgICAgIHdpZHRoLFxuICAgICAgaGVpZ2h0LFxuICAgICAgdmlld0JveDogXCIwIDAgMzAgMTBcIixcbiAgICAgIHByZXNlcnZlQXNwZWN0UmF0aW86IFwibm9uZVwiLFxuICAgICAgY2hpbGRyZW46IHByb3BzLmFzQ2hpbGQgPyBjaGlsZHJlbiA6IC8qIEBfX1BVUkVfXyAqLyBqc3goXCJwb2x5Z29uXCIsIHsgcG9pbnRzOiBcIjAsMCAzMCwwIDE1LDEwXCIgfSlcbiAgICB9XG4gICk7XG59KTtcbkFycm93LmRpc3BsYXlOYW1lID0gTkFNRTtcbnZhciBSb290ID0gQXJyb3c7XG5leHBvcnQge1xuICBBcnJvdyxcbiAgUm9vdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-checkbox/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   CheckboxIndicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createCheckboxScope: () => (/* binding */ createCheckboxScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Checkbox,CheckboxIndicator,Indicator,Root,createCheckboxScope auto */ // src/checkbox.tsx\n\n\n\n\n\n\n\n\n\n\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(CHECKBOX_NAME);\nvar [CheckboxProvider, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nvar Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...checkboxProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"Checkbox.useComposedRefs[composedRefs]\": (node)=>setButton(node)\n    }[\"Checkbox.useComposedRefs[composedRefs]\"]);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked ?? false,\n        onChange: onCheckedChange,\n        caller: CHECKBOX_NAME\n    });\n    const initialCheckedStateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(checked);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Checkbox.useEffect\": ()=>{\n            const form2 = button?.form;\n            if (form2) {\n                const reset = {\n                    \"Checkbox.useEffect.reset\": ()=>setChecked(initialCheckedStateRef.current)\n                }[\"Checkbox.useEffect.reset\"];\n                form2.addEventListener(\"reset\", reset);\n                return ({\n                    \"Checkbox.useEffect\": ()=>form2.removeEventListener(\"reset\", reset)\n                })[\"Checkbox.useEffect\"];\n            }\n        }\n    }[\"Checkbox.useEffect\"], [\n        button,\n        setChecked\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(CheckboxProvider, {\n        scope: __scopeCheckbox,\n        state: checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"checkbox\",\n                \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...checkboxProps,\n                ref: composedRefs,\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                    if (event.key === \"Enter\") event.preventDefault();\n                }),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>isIndeterminate(prevChecked) ? true : !prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxBubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                },\n                defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked\n            })\n        ]\n    });\n});\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || isIndeterminate(context.state) || context.state === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n            \"data-state\": getState(context.state),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"none\",\n                ...props.style\n            }\n        })\n    });\n});\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BUBBLE_INPUT_NAME = \"CheckboxBubbleInput\";\nvar CheckboxBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeCheckbox, control, checked, bubbles = true, defaultChecked, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(ref, forwardedRef);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"CheckboxBubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            if (!input) return;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n            const setChecked = descriptor.set;\n            if (prevChecked !== checked && setChecked) {\n                const event = new Event(\"click\", {\n                    bubbles\n                });\n                input.indeterminate = isIndeterminate(checked);\n                setChecked.call(input, isIndeterminate(checked) ? false : checked);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"CheckboxBubbleInput.useEffect\"], [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    const defaultCheckedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isIndeterminate(checked) ? false : checked);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.input, {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n});\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Checkbox;\nvar Indicator = CheckboxIndicator;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ // src/dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: DIALOG_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Dialog.useCallback\": ()=>setOpen({\n                    \"Dialog.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Dialog.useCallback\"])\n        }[\"Dialog.useCallback\"], [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n});\nDialogOverlay.displayName = OVERLAY_NAME;\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_10__.createSlot)(\"DialogOverlay.RemoveScroll\");\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        as: Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n});\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DialogContentModal.useEffect\": ()=>{\n            const content = contentRef.current;\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n        }\n    }[\"DialogContentModal.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            event.preventDefault();\n            context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n});\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n});\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId })=>{\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TitleWarning.useEffect\": ()=>{\n            if (titleId) {\n                const hasTitle = document.getElementById(titleId);\n                if (!hasTitle) console.error(MESSAGE);\n            }\n        }\n    }[\"TitleWarning.useEffect\"], [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId })=>{\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DescriptionWarning.useEffect\": ()=>{\n            const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n            if (descriptionId && describedById) {\n                const hasDescription = document.getElementById(descriptionId);\n                if (!hasDescription) console.warn(MESSAGE);\n            }\n        }\n    }[\"DescriptionWarning.useEffect\"], [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, {\n        \"DismissableLayer.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"DismissableLayer.useComposedRefs[composedRefs]\"]);\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside({\n        \"DismissableLayer.usePointerDownOutside[pointerDownOutside]\": (event)=>{\n            const target = event.target;\n            const isPointerDownOnBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\"]);\n            if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n            onPointerDownOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside]\"], ownerDocument);\n    const focusOutside = useFocusOutside({\n        \"DismissableLayer.useFocusOutside[focusOutside]\": (event)=>{\n            const target = event.target;\n            const isFocusInBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\"]);\n            if (isFocusInBranch) return;\n            onFocusOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.useFocusOutside[focusOutside]\"], ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)({\n        \"DismissableLayer.useEscapeKeydown\": (event)=>{\n            const isHighestLayer = index === context.layers.size - 1;\n            if (!isHighestLayer) return;\n            onEscapeKeyDown?.(event);\n            if (!event.defaultPrevented && onDismiss) {\n                event.preventDefault();\n                onDismiss();\n            }\n        }\n    }[\"DismissableLayer.useEscapeKeydown\"], ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            if (!node) return;\n            if (disableOutsidePointerEvents) {\n                if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                    originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                    ownerDocument.body.style.pointerEvents = \"none\";\n                }\n                context.layersWithOutsidePointerEventsDisabled.add(node);\n            }\n            context.layers.add(node);\n            dispatchUpdate();\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                        ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n                    }\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (!node) return;\n                    context.layers.delete(node);\n                    context.layersWithOutsidePointerEventsDisabled.delete(node);\n                    dispatchUpdate();\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            const handleUpdate = {\n                \"DismissableLayer.useEffect.handleUpdate\": ()=>force({})\n            }[\"DismissableLayer.useEffect.handleUpdate\"];\n            document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n            return ({\n                \"DismissableLayer.useEffect\": ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate)\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayerBranch.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                context.branches.add(node);\n                return ({\n                    \"DismissableLayerBranch.useEffect\": ()=>{\n                        context.branches.delete(node);\n                    }\n                })[\"DismissableLayerBranch.useEffect\"];\n            }\n        }\n    }[\"DismissableLayerBranch.useEffect\"], [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        \"usePointerDownOutside.useRef[handleClickRef]\": ()=>{}\n    }[\"usePointerDownOutside.useRef[handleClickRef]\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePointerDownOutside.useEffect\": ()=>{\n            const handlePointerDown = {\n                \"usePointerDownOutside.useEffect.handlePointerDown\": (event)=>{\n                    if (event.target && !isPointerInsideReactTreeRef.current) {\n                        let handleAndDispatchPointerDownOutsideEvent2 = {\n                            \"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\": function() {\n                                handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                                    discrete: true\n                                });\n                            }\n                        }[\"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\"];\n                        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        if (event.pointerType === \"touch\") {\n                            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                            handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                            ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                                once: true\n                            });\n                        } else {\n                            handleAndDispatchPointerDownOutsideEvent2();\n                        }\n                    } else {\n                        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    }\n                    isPointerInsideReactTreeRef.current = false;\n                }\n            }[\"usePointerDownOutside.useEffect.handlePointerDown\"];\n            const timerId = window.setTimeout({\n                \"usePointerDownOutside.useEffect.timerId\": ()=>{\n                    ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n                }\n            }[\"usePointerDownOutside.useEffect.timerId\"], 0);\n            return ({\n                \"usePointerDownOutside.useEffect\": ()=>{\n                    window.clearTimeout(timerId);\n                    ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                }\n            })[\"usePointerDownOutside.useEffect\"];\n        }\n    }[\"usePointerDownOutside.useEffect\"], [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusOutside.useEffect\": ()=>{\n            const handleFocus = {\n                \"useFocusOutside.useEffect.handleFocus\": (event)=>{\n                    if (event.target && !isFocusInsideReactTreeRef.current) {\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                            discrete: false\n                        });\n                    }\n                }\n            }[\"useFocusOutside.useEffect.handleFocus\"];\n            ownerDocument.addEventListener(\"focusin\", handleFocus);\n            return ({\n                \"useFocusOutside.useEffect\": ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus)\n            })[\"useFocusOutside.useEffect\"];\n        }\n    }[\"useFocusOutside.useEffect\"], [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/focus-guards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusGuards.useEffect\": ()=>{\n            const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n            document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n            document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n            count++;\n            return ({\n                \"useFocusGuards.useEffect\": ()=>{\n                    if (count === 1) {\n                        document.querySelectorAll(\"[data-radix-focus-guard]\").forEach({\n                            \"useFocusGuards.useEffect\": (node)=>node.remove()\n                        }[\"useFocusGuards.useEffect\"]);\n                    }\n                    count--;\n                }\n            })[\"useFocusGuards.useEffect\"];\n        }\n    }[\"useFocusGuards.useEffect\"], []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // src/focus-scope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"FocusScope.useComposedRefs[composedRefs]\": (node)=>setContainer(node)\n    }[\"FocusScope.useComposedRefs[composedRefs]\"]);\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (trapped) {\n                let handleFocusIn2 = {\n                    \"FocusScope.useEffect.handleFocusIn2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const target = event.target;\n                        if (container.contains(target)) {\n                            lastFocusedElementRef.current = target;\n                        } else {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusIn2\"], handleFocusOut2 = {\n                    \"FocusScope.useEffect.handleFocusOut2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const relatedTarget = event.relatedTarget;\n                        if (relatedTarget === null) return;\n                        if (!container.contains(relatedTarget)) {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusOut2\"], handleMutations2 = {\n                    \"FocusScope.useEffect.handleMutations2\": function(mutations) {\n                        const focusedElement = document.activeElement;\n                        if (focusedElement !== document.body) return;\n                        for (const mutation of mutations){\n                            if (mutation.removedNodes.length > 0) focus(container);\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleMutations2\"];\n                var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n                document.addEventListener(\"focusin\", handleFocusIn2);\n                document.addEventListener(\"focusout\", handleFocusOut2);\n                const mutationObserver = new MutationObserver(handleMutations2);\n                if (container) mutationObserver.observe(container, {\n                    childList: true,\n                    subtree: true\n                });\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        document.removeEventListener(\"focusin\", handleFocusIn2);\n                        document.removeEventListener(\"focusout\", handleFocusOut2);\n                        mutationObserver.disconnect();\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (container) {\n                focusScopesStack.add(focusScope);\n                const previouslyFocusedElement = document.activeElement;\n                const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n                if (!hasFocusedCandidate) {\n                    const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                    container.dispatchEvent(mountEvent);\n                    if (!mountEvent.defaultPrevented) {\n                        focusFirst(removeLinks(getTabbableCandidates(container)), {\n                            select: true\n                        });\n                        if (document.activeElement === previouslyFocusedElement) {\n                            focus(container);\n                        }\n                    }\n                }\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                        setTimeout({\n                            \"FocusScope.useEffect\": ()=>{\n                                const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                                container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                container.dispatchEvent(unmountEvent);\n                                if (!unmountEvent.defaultPrevented) {\n                                    focus(previouslyFocusedElement ?? document.body, {\n                                        select: true\n                                    });\n                                }\n                                container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                focusScopesStack.remove(focusScope);\n                            }\n                        }[\"FocusScope.useEffect\"], 0);\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"FocusScope.useCallback[handleKeyDown]\": (event)=>{\n            if (!loop && !trapped) return;\n            if (focusScope.paused) return;\n            const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n            const focusedElement = document.activeElement;\n            if (isTabKey && focusedElement) {\n                const container2 = event.currentTarget;\n                const [first, last] = getTabbableEdges(container2);\n                const hasTabbableElementsInside = first && last;\n                if (!hasTabbableElementsInside) {\n                    if (focusedElement === container2) event.preventDefault();\n                } else {\n                    if (!event.shiftKey && focusedElement === last) {\n                        event.preventDefault();\n                        if (loop) focus(first, {\n                            select: true\n                        });\n                    } else if (event.shiftKey && focusedElement === first) {\n                        event.preventDefault();\n                        if (loop) focus(last, {\n                            select: true\n                        });\n                    }\n                }\n            }\n        }\n    }[\"FocusScope.useCallback[handleKeyDown]\"], [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcZmVuZXJiYWhjZS1mb3JtXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHJhZGl4LXVpXFxyZWFjdC1pZFxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2lkL3NyYy9pZC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdFwiO1xudmFyIHVzZVJlYWN0SWQgPSBSZWFjdFtcIiB1c2VJZCBcIi50cmltKCkudG9TdHJpbmcoKV0gfHwgKCgpID0+IHZvaWQgMCk7XG52YXIgY291bnQgPSAwO1xuZnVuY3Rpb24gdXNlSWQoZGV0ZXJtaW5pc3RpY0lkKSB7XG4gIGNvbnN0IFtpZCwgc2V0SWRdID0gUmVhY3QudXNlU3RhdGUodXNlUmVhY3RJZCgpKTtcbiAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWRldGVybWluaXN0aWNJZCkgc2V0SWQoKHJlYWN0SWQpID0+IHJlYWN0SWQgPz8gU3RyaW5nKGNvdW50KyspKTtcbiAgfSwgW2RldGVybWluaXN0aWNJZF0pO1xuICByZXR1cm4gZGV0ZXJtaW5pc3RpY0lkIHx8IChpZCA/IGByYWRpeC0ke2lkfWAgOiBcIlwiKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUlkXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // src/label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            props.onMouseDown?.(event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-popover/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-popover/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Popover: () => (/* binding */ Popover),\n/* harmony export */   PopoverAnchor: () => (/* binding */ PopoverAnchor),\n/* harmony export */   PopoverArrow: () => (/* binding */ PopoverArrow),\n/* harmony export */   PopoverClose: () => (/* binding */ PopoverClose),\n/* harmony export */   PopoverContent: () => (/* binding */ PopoverContent),\n/* harmony export */   PopoverPortal: () => (/* binding */ PopoverPortal),\n/* harmony export */   PopoverTrigger: () => (/* binding */ PopoverTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createPopoverScope: () => (/* binding */ createPopoverScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,Close,Content,Popover,PopoverAnchor,PopoverArrow,PopoverClose,PopoverContent,PopoverPortal,PopoverTrigger,Portal,Root,Trigger,createPopoverScope auto */ // src/popover.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar POPOVER_NAME = \"Popover\";\nvar [createPopoverContext, createPopoverScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPOVER_NAME, [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar [PopoverProvider, usePopoverContext] = createPopoverContext(POPOVER_NAME);\nvar Popover = (props)=>{\n    const { __scopePopover, children, open: openProp, defaultOpen, onOpenChange, modal = false } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [hasCustomAnchor, setHasCustomAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: POPOVER_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverProvider, {\n            scope: __scopePopover,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n            triggerRef,\n            open,\n            onOpenChange: setOpen,\n            onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Popover.useCallback\": ()=>setOpen({\n                        \"Popover.useCallback\": (prevOpen)=>!prevOpen\n                    }[\"Popover.useCallback\"])\n            }[\"Popover.useCallback\"], [\n                setOpen\n            ]),\n            hasCustomAnchor,\n            onCustomAnchorAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Popover.useCallback\": ()=>setHasCustomAnchor(true)\n            }[\"Popover.useCallback\"], []),\n            onCustomAnchorRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Popover.useCallback\": ()=>setHasCustomAnchor(false)\n            }[\"Popover.useCallback\"], []),\n            modal,\n            children\n        })\n    });\n};\nPopover.displayName = POPOVER_NAME;\nvar ANCHOR_NAME = \"PopoverAnchor\";\nvar PopoverAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...anchorProps } = props;\n    const context = usePopoverContext(ANCHOR_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const { onCustomAnchorAdd, onCustomAnchorRemove } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"PopoverAnchor.useEffect\": ()=>{\n            onCustomAnchorAdd();\n            return ({\n                \"PopoverAnchor.useEffect\": ()=>onCustomAnchorRemove()\n            })[\"PopoverAnchor.useEffect\"];\n        }\n    }[\"PopoverAnchor.useEffect\"], [\n        onCustomAnchorAdd,\n        onCustomAnchorRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nPopoverAnchor.displayName = ANCHOR_NAME;\nvar TRIGGER_NAME = \"PopoverTrigger\";\nvar PopoverTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...triggerProps } = props;\n    const context = usePopoverContext(TRIGGER_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, context.triggerRef);\n    const trigger = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n    return context.hasCustomAnchor ? trigger : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: trigger\n    });\n});\nPopoverTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"PopoverPortal\";\nvar [PortalProvider, usePortalContext] = createPopoverContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar PopoverPortal = (props)=>{\n    const { __scopePopover, forceMount, children, container } = props;\n    const context = usePopoverContext(PORTAL_NAME, __scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopePopover,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nPopoverPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"PopoverContent\";\nvar PopoverContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopePopover);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nPopoverContent.displayName = CONTENT_NAME;\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.createSlot)(\"PopoverContent.RemoveScroll\");\nvar PopoverContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, contentRef);\n    const isRightClickOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"PopoverContentModal.useEffect\": ()=>{\n            const content = contentRef.current;\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n        }\n    }[\"PopoverContentModal.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        as: Slot,\n        allowPinchZoom: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentImpl, {\n            ...props,\n            ref: composedRefs,\n            trapFocus: context.open,\n            disableOutsidePointerEvents: true,\n            onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n                event.preventDefault();\n                if (!isRightClickOutsideRef.current) context.triggerRef.current?.focus();\n            }),\n            onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n                const originalEvent = event.detail.originalEvent;\n                const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n                const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n                isRightClickOutsideRef.current = isRightClick;\n            }, {\n                checkForDefaultPrevented: false\n            }),\n            onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nvar PopoverContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar PopoverContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_14__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_15__.FocusScope, {\n        asChild: true,\n        loop: true,\n        trapped: trapFocus,\n        onMountAutoFocus: onOpenAutoFocus,\n        onUnmountAutoFocus: onCloseAutoFocus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__.DismissableLayer, {\n            asChild: true,\n            disableOutsidePointerEvents,\n            onInteractOutside,\n            onEscapeKeyDown,\n            onPointerDownOutside,\n            onFocusOutside,\n            onDismiss: ()=>context.onOpenChange(false),\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                \"data-state\": getState(context.open),\n                role: \"dialog\",\n                id: context.contentId,\n                ...popperScope,\n                ...contentProps,\n                ref: forwardedRef,\n                style: {\n                    ...contentProps.style,\n                    // re-namespace exposed content custom properties\n                    ...{\n                        \"--radix-popover-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                        \"--radix-popover-content-available-width\": \"var(--radix-popper-available-width)\",\n                        \"--radix-popover-content-available-height\": \"var(--radix-popper-available-height)\",\n                        \"--radix-popover-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                        \"--radix-popover-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                    }\n                }\n            })\n        })\n    });\n});\nvar CLOSE_NAME = \"PopoverClose\";\nvar PopoverClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...closeProps } = props;\n    const context = usePopoverContext(CLOSE_NAME, __scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nPopoverClose.displayName = CLOSE_NAME;\nvar ARROW_NAME = \"PopoverArrow\";\nvar PopoverArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nPopoverArrow.displayName = ARROW_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root2 = Popover;\nvar Anchor2 = PopoverAnchor;\nvar Trigger = PopoverTrigger;\nvar Portal = PopoverPortal;\nvar Content2 = PopoverContent;\nvar Close = PopoverClose;\nvar Arrow2 = PopoverArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-popover/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"PopperAnchor.useEffect\": ()=>{\n            context.onAnchorChange(virtualRef?.current || ref.current);\n        }\n    }[\"PopperAnchor.useEffect\"]);\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"PopperContent.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"PopperContent.useComposedRefs[composedRefs]\"]);\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: {\n            \"PopperContent.useFloating\": (...args)=>{\n                const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                    animationFrame: updatePositionStrategy === \"always\"\n                });\n                return cleanup;\n            }\n        }[\"PopperContent.useFloating\"],\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: {\n                    \"PopperContent.useFloating\": ({ elements, rects, availableWidth, availableHeight })=>{\n                        const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                        const contentStyle = elements.floating.style;\n                        contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                    }\n                }[\"PopperContent.useFloating\"]\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (isPositioned) {\n                handlePlaced?.();\n            }\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)({\n        \"Portal.useLayoutEffect\": ()=>setMounted(true)\n    }[\"Portal.useLayoutEffect\"], []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePresence.useEffect\": ()=>{\n            const currentAnimationName = getAnimationName(stylesRef.current);\n            prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n        }\n    }[\"usePresence.useEffect\"], [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            const styles = stylesRef.current;\n            const wasPresent = prevPresentRef.current;\n            const hasPresentChanged = wasPresent !== present;\n            if (hasPresentChanged) {\n                const prevAnimationName = prevAnimationNameRef.current;\n                const currentAnimationName = getAnimationName(styles);\n                if (present) {\n                    send(\"MOUNT\");\n                } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                    send(\"UNMOUNT\");\n                } else {\n                    const isAnimating = prevAnimationName !== currentAnimationName;\n                    if (wasPresent && isAnimating) {\n                        send(\"ANIMATION_OUT\");\n                    } else {\n                        send(\"UNMOUNT\");\n                    }\n                }\n                prevPresentRef.current = present;\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            if (node) {\n                let timeoutId;\n                const ownerWindow = node.ownerDocument.defaultView ?? window;\n                const handleAnimationEnd = {\n                    \"usePresence.useLayoutEffect.handleAnimationEnd\": (event)=>{\n                        const currentAnimationName = getAnimationName(stylesRef.current);\n                        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                        if (event.target === node && isCurrentAnimation) {\n                            send(\"ANIMATION_END\");\n                            if (!prevPresentRef.current) {\n                                const currentFillMode = node.style.animationFillMode;\n                                node.style.animationFillMode = \"forwards\";\n                                timeoutId = ownerWindow.setTimeout({\n                                    \"usePresence.useLayoutEffect.handleAnimationEnd\": ()=>{\n                                        if (node.style.animationFillMode === \"forwards\") {\n                                            node.style.animationFillMode = currentFillMode;\n                                        }\n                                    }\n                                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"]);\n                            }\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"];\n                const handleAnimationStart = {\n                    \"usePresence.useLayoutEffect.handleAnimationStart\": (event)=>{\n                        if (event.target === node) {\n                            prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationStart\"];\n                node.addEventListener(\"animationstart\", handleAnimationStart);\n                node.addEventListener(\"animationcancel\", handleAnimationEnd);\n                node.addEventListener(\"animationend\", handleAnimationEnd);\n                return ({\n                    \"usePresence.useLayoutEffect\": ()=>{\n                        ownerWindow.clearTimeout(timeoutId);\n                        node.removeEventListener(\"animationstart\", handleAnimationStart);\n                        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                        node.removeEventListener(\"animationend\", handleAnimationEnd);\n                    }\n                })[\"usePresence.useLayoutEffect\"];\n            } else {\n                send(\"ANIMATION_END\");\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"usePresence.useCallback\": (node2)=>{\n                stylesRef.current = node2 ? getComputedStyle(node2) : null;\n                setNode(node2);\n            }\n        }[\"usePresence.useCallback\"], [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/slot/src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxmZW5lcmJhaGNlLWZvcm1cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1jYWxsYmFjay1yZWZcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2UtY2FsbGJhY2stcmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n      ref.current = callback;\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => ref.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lZmZlY3QtZXZlbnQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ29FO0FBQ3JDO0FBQy9CLDBCQUEwQix5TEFBSztBQUMvQiw4QkFBOEIseUxBQUs7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLHlDQUFZO0FBQzFCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJO0FBQ0osSUFBSSxrRkFBZTtBQUNuQjtBQUNBLEtBQUs7QUFDTDtBQUNBLFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxmZW5lcmJhaGNlLWZvcm1cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1lZmZlY3QtZXZlbnRcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXNlLWVmZmVjdC1ldmVudC50c3hcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZVJlYWN0RWZmZWN0RXZlbnQgPSBSZWFjdFtcIiB1c2VFZmZlY3RFdmVudCBcIi50cmltKCkudG9TdHJpbmcoKV07XG52YXIgdXNlUmVhY3RJbnNlcnRpb25FZmZlY3QgPSBSZWFjdFtcIiB1c2VJbnNlcnRpb25FZmZlY3QgXCIudHJpbSgpLnRvU3RyaW5nKCldO1xuZnVuY3Rpb24gdXNlRWZmZWN0RXZlbnQoY2FsbGJhY2spIHtcbiAgaWYgKHR5cGVvZiB1c2VSZWFjdEVmZmVjdEV2ZW50ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICByZXR1cm4gdXNlUmVhY3RFZmZlY3RFdmVudChjYWxsYmFjayk7XG4gIH1cbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKCgpID0+IHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgY2FsbCBhbiBldmVudCBoYW5kbGVyIHdoaWxlIHJlbmRlcmluZy5cIik7XG4gIH0pO1xuICBpZiAodHlwZW9mIHVzZVJlYWN0SW5zZXJ0aW9uRWZmZWN0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB1c2VSZWFjdEluc2VydGlvbkVmZmVjdCgoKSA9PiB7XG4gICAgICByZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICAgIH0pO1xuICB9IGVsc2Uge1xuICAgIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICByZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICAgIH0pO1xuICB9XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiByZWYuY3VycmVudD8uKC4uLmFyZ3MpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VFZmZlY3RFdmVudFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxmZW5lcmJhaGNlLWZvcm1cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1lc2NhcGUta2V5ZG93blxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1lc2NhcGUta2V5ZG93bi9zcmMvdXNlLWVzY2FwZS1rZXlkb3duLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZlwiO1xuZnVuY3Rpb24gdXNlRXNjYXBlS2V5ZG93bihvbkVzY2FwZUtleURvd25Qcm9wLCBvd25lckRvY3VtZW50ID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQpIHtcbiAgY29uc3Qgb25Fc2NhcGVLZXlEb3duID0gdXNlQ2FsbGJhY2tSZWYob25Fc2NhcGVLZXlEb3duUHJvcCk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LmtleSA9PT0gXCJFc2NhcGVcIikge1xuICAgICAgICBvbkVzY2FwZUtleURvd24oZXZlbnQpO1xuICAgICAgfVxuICAgIH07XG4gICAgb3duZXJEb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gICAgcmV0dXJuICgpID0+IG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICB9LCBbb25Fc2NhcGVLZXlEb3duLCBvd25lckRvY3VtZW50XSk7XG59XG5leHBvcnQge1xuICB1c2VFc2NhcGVLZXlkb3duXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsOENBQThDLGtEQUFxQjtBQUNuRTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGZlbmVyYmFoY2UtZm9ybVxcY2xpZW50XFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdXNlLWxheW91dC1lZmZlY3RcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtbGF5b3V0LWVmZmVjdC9zcmMvdXNlLWxheW91dC1lZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/use-previous.tsx\n\nfunction usePrevious(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({ value, previous: value });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQytCO0FBQy9CO0FBQ0EsY0FBYyx5Q0FBWSxHQUFHLHdCQUF3QjtBQUNyRCxTQUFTLDBDQUFhO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxmZW5lcmJhaGNlLWZvcm1cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1wcmV2aW91c1xcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1wcmV2aW91cy9zcmMvdXNlLXByZXZpb3VzLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VQcmV2aW91cyh2YWx1ZSkge1xuICBjb25zdCByZWYgPSBSZWFjdC51c2VSZWYoeyB2YWx1ZSwgcHJldmlvdXM6IHZhbHVlIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgaWYgKHJlZi5jdXJyZW50LnZhbHVlICE9PSB2YWx1ZSkge1xuICAgICAgcmVmLmN1cnJlbnQucHJldmlvdXMgPSByZWYuY3VycmVudC52YWx1ZTtcbiAgICAgIHJlZi5jdXJyZW50LnZhbHVlID0gdmFsdWU7XG4gICAgfVxuICAgIHJldHVybiByZWYuY3VycmVudC5wcmV2aW91cztcbiAgfSwgW3ZhbHVlXSk7XG59XG5leHBvcnQge1xuICB1c2VQcmV2aW91c1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/use-size.tsx\n\n\nfunction useSize(element) {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ })

};
;