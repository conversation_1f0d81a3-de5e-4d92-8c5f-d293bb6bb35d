"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ function __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsDocumentHidden.useEffect\": ()=>{\n            const callback = {\n                \"useIsDocumentHidden.useEffect.callback\": ()=>{\n                    setIsDocumentHidden(document.hidden);\n                }\n            }[\"useIsDocumentHidden.useEffect.callback\"];\n            document.addEventListener('visibilitychange', callback);\n            return ({\n                \"useIsDocumentHidden.useEffect\": ()=>window.removeEventListener('visibilitychange', callback)\n            })[\"useIsDocumentHidden.useEffect\"];\n        }\n    }[\"useIsDocumentHidden.useEffect\"], []);\n    return isDocumentHidden;\n};\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}[data-sonner-toaster][data-lifted=true]{transform:translateY(-8px)}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[heightIndex]\": ()=>heights.findIndex({\n                \"Toast.useMemo[heightIndex]\": (height)=>height.toastId === toast.id\n            }[\"Toast.useMemo[heightIndex]\"]) || 0\n    }[\"Toast.useMemo[heightIndex]\"], [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[closeButton]\": ()=>{\n            var _toast_closeButton;\n            return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n        }\n    }[\"Toast.useMemo[closeButton]\"], [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[duration]\": ()=>toast.duration || durationFromToaster || TOAST_LIFETIME\n    }[\"Toast.useMemo[duration]\"], [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[toastsHeightBefore]\": ()=>{\n            return heights.reduce({\n                \"Toast.useMemo[toastsHeightBefore]\": (prev, curr, reducerIndex)=>{\n                    // Calculate offset up until current toast\n                    if (reducerIndex >= heightIndex) {\n                        return prev;\n                    }\n                    return prev + curr.height;\n                }\n            }[\"Toast.useMemo[toastsHeightBefore]\"], 0);\n        }\n    }[\"Toast.useMemo[toastsHeightBefore]\"], [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo\": ()=>heightIndex * gap + toastsHeightBefore\n    }[\"Toast.useMemo\"], [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            remainingTime.current = duration;\n        }\n    }[\"Toast.useEffect\"], [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            setMounted(true);\n        }\n    }[\"Toast.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            const toastNode = toastRef.current;\n            if (toastNode) {\n                const height = toastNode.getBoundingClientRect().height;\n                // Add toast height to heights array after the toast is mounted\n                setInitialHeight(height);\n                setHeights({\n                    \"Toast.useEffect\": (h)=>[\n                            {\n                                toastId: toast.id,\n                                height,\n                                position: toast.position\n                            },\n                            ...h\n                        ]\n                }[\"Toast.useEffect\"]);\n                return ({\n                    \"Toast.useEffect\": ()=>setHeights({\n                            \"Toast.useEffect\": (h)=>h.filter({\n                                    \"Toast.useEffect\": (height)=>height.toastId !== toast.id\n                                }[\"Toast.useEffect\"])\n                        }[\"Toast.useEffect\"])\n                })[\"Toast.useEffect\"];\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"Toast.useLayoutEffect\": ()=>{\n            if (!mounted) return;\n            const toastNode = toastRef.current;\n            const originalHeight = toastNode.style.height;\n            toastNode.style.height = 'auto';\n            const newHeight = toastNode.getBoundingClientRect().height;\n            toastNode.style.height = originalHeight;\n            setInitialHeight(newHeight);\n            setHeights({\n                \"Toast.useLayoutEffect\": (heights)=>{\n                    const alreadyExists = heights.find({\n                        \"Toast.useLayoutEffect.alreadyExists\": (height)=>height.toastId === toast.id\n                    }[\"Toast.useLayoutEffect.alreadyExists\"]);\n                    if (!alreadyExists) {\n                        return [\n                            {\n                                toastId: toast.id,\n                                height: newHeight,\n                                position: toast.position\n                            },\n                            ...heights\n                        ];\n                    } else {\n                        return heights.map({\n                            \"Toast.useLayoutEffect\": (height)=>height.toastId === toast.id ? {\n                                    ...height,\n                                    height: newHeight\n                                } : height\n                        }[\"Toast.useLayoutEffect\"]);\n                    }\n                }\n            }[\"Toast.useLayoutEffect\"]);\n        }\n    }[\"Toast.useLayoutEffect\"], [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toast.useCallback[deleteToast]\": ()=>{\n            // Save the offset for the exit swipe animation\n            setRemoved(true);\n            setOffsetBeforeRemove(offset.current);\n            setHeights({\n                \"Toast.useCallback[deleteToast]\": (h)=>h.filter({\n                        \"Toast.useCallback[deleteToast]\": (height)=>height.toastId !== toast.id\n                    }[\"Toast.useCallback[deleteToast]\"])\n            }[\"Toast.useCallback[deleteToast]\"]);\n            setTimeout({\n                \"Toast.useCallback[deleteToast]\": ()=>{\n                    removeToast(toast);\n                }\n            }[\"Toast.useCallback[deleteToast]\"], TIME_BEFORE_UNMOUNT);\n        }\n    }[\"Toast.useCallback[deleteToast]\"], [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n            let timeoutId;\n            // Pause the timer on each hover\n            const pauseTimer = {\n                \"Toast.useEffect.pauseTimer\": ()=>{\n                    if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                        // Get the elapsed time since the timer started\n                        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                        remainingTime.current = remainingTime.current - elapsedTime;\n                    }\n                    lastCloseTimerStartTimeRef.current = new Date().getTime();\n                }\n            }[\"Toast.useEffect.pauseTimer\"];\n            const startTimer = {\n                \"Toast.useEffect.startTimer\": ()=>{\n                    // setTimeout(, Infinity) behaves as if the delay is 0.\n                    // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n                    // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n                    if (remainingTime.current === Infinity) return;\n                    closeTimerStartTimeRef.current = new Date().getTime();\n                    // Let the toast know it has started\n                    timeoutId = setTimeout({\n                        \"Toast.useEffect.startTimer\": ()=>{\n                            toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                            deleteToast();\n                        }\n                    }[\"Toast.useEffect.startTimer\"], remainingTime.current);\n                }\n            }[\"Toast.useEffect.startTimer\"];\n            if (expanded || interacting || isDocumentHidden) {\n                pauseTimer();\n            } else {\n                startTimer();\n            }\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.delete) {\n                deleteToast();\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n            '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (true) return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSonner.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"useSonner.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        setTimeout({\n                            \"useSonner.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"useSonner.useEffect\": ()=>{\n                                        setActiveToasts({\n                                            \"useSonner.useEffect\": (toasts)=>toasts.filter({\n                                                    \"useSonner.useEffect\": (t)=>t.id !== toast.id\n                                                }[\"useSonner.useEffect\"])\n                                        }[\"useSonner.useEffect\"]);\n                                    }\n                                }[\"useSonner.useEffect\"]);\n                            }\n                        }[\"useSonner.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"useSonner.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"useSonner.useEffect\": ()=>{\n                                    setActiveToasts({\n                                        \"useSonner.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"useSonner.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"useSonner.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"useSonner.useEffect\"]);\n                                }\n                            }[\"useSonner.useEffect\"]);\n                        }\n                    }[\"useSonner.useEffect\"]);\n                }\n            }[\"useSonner.useEffect\"]);\n        }\n    }[\"useSonner.useEffect\"], []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Toaster(props, ref) {\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[possiblePositions]\": ()=>{\n            return Array.from(new Set([\n                position\n            ].concat(toasts.filter({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]).map({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]))));\n        }\n    }[\"Toaster.Toaster.useMemo[possiblePositions]\"], [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== 'system' ? theme :  false ? 0 : 'light');\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toaster.Toaster.useCallback[removeToast]\": (toastToRemove)=>{\n            setToasts({\n                \"Toaster.Toaster.useCallback[removeToast]\": (toasts)=>{\n                    var _toasts_find;\n                    if (!((_toasts_find = toasts.find({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (toast)=>toast.id === toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"])) == null ? void 0 : _toasts_find.delete)) {\n                        ToastState.dismiss(toastToRemove.id);\n                    }\n                    return toasts.filter({\n                        \"Toaster.Toaster.useCallback[removeToast]\": ({ id })=>id !== toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n                }\n            }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n        }\n    }[\"Toaster.Toaster.useCallback[removeToast]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"Toaster.Toaster.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        // Prevent batching of other state updates\n                        requestAnimationFrame({\n                            \"Toaster.Toaster.useEffect\": ()=>{\n                                setToasts({\n                                    \"Toaster.Toaster.useEffect\": (toasts)=>toasts.map({\n                                            \"Toaster.Toaster.useEffect\": (t)=>t.id === toast.id ? {\n                                                    ...t,\n                                                    delete: true\n                                                } : t\n                                        }[\"Toaster.Toaster.useEffect\"])\n                                }[\"Toaster.Toaster.useEffect\"]);\n                            }\n                        }[\"Toaster.Toaster.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"Toaster.Toaster.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Toaster.Toaster.useEffect\": ()=>{\n                                    setToasts({\n                                        \"Toaster.Toaster.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"Toaster.Toaster.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"Toaster.Toaster.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"Toaster.Toaster.useEffect\"]);\n                                }\n                            }[\"Toaster.Toaster.useEffect\"]);\n                        }\n                    }[\"Toaster.Toaster.useEffect\"]);\n                }\n            }[\"Toaster.Toaster.useEffect\"]);\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (theme !== 'system') {\n                setActualTheme(theme);\n                return;\n            }\n            if (theme === 'system') {\n                // check if current preference is dark\n                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    // it's currently dark\n                    setActualTheme('dark');\n                } else {\n                    // it's not dark\n                    setActualTheme('light');\n                }\n            }\n            if (true) return;\n            const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            try {\n                // Chrome & Firefox\n                darkMediaQuery.addEventListener('change', {\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        if (matches) {\n                            setActualTheme('dark');\n                        } else {\n                            setActualTheme('light');\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            } catch (error) {\n                // Safari < 14\n                darkMediaQuery.addListener({\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        try {\n                            if (matches) {\n                                setActualTheme('dark');\n                            } else {\n                                setActualTheme('light');\n                            }\n                        } catch (e) {\n                            console.error(e);\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            // Ensure expanded is always false when no toasts are present / only one left\n            if (toasts.length <= 1) {\n                setExpanded(false);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Toaster.Toaster.useEffect.handleKeyDown\": (event)=>{\n                    var _listRef_current;\n                    const isHotkeyPressed = hotkey.every({\n                        \"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\": (key)=>event[key] || event.code === key\n                    }[\"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\"]);\n                    if (isHotkeyPressed) {\n                        var _listRef_current1;\n                        setExpanded(true);\n                        (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n                    }\n                    if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                        setExpanded(false);\n                    }\n                }\n            }[\"Toaster.Toaster.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Toaster.Toaster.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Toaster.Toaster.useEffect\"];\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (listRef.current) {\n                return ({\n                    \"Toaster.Toaster.useEffect\": ()=>{\n                        if (lastFocusedElementRef.current) {\n                            lastFocusedElementRef.current.focus({\n                                preventScroll: true\n                            });\n                            lastFocusedElementRef.current = null;\n                            isFocusWithinRef.current = false;\n                        }\n                    }\n                })[\"Toaster.Toaster.useEffect\"];\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-lifted\": expanded && toasts.length > 1 && !expand,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ubmVyL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZFQUNBLFNBQVNBLFlBQVlDLElBQUk7SUFDdkIsSUFBSSxDQUFDQSxRQUFRLE9BQU9DLFlBQVksYUFBYTtJQUM3QyxJQUFJQyxPQUFPRCxTQUFTQyxJQUFJLElBQUlELFNBQVNFLG9CQUFvQixDQUFDLE9BQU8sQ0FBQyxFQUFFO0lBQ3BFLElBQUlDLFFBQVFILFNBQVNJLGFBQWEsQ0FBQztJQUNuQ0QsTUFBTUUsSUFBSSxHQUFHO0lBQ2JKLEtBQUtLLFdBQVcsQ0FBQ0g7SUFDaEJBLE1BQU1JLFVBQVUsR0FBSUosTUFBTUksVUFBVSxDQUFDQyxPQUFPLEdBQUdULE9BQVFJLE1BQU1HLFdBQVcsQ0FBQ04sU0FBU1MsY0FBYyxDQUFDVjtBQUNwRztBQUUwQjtBQUNPO0FBRWpDLE1BQU1hLFdBQVcsQ0FBQ1A7SUFDZCxPQUFPQTtRQUNILEtBQUs7WUFDRCxPQUFPUTtRQUNYLEtBQUs7WUFDRCxPQUFPQztRQUNYLEtBQUs7WUFDRCxPQUFPQztRQUNYLEtBQUs7WUFDRCxPQUFPQztRQUNYO1lBQ0ksT0FBTztJQUNmO0FBQ0o7QUFDQSxNQUFNQyxPQUFPQyxNQUFNLElBQUlDLElBQUksQ0FBQztBQUM1QixNQUFNQyxTQUFTLENBQUMsRUFBRUMsT0FBTyxFQUFFQyxTQUFTLEVBQUU7SUFDbEMsT0FBTyxXQUFXLEdBQUdaLGdEQUFtQixDQUFDLE9BQU87UUFDNUNZLFdBQVc7WUFDUDtZQUNBQTtTQUNILENBQUNDLE1BQU0sQ0FBQ0MsU0FBU0MsSUFBSSxDQUFDO1FBQ3ZCLGdCQUFnQko7SUFDcEIsR0FBRyxXQUFXLEdBQUdYLGdEQUFtQixDQUFDLE9BQU87UUFDeENZLFdBQVc7SUFDZixHQUFHTCxLQUFLUyxHQUFHLENBQUMsQ0FBQ0MsR0FBR0MsSUFBSSxXQUFXLEdBQUdsQixnREFBbUIsQ0FBQyxPQUFPO1lBQ3JEWSxXQUFXO1lBQ1hPLEtBQUssQ0FBQyxZQUFZLEVBQUVELEdBQUc7UUFDM0I7QUFDUjtBQUNBLE1BQU1mLGNBQWMsV0FBVyxHQUFHSCxnREFBbUIsQ0FBQyxPQUFPO0lBQ3pEb0IsT0FBTztJQUNQQyxTQUFTO0lBQ1RaLE1BQU07SUFDTmEsUUFBUTtJQUNSQyxPQUFPO0FBQ1gsR0FBRyxXQUFXLEdBQUd2QixnREFBbUIsQ0FBQyxRQUFRO0lBQ3pDd0IsVUFBVTtJQUNWQyxHQUFHO0lBQ0hDLFVBQVU7QUFDZDtBQUNBLE1BQU1yQixjQUFjLFdBQVcsR0FBR0wsZ0RBQW1CLENBQUMsT0FBTztJQUN6RG9CLE9BQU87SUFDUEMsU0FBUztJQUNUWixNQUFNO0lBQ05hLFFBQVE7SUFDUkMsT0FBTztBQUNYLEdBQUcsV0FBVyxHQUFHdkIsZ0RBQW1CLENBQUMsUUFBUTtJQUN6Q3dCLFVBQVU7SUFDVkMsR0FBRztJQUNIQyxVQUFVO0FBQ2Q7QUFDQSxNQUFNdEIsV0FBVyxXQUFXLEdBQUdKLGdEQUFtQixDQUFDLE9BQU87SUFDdERvQixPQUFPO0lBQ1BDLFNBQVM7SUFDVFosTUFBTTtJQUNOYSxRQUFRO0lBQ1JDLE9BQU87QUFDWCxHQUFHLFdBQVcsR0FBR3ZCLGdEQUFtQixDQUFDLFFBQVE7SUFDekN3QixVQUFVO0lBQ1ZDLEdBQUc7SUFDSEMsVUFBVTtBQUNkO0FBQ0EsTUFBTXBCLFlBQVksV0FBVyxHQUFHTixnREFBbUIsQ0FBQyxPQUFPO0lBQ3ZEb0IsT0FBTztJQUNQQyxTQUFTO0lBQ1RaLE1BQU07SUFDTmEsUUFBUTtJQUNSQyxPQUFPO0FBQ1gsR0FBRyxXQUFXLEdBQUd2QixnREFBbUIsQ0FBQyxRQUFRO0lBQ3pDd0IsVUFBVTtJQUNWQyxHQUFHO0lBQ0hDLFVBQVU7QUFDZDtBQUNBLE1BQU1DLFlBQVksV0FBVyxHQUFHM0IsZ0RBQW1CLENBQUMsT0FBTztJQUN2RG9CLE9BQU87SUFDUEcsT0FBTztJQUNQRCxRQUFRO0lBQ1JELFNBQVM7SUFDVFosTUFBTTtJQUNObUIsUUFBUTtJQUNSQyxhQUFhO0lBQ2JDLGVBQWU7SUFDZkMsZ0JBQWdCO0FBQ3BCLEdBQUcsV0FBVyxHQUFHL0IsZ0RBQW1CLENBQUMsUUFBUTtJQUN6Q2dDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7QUFDUixJQUFJLFdBQVcsR0FBR25DLGdEQUFtQixDQUFDLFFBQVE7SUFDMUNnQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0FBQ1I7QUFFQSxNQUFNQyxzQkFBc0I7SUFDeEIsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHdEMsMkNBQWMsQ0FBQ1YsU0FBU2tELE1BQU07SUFDOUV4Qyw0Q0FBZTt5Q0FBQztZQUNaLE1BQU0wQzswREFBVztvQkFDYkosb0JBQW9CaEQsU0FBU2tELE1BQU07Z0JBQ3ZDOztZQUNBbEQsU0FBU3FELGdCQUFnQixDQUFDLG9CQUFvQkQ7WUFDOUM7aURBQU8sSUFBSUUsT0FBT0MsbUJBQW1CLENBQUMsb0JBQW9CSDs7UUFDOUQ7d0NBQUcsRUFBRTtJQUNMLE9BQU9MO0FBQ1g7QUFFQSxJQUFJUyxnQkFBZ0I7QUFDcEIsTUFBTUM7SUFDRkMsYUFBYTtRQUNULGtFQUFrRTtRQUNsRSxJQUFJLENBQUNDLFNBQVMsR0FBRyxDQUFDQztZQUNkLElBQUksQ0FBQ0MsV0FBVyxDQUFDQyxJQUFJLENBQUNGO1lBQ3RCLE9BQU87Z0JBQ0gsTUFBTUcsUUFBUSxJQUFJLENBQUNGLFdBQVcsQ0FBQ0csT0FBTyxDQUFDSjtnQkFDdkMsSUFBSSxDQUFDQyxXQUFXLENBQUNJLE1BQU0sQ0FBQ0YsT0FBTztZQUNuQztRQUNKO1FBQ0EsSUFBSSxDQUFDRyxPQUFPLEdBQUcsQ0FBQ0M7WUFDWixJQUFJLENBQUNOLFdBQVcsQ0FBQ08sT0FBTyxDQUFDLENBQUNSLGFBQWFBLFdBQVdPO1FBQ3REO1FBQ0EsSUFBSSxDQUFDRSxRQUFRLEdBQUcsQ0FBQ0Y7WUFDYixJQUFJLENBQUNELE9BQU8sQ0FBQ0M7WUFDYixJQUFJLENBQUNHLE1BQU0sR0FBRzttQkFDUCxJQUFJLENBQUNBLE1BQU07Z0JBQ2RIO2FBQ0g7UUFDTDtRQUNBLElBQUksQ0FBQ0ksTUFBTSxHQUFHLENBQUNKO1lBQ1gsSUFBSUs7WUFDSixNQUFNLEVBQUVDLE9BQU8sRUFBRSxHQUFHQyxNQUFNLEdBQUdQO1lBQzdCLE1BQU1RLEtBQUssT0FBUVIsQ0FBQUEsUUFBUSxPQUFPLEtBQUssSUFBSUEsS0FBS1EsRUFBRSxNQUFNLFlBQVksQ0FBQyxDQUFDSCxXQUFXTCxLQUFLUSxFQUFFLEtBQUssT0FBTyxLQUFLLElBQUlILFNBQVNJLE1BQU0sSUFBSSxJQUFJVCxLQUFLUSxFQUFFLEdBQUduQjtZQUM5SSxNQUFNcUIsZ0JBQWdCLElBQUksQ0FBQ1AsTUFBTSxDQUFDUSxJQUFJLENBQUMsQ0FBQ0M7Z0JBQ3BDLE9BQU9BLE1BQU1KLEVBQUUsS0FBS0E7WUFDeEI7WUFDQSxNQUFNSyxjQUFjYixLQUFLYSxXQUFXLEtBQUtDLFlBQVksT0FBT2QsS0FBS2EsV0FBVztZQUM1RSxJQUFJLElBQUksQ0FBQ0UsZUFBZSxDQUFDQyxHQUFHLENBQUNSLEtBQUs7Z0JBQzlCLElBQUksQ0FBQ08sZUFBZSxDQUFDRSxNQUFNLENBQUNUO1lBQ2hDO1lBQ0EsSUFBSUUsZUFBZTtnQkFDZixJQUFJLENBQUNQLE1BQU0sR0FBRyxJQUFJLENBQUNBLE1BQU0sQ0FBQzVDLEdBQUcsQ0FBQyxDQUFDcUQ7b0JBQzNCLElBQUlBLE1BQU1KLEVBQUUsS0FBS0EsSUFBSTt3QkFDakIsSUFBSSxDQUFDVCxPQUFPLENBQUM7NEJBQ1QsR0FBR2EsS0FBSzs0QkFDUixHQUFHWixJQUFJOzRCQUNQUTs0QkFDQVUsT0FBT1o7d0JBQ1g7d0JBQ0EsT0FBTzs0QkFDSCxHQUFHTSxLQUFLOzRCQUNSLEdBQUdaLElBQUk7NEJBQ1BROzRCQUNBSzs0QkFDQUssT0FBT1o7d0JBQ1g7b0JBQ0o7b0JBQ0EsT0FBT007Z0JBQ1g7WUFDSixPQUFPO2dCQUNILElBQUksQ0FBQ1YsUUFBUSxDQUFDO29CQUNWZ0IsT0FBT1o7b0JBQ1AsR0FBR0MsSUFBSTtvQkFDUE07b0JBQ0FMO2dCQUNKO1lBQ0o7WUFDQSxPQUFPQTtRQUNYO1FBQ0EsSUFBSSxDQUFDVyxPQUFPLEdBQUcsQ0FBQ1g7WUFDWixJQUFJQSxJQUFJO2dCQUNKLElBQUksQ0FBQ08sZUFBZSxDQUFDSyxHQUFHLENBQUNaO2dCQUN6QmEsc0JBQXNCLElBQUksSUFBSSxDQUFDM0IsV0FBVyxDQUFDTyxPQUFPLENBQUMsQ0FBQ1IsYUFBYUEsV0FBVzs0QkFDaEVlOzRCQUNBVyxTQUFTO3dCQUNiO1lBQ1osT0FBTztnQkFDSCxJQUFJLENBQUNoQixNQUFNLENBQUNGLE9BQU8sQ0FBQyxDQUFDVztvQkFDakIsSUFBSSxDQUFDbEIsV0FBVyxDQUFDTyxPQUFPLENBQUMsQ0FBQ1IsYUFBYUEsV0FBVzs0QkFDMUNlLElBQUlJLE1BQU1KLEVBQUU7NEJBQ1pXLFNBQVM7d0JBQ2I7Z0JBQ1I7WUFDSjtZQUNBLE9BQU9YO1FBQ1g7UUFDQSxJQUFJLENBQUNGLE9BQU8sR0FBRyxDQUFDQSxTQUFTTjtZQUNyQixPQUFPLElBQUksQ0FBQ0ksTUFBTSxDQUFDO2dCQUNmLEdBQUdKLElBQUk7Z0JBQ1BNO1lBQ0o7UUFDSjtRQUNBLElBQUksQ0FBQ2dCLEtBQUssR0FBRyxDQUFDaEIsU0FBU047WUFDbkIsT0FBTyxJQUFJLENBQUNJLE1BQU0sQ0FBQztnQkFDZixHQUFHSixJQUFJO2dCQUNQTTtnQkFDQXBFLE1BQU07WUFDVjtRQUNKO1FBQ0EsSUFBSSxDQUFDcUYsT0FBTyxHQUFHLENBQUNqQixTQUFTTjtZQUNyQixPQUFPLElBQUksQ0FBQ0ksTUFBTSxDQUFDO2dCQUNmLEdBQUdKLElBQUk7Z0JBQ1A5RCxNQUFNO2dCQUNOb0U7WUFDSjtRQUNKO1FBQ0EsSUFBSSxDQUFDa0IsSUFBSSxHQUFHLENBQUNsQixTQUFTTjtZQUNsQixPQUFPLElBQUksQ0FBQ0ksTUFBTSxDQUFDO2dCQUNmLEdBQUdKLElBQUk7Z0JBQ1A5RCxNQUFNO2dCQUNOb0U7WUFDSjtRQUNKO1FBQ0EsSUFBSSxDQUFDbUIsT0FBTyxHQUFHLENBQUNuQixTQUFTTjtZQUNyQixPQUFPLElBQUksQ0FBQ0ksTUFBTSxDQUFDO2dCQUNmLEdBQUdKLElBQUk7Z0JBQ1A5RCxNQUFNO2dCQUNOb0U7WUFDSjtRQUNKO1FBQ0EsSUFBSSxDQUFDb0IsT0FBTyxHQUFHLENBQUNwQixTQUFTTjtZQUNyQixPQUFPLElBQUksQ0FBQ0ksTUFBTSxDQUFDO2dCQUNmLEdBQUdKLElBQUk7Z0JBQ1A5RCxNQUFNO2dCQUNOb0U7WUFDSjtRQUNKO1FBQ0EsSUFBSSxDQUFDcUIsT0FBTyxHQUFHLENBQUNBLFNBQVMzQjtZQUNyQixJQUFJLENBQUNBLE1BQU07Z0JBQ1Asa0JBQWtCO2dCQUNsQjtZQUNKO1lBQ0EsSUFBSVEsS0FBS007WUFDVCxJQUFJZCxLQUFLMEIsT0FBTyxLQUFLWixXQUFXO2dCQUM1Qk4sS0FBSyxJQUFJLENBQUNKLE1BQU0sQ0FBQztvQkFDYixHQUFHSixJQUFJO29CQUNQMkI7b0JBQ0F6RixNQUFNO29CQUNOb0UsU0FBU04sS0FBSzBCLE9BQU87b0JBQ3JCRSxhQUFhLE9BQU81QixLQUFLNEIsV0FBVyxLQUFLLGFBQWE1QixLQUFLNEIsV0FBVyxHQUFHZDtnQkFDN0U7WUFDSjtZQUNBLE1BQU1lLElBQUlDLFFBQVFDLE9BQU8sQ0FBQ0osbUJBQW1CSyxXQUFXTCxZQUFZQTtZQUNwRSxJQUFJTSxnQkFBZ0J6QixPQUFPTTtZQUMzQixJQUFJb0I7WUFDSixNQUFNQyxrQkFBa0JOLEVBQUVPLElBQUksQ0FBQyxPQUFPQztnQkFDbENILFNBQVM7b0JBQ0w7b0JBQ0FHO2lCQUNIO2dCQUNELE1BQU1DLHVDQUF5Qi9GLGlEQUFvQixDQUFDOEY7Z0JBQ3BELElBQUlDLHdCQUF3QjtvQkFDeEJMLGdCQUFnQjtvQkFDaEIsSUFBSSxDQUFDN0IsTUFBTSxDQUFDO3dCQUNSSTt3QkFDQXRFLE1BQU07d0JBQ05vRSxTQUFTK0I7b0JBQ2I7Z0JBQ0osT0FBTyxJQUFJRyxlQUFlSCxhQUFhLENBQUNBLFNBQVNJLEVBQUUsRUFBRTtvQkFDakRSLGdCQUFnQjtvQkFDaEIsTUFBTVMsY0FBYyxPQUFPMUMsS0FBS3NCLEtBQUssS0FBSyxhQUFhLE1BQU10QixLQUFLc0IsS0FBSyxDQUFDLENBQUMsb0JBQW9CLEVBQUVlLFNBQVNNLE1BQU0sRUFBRSxJQUFJM0MsS0FBS3NCLEtBQUs7b0JBQzlILE1BQU1NLGNBQWMsT0FBTzVCLEtBQUs0QixXQUFXLEtBQUssYUFBYSxNQUFNNUIsS0FBSzRCLFdBQVcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFUyxTQUFTTSxNQUFNLEVBQUUsSUFBSTNDLEtBQUs0QixXQUFXO29CQUNoSixNQUFNZ0IsbUJBQW1CLE9BQU9GLGdCQUFnQixZQUFZLGVBQUNuRyxpREFBb0IsQ0FBQ21HO29CQUNsRixNQUFNRyxnQkFBZ0JELG1CQUFtQkYsY0FBYzt3QkFDbkRwQyxTQUFTb0M7b0JBQ2I7b0JBQ0EsSUFBSSxDQUFDdEMsTUFBTSxDQUFDO3dCQUNSSTt3QkFDQXRFLE1BQU07d0JBQ04wRjt3QkFDQSxHQUFHaUIsYUFBYTtvQkFDcEI7Z0JBQ0osT0FBTyxJQUFJUixvQkFBb0JTLE9BQU87b0JBQ2xDYixnQkFBZ0I7b0JBQ2hCLE1BQU1TLGNBQWMsT0FBTzFDLEtBQUtzQixLQUFLLEtBQUssYUFBYSxNQUFNdEIsS0FBS3NCLEtBQUssQ0FBQ2UsWUFBWXJDLEtBQUtzQixLQUFLO29CQUM5RixNQUFNTSxjQUFjLE9BQU81QixLQUFLNEIsV0FBVyxLQUFLLGFBQWEsTUFBTTVCLEtBQUs0QixXQUFXLENBQUNTLFlBQVlyQyxLQUFLNEIsV0FBVztvQkFDaEgsTUFBTWdCLG1CQUFtQixPQUFPRixnQkFBZ0IsWUFBWSxlQUFDbkcsaURBQW9CLENBQUNtRztvQkFDbEYsTUFBTUcsZ0JBQWdCRCxtQkFBbUJGLGNBQWM7d0JBQ25EcEMsU0FBU29DO29CQUNiO29CQUNBLElBQUksQ0FBQ3RDLE1BQU0sQ0FBQzt3QkFDUkk7d0JBQ0F0RSxNQUFNO3dCQUNOMEY7d0JBQ0EsR0FBR2lCLGFBQWE7b0JBQ3BCO2dCQUNKLE9BQU8sSUFBSTdDLEtBQUt1QixPQUFPLEtBQUtULFdBQVc7b0JBQ25DbUIsZ0JBQWdCO29CQUNoQixNQUFNUyxjQUFjLE9BQU8xQyxLQUFLdUIsT0FBTyxLQUFLLGFBQWEsTUFBTXZCLEtBQUt1QixPQUFPLENBQUNjLFlBQVlyQyxLQUFLdUIsT0FBTztvQkFDcEcsTUFBTUssY0FBYyxPQUFPNUIsS0FBSzRCLFdBQVcsS0FBSyxhQUFhLE1BQU01QixLQUFLNEIsV0FBVyxDQUFDUyxZQUFZckMsS0FBSzRCLFdBQVc7b0JBQ2hILE1BQU1nQixtQkFBbUIsT0FBT0YsZ0JBQWdCLFlBQVksZUFBQ25HLGlEQUFvQixDQUFDbUc7b0JBQ2xGLE1BQU1HLGdCQUFnQkQsbUJBQW1CRixjQUFjO3dCQUNuRHBDLFNBQVNvQztvQkFDYjtvQkFDQSxJQUFJLENBQUN0QyxNQUFNLENBQUM7d0JBQ1JJO3dCQUNBdEUsTUFBTTt3QkFDTjBGO3dCQUNBLEdBQUdpQixhQUFhO29CQUNwQjtnQkFDSjtZQUNKLEdBQUdFLEtBQUssQ0FBQyxPQUFPekI7Z0JBQ1pZLFNBQVM7b0JBQ0w7b0JBQ0FaO2lCQUNIO2dCQUNELElBQUl0QixLQUFLc0IsS0FBSyxLQUFLUixXQUFXO29CQUMxQm1CLGdCQUFnQjtvQkFDaEIsTUFBTVMsY0FBYyxPQUFPMUMsS0FBS3NCLEtBQUssS0FBSyxhQUFhLE1BQU10QixLQUFLc0IsS0FBSyxDQUFDQSxTQUFTdEIsS0FBS3NCLEtBQUs7b0JBQzNGLE1BQU1NLGNBQWMsT0FBTzVCLEtBQUs0QixXQUFXLEtBQUssYUFBYSxNQUFNNUIsS0FBSzRCLFdBQVcsQ0FBQ04sU0FBU3RCLEtBQUs0QixXQUFXO29CQUM3RyxNQUFNZ0IsbUJBQW1CLE9BQU9GLGdCQUFnQixZQUFZLGVBQUNuRyxpREFBb0IsQ0FBQ21HO29CQUNsRixNQUFNRyxnQkFBZ0JELG1CQUFtQkYsY0FBYzt3QkFDbkRwQyxTQUFTb0M7b0JBQ2I7b0JBQ0EsSUFBSSxDQUFDdEMsTUFBTSxDQUFDO3dCQUNSSTt3QkFDQXRFLE1BQU07d0JBQ04wRjt3QkFDQSxHQUFHaUIsYUFBYTtvQkFDcEI7Z0JBQ0o7WUFDSixHQUFHRyxPQUFPLENBQUM7Z0JBQ1AsSUFBSWYsZUFBZTtvQkFDZix1RUFBdUU7b0JBQ3ZFLElBQUksQ0FBQ2QsT0FBTyxDQUFDWDtvQkFDYkEsS0FBS007Z0JBQ1Q7Z0JBQ0FkLEtBQUtnRCxPQUFPLElBQUksT0FBTyxLQUFLLElBQUloRCxLQUFLZ0QsT0FBTyxDQUFDQyxJQUFJLENBQUNqRDtZQUN0RDtZQUNBLE1BQU1rRCxTQUFTLElBQUksSUFBSXBCLFFBQVEsQ0FBQ0MsU0FBU29CLFNBQVNoQixnQkFBZ0JDLElBQUksQ0FBQyxJQUFJRixNQUFNLENBQUMsRUFBRSxLQUFLLFdBQVdpQixPQUFPakIsTUFBTSxDQUFDLEVBQUUsSUFBSUgsUUFBUUcsTUFBTSxDQUFDLEVBQUUsR0FBR2EsS0FBSyxDQUFDSTtZQUNsSixJQUFJLE9BQU8zQyxPQUFPLFlBQVksT0FBT0EsT0FBTyxVQUFVO2dCQUNsRCxvQ0FBb0M7Z0JBQ3BDLE9BQU87b0JBQ0gwQztnQkFDSjtZQUNKLE9BQU87Z0JBQ0gsT0FBT0UsT0FBT0MsTUFBTSxDQUFDN0MsSUFBSTtvQkFDckIwQztnQkFDSjtZQUNKO1FBQ0o7UUFDQSxJQUFJLENBQUNJLE1BQU0sR0FBRyxDQUFDQyxLQUFLdkQ7WUFDaEIsTUFBTVEsS0FBSyxDQUFDUixRQUFRLE9BQU8sS0FBSyxJQUFJQSxLQUFLUSxFQUFFLEtBQUtuQjtZQUNoRCxJQUFJLENBQUNlLE1BQU0sQ0FBQztnQkFDUm1ELEtBQUtBLElBQUkvQztnQkFDVEE7Z0JBQ0EsR0FBR1IsSUFBSTtZQUNYO1lBQ0EsT0FBT1E7UUFDWDtRQUNBLElBQUksQ0FBQ2dELGVBQWUsR0FBRztZQUNuQixPQUFPLElBQUksQ0FBQ3JELE1BQU0sQ0FBQy9DLE1BQU0sQ0FBQyxDQUFDd0QsUUFBUSxDQUFDLElBQUksQ0FBQ0csZUFBZSxDQUFDQyxHQUFHLENBQUNKLE1BQU1KLEVBQUU7UUFDekU7UUFDQSxJQUFJLENBQUNkLFdBQVcsR0FBRyxFQUFFO1FBQ3JCLElBQUksQ0FBQ1MsTUFBTSxHQUFHLEVBQUU7UUFDaEIsSUFBSSxDQUFDWSxlQUFlLEdBQUcsSUFBSTBDO0lBQy9CO0FBQ0o7QUFDQSxNQUFNQyxhQUFhLElBQUlwRTtBQUN2QixrQ0FBa0M7QUFDbEMsTUFBTXFFLGdCQUFnQixDQUFDckQsU0FBU047SUFDNUIsTUFBTVEsS0FBSyxDQUFDUixRQUFRLE9BQU8sS0FBSyxJQUFJQSxLQUFLUSxFQUFFLEtBQUtuQjtJQUNoRHFFLFdBQVd4RCxRQUFRLENBQUM7UUFDaEJnQixPQUFPWjtRQUNQLEdBQUdOLElBQUk7UUFDUFE7SUFDSjtJQUNBLE9BQU9BO0FBQ1g7QUFDQSxNQUFNZ0MsaUJBQWlCLENBQUN4QztJQUNwQixPQUFPQSxRQUFRLE9BQU9BLFNBQVMsWUFBWSxRQUFRQSxRQUFRLE9BQU9BLEtBQUt5QyxFQUFFLEtBQUssYUFBYSxZQUFZekMsUUFBUSxPQUFPQSxLQUFLMkMsTUFBTSxLQUFLO0FBQzFJO0FBQ0EsTUFBTWlCLGFBQWFEO0FBQ25CLE1BQU1FLGFBQWEsSUFBSUgsV0FBV3ZELE1BQU07QUFDeEMsTUFBTTJELFlBQVksSUFBSUosV0FBV0YsZUFBZTtBQUNoRCx1RkFBdUY7QUFDdkYsTUFBTTVDLFFBQVF3QyxPQUFPQyxNQUFNLENBQUNPLFlBQVk7SUFDcENyQyxTQUFTbUMsV0FBV25DLE9BQU87SUFDM0JDLE1BQU1rQyxXQUFXbEMsSUFBSTtJQUNyQkMsU0FBU2lDLFdBQVdqQyxPQUFPO0lBQzNCSCxPQUFPb0MsV0FBV3BDLEtBQUs7SUFDdkJnQyxRQUFRSSxXQUFXSixNQUFNO0lBQ3pCaEQsU0FBU29ELFdBQVdwRCxPQUFPO0lBQzNCcUIsU0FBUytCLFdBQVcvQixPQUFPO0lBQzNCUixTQUFTdUMsV0FBV3ZDLE9BQU87SUFDM0JPLFNBQVNnQyxXQUFXaEMsT0FBTztBQUMvQixHQUFHO0lBQ0NtQztJQUNBQztBQUNKO0FBRUFuSSxZQUFZO0FBRVosU0FBU29JLFNBQVNDLE1BQU07SUFDcEIsT0FBT0EsT0FBT0MsS0FBSyxLQUFLbkQ7QUFDNUI7QUFFQSx3QkFBd0I7QUFDeEIsTUFBTW9ELHdCQUF3QjtBQUM5QixtQkFBbUI7QUFDbkIsTUFBTUMsa0JBQWtCO0FBQ3hCLDBCQUEwQjtBQUMxQixNQUFNQyx5QkFBeUI7QUFDL0IsdUNBQXVDO0FBQ3ZDLE1BQU1DLGlCQUFpQjtBQUN2QixzQkFBc0I7QUFDdEIsTUFBTUMsY0FBYztBQUNwQiw2QkFBNkI7QUFDN0IsTUFBTUMsTUFBTTtBQUNaLCtCQUErQjtBQUMvQixNQUFNQyxrQkFBa0I7QUFDeEIsbUNBQW1DO0FBQ25DLE1BQU1DLHNCQUFzQjtBQUM1QixTQUFTQyxHQUFHLEdBQUdDLE9BQU87SUFDbEIsT0FBT0EsUUFBUXZILE1BQU0sQ0FBQ0MsU0FBU0MsSUFBSSxDQUFDO0FBQ3hDO0FBQ0EsU0FBU3NILDBCQUEwQkMsUUFBUTtJQUN2QyxNQUFNLENBQUNDLEdBQUdDLEVBQUUsR0FBR0YsU0FBU0csS0FBSyxDQUFDO0lBQzlCLE1BQU1DLGFBQWEsRUFBRTtJQUNyQixJQUFJSCxHQUFHO1FBQ0hHLFdBQVd0RixJQUFJLENBQUNtRjtJQUNwQjtJQUNBLElBQUlDLEdBQUc7UUFDSEUsV0FBV3RGLElBQUksQ0FBQ29GO0lBQ3BCO0lBQ0EsT0FBT0U7QUFDWDtBQUNBLE1BQU1DLFFBQVEsQ0FBQ0M7SUFDWCxJQUFJQyxtQkFBbUJDLG9CQUFvQkMsb0JBQW9CQyxvQkFBb0JDLG9CQUFvQkMsb0JBQW9CQyxvQkFBb0JDLG9CQUFvQkM7SUFDbkssTUFBTSxFQUFFQyxRQUFRQyxhQUFhLEVBQUVsRixLQUFLLEVBQUVtRixRQUFRLEVBQUVDLFdBQVcsRUFBRUMsVUFBVSxFQUFFQyxhQUFhLEVBQUVDLE9BQU8sRUFBRXZHLEtBQUssRUFBRU8sTUFBTSxFQUFFaUcsUUFBUSxFQUFFQyxXQUFXLEVBQUVDLGlCQUFpQixFQUFFQyxhQUFhQyxzQkFBc0IsRUFBRXhLLEtBQUssRUFBRXlLLGlCQUFpQixFQUFFQyxpQkFBaUIsRUFBRXZKLFlBQVksRUFBRSxFQUFFd0osdUJBQXVCLEVBQUUsRUFBRUMsVUFBVUMsbUJBQW1CLEVBQUVoQyxRQUFRLEVBQUVpQyxHQUFHLEVBQUVDLGVBQWUsRUFBRUMsVUFBVSxFQUFFQyxLQUFLLEVBQUVDLHVCQUF1QixhQUFhLEVBQUUsR0FBRy9CO0lBQ2xaLE1BQU0sQ0FBQ2dDLGdCQUFnQkMsa0JBQWtCLEdBQUc3SywyQ0FBYyxDQUFDO0lBQzNELE1BQU0sQ0FBQzhLLG1CQUFtQkMscUJBQXFCLEdBQUcvSywyQ0FBYyxDQUFDO0lBQ2pFLE1BQU0sQ0FBQ2dMLFNBQVNDLFdBQVcsR0FBR2pMLDJDQUFjLENBQUM7SUFDN0MsTUFBTSxDQUFDa0wsU0FBU0MsV0FBVyxHQUFHbkwsMkNBQWMsQ0FBQztJQUM3QyxNQUFNLENBQUNvTCxTQUFTQyxXQUFXLEdBQUdyTCwyQ0FBYyxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3NMLFVBQVVDLFlBQVksR0FBR3ZMLDJDQUFjLENBQUM7SUFDL0MsTUFBTSxDQUFDd0wsVUFBVUMsWUFBWSxHQUFHekwsMkNBQWMsQ0FBQztJQUMvQyxNQUFNLENBQUMwTCxvQkFBb0JDLHNCQUFzQixHQUFHM0wsMkNBQWMsQ0FBQztJQUNuRSxNQUFNLENBQUM0TCxlQUFlQyxpQkFBaUIsR0FBRzdMLDJDQUFjLENBQUM7SUFDekQsTUFBTThMLGdCQUFnQjlMLHlDQUFZLENBQUNxRSxNQUFNZ0csUUFBUSxJQUFJQyx1QkFBdUJ4QztJQUM1RSxNQUFNa0UsZ0JBQWdCaE0seUNBQVksQ0FBQztJQUNuQyxNQUFNaU0sV0FBV2pNLHlDQUFZLENBQUM7SUFDOUIsTUFBTWtNLFVBQVU3SSxVQUFVO0lBQzFCLE1BQU04SSxZQUFZOUksUUFBUSxLQUFLc0c7SUFDL0IsTUFBTXlDLFlBQVkvSCxNQUFNMUUsSUFBSTtJQUM1QixNQUFNMkUsY0FBY0QsTUFBTUMsV0FBVyxLQUFLO0lBQzFDLE1BQU0rSCxpQkFBaUJoSSxNQUFNekQsU0FBUyxJQUFJO0lBQzFDLE1BQU0wTCw0QkFBNEJqSSxNQUFNK0Ysb0JBQW9CLElBQUk7SUFDaEUsOElBQThJO0lBQzlJLE1BQU1tQyxjQUFjdk0sMENBQWE7c0NBQUMsSUFBSTRKLFFBQVE2QyxTQUFTOzhDQUFDLENBQUNuTCxTQUFTQSxPQUFPb0wsT0FBTyxLQUFLckksTUFBTUosRUFBRTtnREFBSztxQ0FBRztRQUNqRzJGO1FBQ0F2RixNQUFNSixFQUFFO0tBQ1g7SUFDRCxNQUFNK0YsY0FBY2hLLDBDQUFhO3NDQUFDO1lBQzlCLElBQUkyTTtZQUNKLE9BQU8sQ0FBQ0EscUJBQXFCdEksTUFBTTJGLFdBQVcsS0FBSyxPQUFPMkMscUJBQXFCMUM7UUFDbkY7cUNBQUc7UUFDQzVGLE1BQU0yRixXQUFXO1FBQ2pCQztLQUNIO0lBQ0QsTUFBTUksV0FBV3JLLDBDQUFhO21DQUFDLElBQUlxRSxNQUFNZ0csUUFBUSxJQUFJQyx1QkFBdUJ4QztrQ0FBZ0I7UUFDeEZ6RCxNQUFNZ0csUUFBUTtRQUNkQztLQUNIO0lBQ0QsTUFBTXNDLHlCQUF5QjVNLHlDQUFZLENBQUM7SUFDNUMsTUFBTTZNLFNBQVM3TSx5Q0FBWSxDQUFDO0lBQzVCLE1BQU04TSw2QkFBNkI5TSx5Q0FBWSxDQUFDO0lBQ2hELE1BQU0rTSxrQkFBa0IvTSx5Q0FBWSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ3VJLEdBQUdDLEVBQUUsR0FBR0YsU0FBU0csS0FBSyxDQUFDO0lBQzlCLE1BQU11RSxxQkFBcUJoTiwwQ0FBYTs2Q0FBQztZQUNyQyxPQUFPNEosUUFBUXFELE1BQU07cURBQUMsQ0FBQ0MsTUFBTUMsTUFBTUM7b0JBQy9CLDBDQUEwQztvQkFDMUMsSUFBSUEsZ0JBQWdCYixhQUFhO3dCQUM3QixPQUFPVztvQkFDWDtvQkFDQSxPQUFPQSxPQUFPQyxLQUFLN0wsTUFBTTtnQkFDN0I7b0RBQUc7UUFDUDs0Q0FBRztRQUNDc0k7UUFDQTJDO0tBQ0g7SUFDRCxNQUFNbEssbUJBQW1CRDtJQUN6QixNQUFNa0gsU0FBU2pGLE1BQU1pRixNQUFNLElBQUlDO0lBQy9CLE1BQU04RCxXQUFXakIsY0FBYztJQUMvQlMsT0FBT1MsT0FBTyxHQUFHdE4sMENBQWE7eUJBQUMsSUFBSXVNLGNBQWNoQyxNQUFNeUM7d0JBQW9CO1FBQ3ZFVDtRQUNBUztLQUNIO0lBQ0RoTiw0Q0FBZTsyQkFBQztZQUNaOEwsY0FBY3dCLE9BQU8sR0FBR2pEO1FBQzVCOzBCQUFHO1FBQ0NBO0tBQ0g7SUFDRHJLLDRDQUFlOzJCQUFDO1lBQ1osc0RBQXNEO1lBQ3REaUwsV0FBVztRQUNmOzBCQUFHLEVBQUU7SUFDTGpMLDRDQUFlOzJCQUFDO1lBQ1osTUFBTXVOLFlBQVl0QixTQUFTcUIsT0FBTztZQUNsQyxJQUFJQyxXQUFXO2dCQUNYLE1BQU1qTSxTQUFTaU0sVUFBVUMscUJBQXFCLEdBQUdsTSxNQUFNO2dCQUN2RCwrREFBK0Q7Z0JBQy9EdUssaUJBQWlCdks7Z0JBQ2pCb0k7dUNBQVcsQ0FBQytELElBQUk7NEJBQ1I7Z0NBQ0lmLFNBQVNySSxNQUFNSixFQUFFO2dDQUNqQjNDO2dDQUNBZ0gsVUFBVWpFLE1BQU1pRSxRQUFROzRCQUM1QjsrQkFDR21GO3lCQUNOOztnQkFDTDt1Q0FBTyxJQUFJL0Q7K0NBQVcsQ0FBQytELElBQUlBLEVBQUU1TSxNQUFNO3VEQUFDLENBQUNTLFNBQVNBLE9BQU9vTCxPQUFPLEtBQUtySSxNQUFNSixFQUFFOzs7O1lBQzdFO1FBQ0o7MEJBQUc7UUFDQ3lGO1FBQ0FyRixNQUFNSixFQUFFO0tBQ1g7SUFDRGpFLGtEQUFxQjtpQ0FBQztZQUNsQixJQUFJLENBQUNnTCxTQUFTO1lBQ2QsTUFBTXVDLFlBQVl0QixTQUFTcUIsT0FBTztZQUNsQyxNQUFNSyxpQkFBaUJKLFVBQVU5TixLQUFLLENBQUM2QixNQUFNO1lBQzdDaU0sVUFBVTlOLEtBQUssQ0FBQzZCLE1BQU0sR0FBRztZQUN6QixNQUFNc00sWUFBWUwsVUFBVUMscUJBQXFCLEdBQUdsTSxNQUFNO1lBQzFEaU0sVUFBVTlOLEtBQUssQ0FBQzZCLE1BQU0sR0FBR3FNO1lBQ3pCOUIsaUJBQWlCK0I7WUFDakJsRTt5Q0FBVyxDQUFDRTtvQkFDUixNQUFNekYsZ0JBQWdCeUYsUUFBUXhGLElBQUk7K0RBQUMsQ0FBQzlDLFNBQVNBLE9BQU9vTCxPQUFPLEtBQUtySSxNQUFNSixFQUFFOztvQkFDeEUsSUFBSSxDQUFDRSxlQUFlO3dCQUNoQixPQUFPOzRCQUNIO2dDQUNJdUksU0FBU3JJLE1BQU1KLEVBQUU7Z0NBQ2pCM0MsUUFBUXNNO2dDQUNSdEYsVUFBVWpFLE1BQU1pRSxRQUFROzRCQUM1QjsrQkFDR3NCO3lCQUNOO29CQUNMLE9BQU87d0JBQ0gsT0FBT0EsUUFBUTVJLEdBQUc7cURBQUMsQ0FBQ00sU0FBU0EsT0FBT29MLE9BQU8sS0FBS3JJLE1BQU1KLEVBQUUsR0FBRztvQ0FDbkQsR0FBRzNDLE1BQU07b0NBQ1RBLFFBQVFzTTtnQ0FDWixJQUFJdE07O29CQUNaO2dCQUNKOztRQUNKO2dDQUFHO1FBQ0MwSjtRQUNBM0csTUFBTU0sS0FBSztRQUNYTixNQUFNZ0IsV0FBVztRQUNqQnFFO1FBQ0FyRixNQUFNSixFQUFFO0tBQ1g7SUFDRCxNQUFNNEosY0FBYzdOLDhDQUFpQjswQ0FBQztZQUNsQywrQ0FBK0M7WUFDL0NtTCxXQUFXO1lBQ1hRLHNCQUFzQmtCLE9BQU9TLE9BQU87WUFDcEM1RDtrREFBVyxDQUFDK0QsSUFBSUEsRUFBRTVNLE1BQU07MERBQUMsQ0FBQ1MsU0FBU0EsT0FBT29MLE9BQU8sS0FBS3JJLE1BQU1KLEVBQUU7OztZQUM5RDhKO2tEQUFXO29CQUNQakUsWUFBWXpGO2dCQUNoQjtpREFBRzZEO1FBQ1A7eUNBQUc7UUFDQzdEO1FBQ0F5RjtRQUNBSjtRQUNBbUQ7S0FDSDtJQUNEN00sNENBQWU7MkJBQUM7WUFDWixJQUFJcUUsTUFBTWUsT0FBTyxJQUFJZ0gsY0FBYyxhQUFhL0gsTUFBTWdHLFFBQVEsS0FBSzJELFlBQVkzSixNQUFNMUUsSUFBSSxLQUFLLFdBQVc7WUFDekcsSUFBSXNPO1lBQ0osZ0NBQWdDO1lBQ2hDLE1BQU1DOzhDQUFhO29CQUNmLElBQUlwQiwyQkFBMkJRLE9BQU8sR0FBR1YsdUJBQXVCVSxPQUFPLEVBQUU7d0JBQ3JFLCtDQUErQzt3QkFDL0MsTUFBTWEsY0FBYyxJQUFJQyxPQUFPQyxPQUFPLEtBQUt6Qix1QkFBdUJVLE9BQU87d0JBQ3pFeEIsY0FBY3dCLE9BQU8sR0FBR3hCLGNBQWN3QixPQUFPLEdBQUdhO29CQUNwRDtvQkFDQXJCLDJCQUEyQlEsT0FBTyxHQUFHLElBQUljLE9BQU9DLE9BQU87Z0JBQzNEOztZQUNBLE1BQU1DOzhDQUFhO29CQUNmLHVEQUF1RDtvQkFDdkQsd0dBQXdHO29CQUN4RyxtRkFBbUY7b0JBQ25GLElBQUl4QyxjQUFjd0IsT0FBTyxLQUFLVSxVQUFVO29CQUN4Q3BCLHVCQUF1QlUsT0FBTyxHQUFHLElBQUljLE9BQU9DLE9BQU87b0JBQ25ELG9DQUFvQztvQkFDcENKLFlBQVlGO3NEQUFXOzRCQUNuQjFKLE1BQU1rSyxXQUFXLElBQUksT0FBTyxLQUFLLElBQUlsSyxNQUFNa0ssV0FBVyxDQUFDN0gsSUFBSSxDQUFDckMsT0FBT0E7NEJBQ25Fd0o7d0JBQ0o7cURBQUcvQixjQUFjd0IsT0FBTztnQkFDNUI7O1lBQ0EsSUFBSXpELFlBQVlKLGVBQWVwSCxrQkFBa0I7Z0JBQzdDNkw7WUFDSixPQUFPO2dCQUNISTtZQUNKO1lBQ0E7bUNBQU8sSUFBSUUsYUFBYVA7O1FBQzVCOzBCQUFHO1FBQ0NwRTtRQUNBSjtRQUNBcEY7UUFDQStIO1FBQ0EvSjtRQUNBd0w7S0FDSDtJQUNEN04sNENBQWU7MkJBQUM7WUFDWixJQUFJcUUsTUFBTUssTUFBTSxFQUFFO2dCQUNkbUo7WUFDSjtRQUNKOzBCQUFHO1FBQ0NBO1FBQ0F4SixNQUFNSyxNQUFNO0tBQ2Y7SUFDRCxTQUFTK0o7UUFDTCxJQUFJNUY7UUFDSixJQUFJNkIsU0FBUyxPQUFPLEtBQUssSUFBSUEsTUFBTXZGLE9BQU8sRUFBRTtZQUN4QyxJQUFJMkQ7WUFDSixPQUFPLFdBQVcsR0FBRzlJLGdEQUFtQixDQUFDLE9BQU87Z0JBQzVDWSxXQUFXdUgsR0FBR3NDLGNBQWMsT0FBTyxLQUFLLElBQUlBLFdBQVdpRSxNQUFNLEVBQUVySyxTQUFTLE9BQU8sS0FBSyxJQUFJLENBQUN5RSxxQkFBcUJ6RSxNQUFNb0csVUFBVSxLQUFLLE9BQU8sS0FBSyxJQUFJM0IsbUJBQW1CNEYsTUFBTSxFQUFFO2dCQUM5SyxnQkFBZ0J0QyxjQUFjO1lBQ2xDLEdBQUcxQixNQUFNdkYsT0FBTztRQUNwQjtRQUNBLE9BQU8sV0FBVyxHQUFHbkYsZ0RBQW1CLENBQUNVLFFBQVE7WUFDN0NFLFdBQVd1SCxHQUFHc0MsY0FBYyxPQUFPLEtBQUssSUFBSUEsV0FBV2lFLE1BQU0sRUFBRXJLLFNBQVMsT0FBTyxLQUFLLElBQUksQ0FBQ3dFLG9CQUFvQnhFLE1BQU1vRyxVQUFVLEtBQUssT0FBTyxLQUFLLElBQUk1QixrQkFBa0I2RixNQUFNO1lBQzFLL04sU0FBU3lMLGNBQWM7UUFDM0I7SUFDSjtJQUNBLE1BQU11QyxPQUFPdEssTUFBTXNLLElBQUksSUFBS2pFLENBQUFBLFNBQVMsT0FBTyxLQUFLLElBQUlBLEtBQUssQ0FBQzBCLFVBQVUsS0FBS2xNLFNBQVNrTTtJQUNuRixJQUFJd0MsbUJBQW1CQztJQUN2QixPQUFPLFdBQVcsR0FBRzdPLGdEQUFtQixDQUFDLE1BQU07UUFDM0M4TyxVQUFVO1FBQ1ZDLEtBQUs5QztRQUNMckwsV0FBV3VILEdBQUd2SCxXQUFXeUwsZ0JBQWdCNUIsY0FBYyxPQUFPLEtBQUssSUFBSUEsV0FBV3BHLEtBQUssRUFBRUEsU0FBUyxPQUFPLEtBQUssSUFBSSxDQUFDd0Usb0JBQW9CeEUsTUFBTW9HLFVBQVUsS0FBSyxPQUFPLEtBQUssSUFBSTVCLGtCQUFrQnhFLEtBQUssRUFBRW9HLGNBQWMsT0FBTyxLQUFLLElBQUlBLFdBQVd1RSxPQUFPLEVBQUV2RSxjQUFjLE9BQU8sS0FBSyxJQUFJQSxVQUFVLENBQUMyQixVQUFVLEVBQUUvSCxTQUFTLE9BQU8sS0FBSyxJQUFJLENBQUN5RSxxQkFBcUJ6RSxNQUFNb0csVUFBVSxLQUFLLE9BQU8sS0FBSyxJQUFJM0Isa0JBQWtCLENBQUNzRCxVQUFVO1FBQzdaLHFCQUFxQjtRQUNyQixvQkFBb0IsQ0FBQ3dDLG9CQUFvQnZLLE1BQU00SyxVQUFVLEtBQUssT0FBT0wsb0JBQW9CN0U7UUFDekYsZUFBZSxDQUFDakosUUFBUXVELE1BQU0yQyxHQUFHLElBQUkzQyxNQUFNbUYsUUFBUSxJQUFJQTtRQUN2RCxnQkFBZ0J3QjtRQUNoQixnQkFBZ0JsSyxRQUFRdUQsTUFBTWUsT0FBTztRQUNyQyxlQUFlb0c7UUFDZixnQkFBZ0JOO1FBQ2hCLGdCQUFnQmlCO1FBQ2hCLG1CQUFtQjVEO1FBQ25CLG1CQUFtQkM7UUFDbkIsY0FBY25GO1FBQ2QsY0FBYzZJO1FBQ2QsZ0JBQWdCZDtRQUNoQixvQkFBb0I5RztRQUNwQixhQUFhOEg7UUFDYixlQUFlOUM7UUFDZixrQkFBa0JnQztRQUNsQix3QkFBd0JSO1FBQ3hCLGlCQUFpQmhLLFFBQVErSSxZQUFZVyxtQkFBbUJRO1FBQ3hEdkwsT0FBTztZQUNILFdBQVc0RDtZQUNYLG1CQUFtQkE7WUFDbkIsYUFBYU8sT0FBT00sTUFBTSxHQUFHYjtZQUM3QixZQUFZLEdBQUc2SCxVQUFVUSxxQkFBcUJtQixPQUFPUyxPQUFPLENBQUMsRUFBRSxDQUFDO1lBQ2hFLG9CQUFvQjlDLGtCQUFrQixTQUFTLEdBQUdvQixjQUFjLEVBQUUsQ0FBQztZQUNuRSxHQUFHbk0sS0FBSztZQUNSLEdBQUc0RSxNQUFNNUUsS0FBSztRQUNsQjtRQUNBeVAsV0FBVztZQUNQN0QsV0FBVztZQUNYUixrQkFBa0I7WUFDbEJrQyxnQkFBZ0JPLE9BQU8sR0FBRztRQUM5QjtRQUNBNkIsZUFBZSxDQUFDQztZQUNaLElBQUkvQixZQUFZLENBQUMvSSxhQUFhO1lBQzlCMEgsY0FBY3NCLE9BQU8sR0FBRyxJQUFJYztZQUM1QnpDLHNCQUFzQmtCLE9BQU9TLE9BQU87WUFDcEMsc0dBQXNHO1lBQ3RHOEIsTUFBTUMsTUFBTSxDQUFDQyxpQkFBaUIsQ0FBQ0YsTUFBTUcsU0FBUztZQUM5QyxJQUFJSCxNQUFNQyxNQUFNLENBQUNHLE9BQU8sS0FBSyxVQUFVO1lBQ3ZDbkUsV0FBVztZQUNYMEIsZ0JBQWdCTyxPQUFPLEdBQUc7Z0JBQ3RCOUUsR0FBRzRHLE1BQU1LLE9BQU87Z0JBQ2hCbEgsR0FBRzZHLE1BQU1NLE9BQU87WUFDcEI7UUFDSjtRQUNBQyxhQUFhO1lBQ1QsSUFBSUMsbUJBQW1CQyxvQkFBb0JDO1lBQzNDLElBQUl4RSxZQUFZLENBQUNoSCxhQUFhO1lBQzlCeUksZ0JBQWdCTyxPQUFPLEdBQUc7WUFDMUIsTUFBTXlDLGVBQWVDLE9BQU8sQ0FBQyxDQUFDSixvQkFBb0IzRCxTQUFTcUIsT0FBTyxLQUFLLE9BQU8sS0FBSyxJQUFJc0Msa0JBQWtCblEsS0FBSyxDQUFDd1EsZ0JBQWdCLENBQUMsb0JBQW9CQyxPQUFPLENBQUMsTUFBTSxHQUFFLEtBQU07WUFDMUssTUFBTUMsZUFBZUgsT0FBTyxDQUFDLENBQUNILHFCQUFxQjVELFNBQVNxQixPQUFPLEtBQUssT0FBTyxLQUFLLElBQUl1QyxtQkFBbUJwUSxLQUFLLENBQUN3USxnQkFBZ0IsQ0FBQyxvQkFBb0JDLE9BQU8sQ0FBQyxNQUFNLEdBQUUsS0FBTTtZQUM1SyxNQUFNRSxZQUFZLElBQUloQyxPQUFPQyxPQUFPLEtBQU0sRUFBQ3lCLHlCQUF5QjlELGNBQWNzQixPQUFPLEtBQUssT0FBTyxLQUFLLElBQUl3Qyx1QkFBdUJ6QixPQUFPLEVBQUM7WUFDN0ksTUFBTWdDLGNBQWN6RixtQkFBbUIsTUFBTW1GLGVBQWVJO1lBQzVELE1BQU1HLFdBQVdDLEtBQUtDLEdBQUcsQ0FBQ0gsZUFBZUQ7WUFDekMsSUFBSUcsS0FBS0MsR0FBRyxDQUFDSCxnQkFBZ0JwSSxtQkFBbUJxSSxXQUFXLE1BQU07Z0JBQzdEM0Usc0JBQXNCa0IsT0FBT1MsT0FBTztnQkFDcENqSixNQUFNb00sU0FBUyxJQUFJLE9BQU8sS0FBSyxJQUFJcE0sTUFBTW9NLFNBQVMsQ0FBQy9KLElBQUksQ0FBQ3JDLE9BQU9BO2dCQUMvRCxJQUFJdUcsbUJBQW1CLEtBQUs7b0JBQ3hCRyxxQkFBcUJnRixlQUFlLElBQUksVUFBVTtnQkFDdEQsT0FBTztvQkFDSGhGLHFCQUFxQm9GLGVBQWUsSUFBSSxTQUFTO2dCQUNyRDtnQkFDQXRDO2dCQUNBdEMsWUFBWTtnQkFDWjtZQUNKLE9BQU87Z0JBQ0gsSUFBSW1GLG9CQUFvQkM7Z0JBQ3ZCRCxDQUFBQSxxQkFBcUJ6RSxTQUFTcUIsT0FBTyxLQUFLLE9BQU8sS0FBSyxJQUFJb0QsbUJBQW1CalIsS0FBSyxDQUFDbVIsV0FBVyxDQUFDLG9CQUFvQixDQUFDLEdBQUcsQ0FBQztnQkFDeEhELENBQUFBLHFCQUFxQjFFLFNBQVNxQixPQUFPLEtBQUssT0FBTyxLQUFLLElBQUlxRCxtQkFBbUJsUixLQUFLLENBQUNtUixXQUFXLENBQUMsb0JBQW9CLENBQUMsR0FBRyxDQUFDO1lBQzdIO1lBQ0FuRixZQUFZO1lBQ1pKLFdBQVc7WUFDWFIsa0JBQWtCO1FBQ3RCO1FBQ0FnRyxlQUFlLENBQUN6QjtZQUNaLElBQUkwQixzQkFDSmxCLG1CQUFtQkM7WUFDbkIsSUFBSSxDQUFDOUMsZ0JBQWdCTyxPQUFPLElBQUksQ0FBQ2hKLGFBQWE7WUFDOUMsTUFBTXlNLGdCQUFnQixDQUFDLENBQUNELHVCQUF1QmxPLE9BQU9vTyxZQUFZLEVBQUMsS0FBTSxPQUFPLEtBQUssSUFBSUYscUJBQXFCRyxRQUFRLEdBQUcvTSxNQUFNLElBQUk7WUFDbkksSUFBSTZNLGVBQWU7WUFDbkIsTUFBTUcsU0FBUzlCLE1BQU1NLE9BQU8sR0FBRzNDLGdCQUFnQk8sT0FBTyxDQUFDL0UsQ0FBQztZQUN4RCxNQUFNNEksU0FBUy9CLE1BQU1LLE9BQU8sR0FBRzFDLGdCQUFnQk8sT0FBTyxDQUFDOUUsQ0FBQztZQUN4RCxJQUFJNEk7WUFDSixNQUFNQyxrQkFBa0IsQ0FBQ0QseUJBQXlCeEksTUFBTXlJLGVBQWUsS0FBSyxPQUFPRCx5QkFBeUIvSSwwQkFBMEJDO1lBQ3RJLGtEQUFrRDtZQUNsRCxJQUFJLENBQUNzQyxrQkFBbUIyRixDQUFBQSxLQUFLQyxHQUFHLENBQUNXLFVBQVUsS0FBS1osS0FBS0MsR0FBRyxDQUFDVSxVQUFVLElBQUk7Z0JBQ25Fckcsa0JBQWtCMEYsS0FBS0MsR0FBRyxDQUFDVyxVQUFVWixLQUFLQyxHQUFHLENBQUNVLFVBQVUsTUFBTTtZQUNsRTtZQUNBLElBQUliLGNBQWM7Z0JBQ2Q3SCxHQUFHO2dCQUNIRCxHQUFHO1lBQ1A7WUFDQSxNQUFNK0ksZUFBZSxDQUFDQztnQkFDbEIsTUFBTUMsU0FBU2pCLEtBQUtDLEdBQUcsQ0FBQ2UsU0FBUztnQkFDakMsT0FBTyxJQUFLLE9BQU1DLE1BQUs7WUFDM0I7WUFDQSwyQ0FBMkM7WUFDM0MsSUFBSTVHLG1CQUFtQixLQUFLO2dCQUN4Qix5QkFBeUI7Z0JBQ3pCLElBQUl5RyxnQkFBZ0JJLFFBQVEsQ0FBQyxVQUFVSixnQkFBZ0JJLFFBQVEsQ0FBQyxXQUFXO29CQUN2RSxJQUFJSixnQkFBZ0JJLFFBQVEsQ0FBQyxVQUFVUCxTQUFTLEtBQUtHLGdCQUFnQkksUUFBUSxDQUFDLGFBQWFQLFNBQVMsR0FBRzt3QkFDbkdiLFlBQVk5SCxDQUFDLEdBQUcySTtvQkFDcEIsT0FBTzt3QkFDSCwyQ0FBMkM7d0JBQzNDLE1BQU1RLGdCQUFnQlIsU0FBU0ksYUFBYUo7d0JBQzVDLCtEQUErRDt3QkFDL0RiLFlBQVk5SCxDQUFDLEdBQUdnSSxLQUFLQyxHQUFHLENBQUNrQixpQkFBaUJuQixLQUFLQyxHQUFHLENBQUNVLFVBQVVRLGdCQUFnQlI7b0JBQ2pGO2dCQUNKO1lBQ0osT0FBTyxJQUFJdEcsbUJBQW1CLEtBQUs7Z0JBQy9CLDJCQUEyQjtnQkFDM0IsSUFBSXlHLGdCQUFnQkksUUFBUSxDQUFDLFdBQVdKLGdCQUFnQkksUUFBUSxDQUFDLFVBQVU7b0JBQ3ZFLElBQUlKLGdCQUFnQkksUUFBUSxDQUFDLFdBQVdOLFNBQVMsS0FBS0UsZ0JBQWdCSSxRQUFRLENBQUMsWUFBWU4sU0FBUyxHQUFHO3dCQUNuR2QsWUFBWTdILENBQUMsR0FBRzJJO29CQUNwQixPQUFPO3dCQUNILDJDQUEyQzt3QkFDM0MsTUFBTU8sZ0JBQWdCUCxTQUFTRyxhQUFhSDt3QkFDNUMsK0RBQStEO3dCQUMvRGQsWUFBWTdILENBQUMsR0FBRytILEtBQUtDLEdBQUcsQ0FBQ2tCLGlCQUFpQm5CLEtBQUtDLEdBQUcsQ0FBQ1csVUFBVU8sZ0JBQWdCUDtvQkFDakY7Z0JBQ0o7WUFDSjtZQUNBLElBQUlaLEtBQUtDLEdBQUcsQ0FBQ0gsWUFBWTdILENBQUMsSUFBSSxLQUFLK0gsS0FBS0MsR0FBRyxDQUFDSCxZQUFZOUgsQ0FBQyxJQUFJLEdBQUc7Z0JBQzVEa0QsWUFBWTtZQUNoQjtZQUNDbUUsQ0FBQUEsb0JBQW9CM0QsU0FBU3FCLE9BQU8sS0FBSyxPQUFPLEtBQUssSUFBSXNDLGtCQUFrQm5RLEtBQUssQ0FBQ21SLFdBQVcsQ0FBQyxvQkFBb0IsR0FBR1AsWUFBWTdILENBQUMsQ0FBQyxFQUFFLENBQUM7WUFDcklxSCxDQUFBQSxxQkFBcUI1RCxTQUFTcUIsT0FBTyxLQUFLLE9BQU8sS0FBSyxJQUFJdUMsbUJBQW1CcFEsS0FBSyxDQUFDbVIsV0FBVyxDQUFDLG9CQUFvQixHQUFHUCxZQUFZOUgsQ0FBQyxDQUFDLEVBQUUsQ0FBQztRQUM1STtJQUNKLEdBQUd5QixlQUFlLENBQUMzRixNQUFNMkMsR0FBRyxJQUFJb0YsY0FBYyxZQUFZLFdBQVcsR0FBR3BNLGdEQUFtQixDQUFDLFVBQVU7UUFDbEcsY0FBYzJLO1FBQ2QsaUJBQWlCMEM7UUFDakIscUJBQXFCO1FBQ3JCc0UsU0FBU3RFLFlBQVksQ0FBQy9JLGNBQWMsS0FBSyxJQUFJO1lBQ3pDdUo7WUFDQXhKLE1BQU1vTSxTQUFTLElBQUksT0FBTyxLQUFLLElBQUlwTSxNQUFNb00sU0FBUyxDQUFDL0osSUFBSSxDQUFDckMsT0FBT0E7UUFDbkU7UUFDQXpELFdBQVd1SCxHQUFHc0MsY0FBYyxPQUFPLEtBQUssSUFBSUEsV0FBV1QsV0FBVyxFQUFFM0YsU0FBUyxPQUFPLEtBQUssSUFBSSxDQUFDMEUscUJBQXFCMUUsTUFBTW9HLFVBQVUsS0FBSyxPQUFPLEtBQUssSUFBSTFCLG1CQUFtQmlCLFdBQVc7SUFDMUwsR0FBRyxDQUFDNkUsZUFBZW5FLFNBQVMsT0FBTyxLQUFLLElBQUlBLE1BQU1rSCxLQUFLLEtBQUssT0FBTy9DLGVBQWVsTixhQUFhLE1BQU0sQ0FBQ3lLLGFBQWEvSCxNQUFNc0ssSUFBSSxJQUFJdEssTUFBTWUsT0FBTyxLQUFLZixNQUFNc0ssSUFBSSxLQUFLLFFBQVMsRUFBQ2pFLFNBQVMsT0FBTyxLQUFLLElBQUlBLEtBQUssQ0FBQzBCLFVBQVUsTUFBTSxRQUFRL0gsTUFBTXNLLElBQUksSUFBSSxXQUFXLEdBQUczTyxnREFBbUIsQ0FBQyxPQUFPO1FBQ3RSLGFBQWE7UUFDYlksV0FBV3VILEdBQUdzQyxjQUFjLE9BQU8sS0FBSyxJQUFJQSxXQUFXa0UsSUFBSSxFQUFFdEssU0FBUyxPQUFPLEtBQUssSUFBSSxDQUFDMkUscUJBQXFCM0UsTUFBTW9HLFVBQVUsS0FBSyxPQUFPLEtBQUssSUFBSXpCLG1CQUFtQjJGLElBQUk7SUFDNUssR0FBR3RLLE1BQU1lLE9BQU8sSUFBSWYsTUFBTTFFLElBQUksS0FBSyxhQUFhLENBQUMwRSxNQUFNc0ssSUFBSSxHQUFHdEssTUFBTXNLLElBQUksSUFBSUYsbUJBQW1CLE1BQU1wSyxNQUFNMUUsSUFBSSxLQUFLLFlBQVlnUCxPQUFPLFFBQVEsTUFBTSxXQUFXLEdBQUczTyxnREFBbUIsQ0FBQyxPQUFPO1FBQzFMLGdCQUFnQjtRQUNoQlksV0FBV3VILEdBQUdzQyxjQUFjLE9BQU8sS0FBSyxJQUFJQSxXQUFXb0gsT0FBTyxFQUFFeE4sU0FBUyxPQUFPLEtBQUssSUFBSSxDQUFDNEUscUJBQXFCNUUsTUFBTW9HLFVBQVUsS0FBSyxPQUFPLEtBQUssSUFBSXhCLG1CQUFtQjRJLE9BQU87SUFDbEwsR0FBRyxXQUFXLEdBQUc3UixnREFBbUIsQ0FBQyxPQUFPO1FBQ3hDLGNBQWM7UUFDZFksV0FBV3VILEdBQUdzQyxjQUFjLE9BQU8sS0FBSyxJQUFJQSxXQUFXOUYsS0FBSyxFQUFFTixTQUFTLE9BQU8sS0FBSyxJQUFJLENBQUM2RSxxQkFBcUI3RSxNQUFNb0csVUFBVSxLQUFLLE9BQU8sS0FBSyxJQUFJdkIsbUJBQW1CdkUsS0FBSztJQUM5SyxHQUFHTixNQUFNMkMsR0FBRyxHQUFHM0MsTUFBTTJDLEdBQUcsR0FBRyxPQUFPM0MsTUFBTU0sS0FBSyxLQUFLLGFBQWFOLE1BQU1NLEtBQUssS0FBS04sTUFBTU0sS0FBSyxHQUFHTixNQUFNZ0IsV0FBVyxHQUFHLFdBQVcsR0FBR3JGLGdEQUFtQixDQUFDLE9BQU87UUFDdEosb0JBQW9CO1FBQ3BCWSxXQUFXdUgsR0FBR2lDLHNCQUFzQmtDLDJCQUEyQjdCLGNBQWMsT0FBTyxLQUFLLElBQUlBLFdBQVdwRixXQUFXLEVBQUVoQixTQUFTLE9BQU8sS0FBSyxJQUFJLENBQUM4RSxxQkFBcUI5RSxNQUFNb0csVUFBVSxLQUFLLE9BQU8sS0FBSyxJQUFJdEIsbUJBQW1COUQsV0FBVztJQUMzTyxHQUFHLE9BQU9oQixNQUFNZ0IsV0FBVyxLQUFLLGFBQWFoQixNQUFNZ0IsV0FBVyxLQUFLaEIsTUFBTWdCLFdBQVcsSUFBSSxPQUFPLFdBQVcsR0FBR3JGLGlEQUFvQixDQUFDcUUsTUFBTXlOLE1BQU0sSUFBSXpOLE1BQU15TixNQUFNLEdBQUd6TixNQUFNeU4sTUFBTSxJQUFJdEssU0FBU25ELE1BQU15TixNQUFNLElBQUksV0FBVyxHQUFHOVIsZ0RBQW1CLENBQUMsVUFBVTtRQUNsUCxlQUFlO1FBQ2YsZUFBZTtRQUNmUCxPQUFPNEUsTUFBTTZGLGlCQUFpQixJQUFJQTtRQUNsQ3lILFNBQVMsQ0FBQ3ZDO1lBQ04sNENBQTRDO1lBQzVDLElBQUksQ0FBQzVILFNBQVNuRCxNQUFNeU4sTUFBTSxHQUFHO1lBQzdCLElBQUksQ0FBQ3hOLGFBQWE7WUFDbEJELE1BQU15TixNQUFNLENBQUNILE9BQU8sSUFBSSxPQUFPLEtBQUssSUFBSXROLE1BQU15TixNQUFNLENBQUNILE9BQU8sQ0FBQ2pMLElBQUksQ0FBQ3JDLE1BQU15TixNQUFNLEVBQUUxQztZQUNoRnZCO1FBQ0o7UUFDQWpOLFdBQVd1SCxHQUFHc0MsY0FBYyxPQUFPLEtBQUssSUFBSUEsV0FBV3NILFlBQVksRUFBRTFOLFNBQVMsT0FBTyxLQUFLLElBQUksQ0FBQytFLHFCQUFxQi9FLE1BQU1vRyxVQUFVLEtBQUssT0FBTyxLQUFLLElBQUlyQixtQkFBbUIySSxZQUFZO0lBQzVMLEdBQUcxTixNQUFNeU4sTUFBTSxDQUFDcEssS0FBSyxJQUFJLE1BQU0sV0FBVyxHQUFHMUgsaURBQW9CLENBQUNxRSxNQUFNb0QsTUFBTSxJQUFJcEQsTUFBTW9ELE1BQU0sR0FBR3BELE1BQU1vRCxNQUFNLElBQUlELFNBQVNuRCxNQUFNb0QsTUFBTSxJQUFJLFdBQVcsR0FBR3pILGdEQUFtQixDQUFDLFVBQVU7UUFDbEwsZUFBZTtRQUNmLGVBQWU7UUFDZlAsT0FBTzRFLE1BQU04RixpQkFBaUIsSUFBSUE7UUFDbEN3SCxTQUFTLENBQUN2QztZQUNOLDRDQUE0QztZQUM1QyxJQUFJLENBQUM1SCxTQUFTbkQsTUFBTW9ELE1BQU0sR0FBRztZQUM3QnBELE1BQU1vRCxNQUFNLENBQUNrSyxPQUFPLElBQUksT0FBTyxLQUFLLElBQUl0TixNQUFNb0QsTUFBTSxDQUFDa0ssT0FBTyxDQUFDakwsSUFBSSxDQUFDckMsTUFBTW9ELE1BQU0sRUFBRTJIO1lBQ2hGLElBQUlBLE1BQU00QyxnQkFBZ0IsRUFBRTtZQUM1Qm5FO1FBQ0o7UUFDQWpOLFdBQVd1SCxHQUFHc0MsY0FBYyxPQUFPLEtBQUssSUFBSUEsV0FBV3dILFlBQVksRUFBRTVOLFNBQVMsT0FBTyxLQUFLLElBQUksQ0FBQ2dGLHFCQUFxQmhGLE1BQU1vRyxVQUFVLEtBQUssT0FBTyxLQUFLLElBQUlwQixtQkFBbUI0SSxZQUFZO0lBQzVMLEdBQUc1TixNQUFNb0QsTUFBTSxDQUFDQyxLQUFLLElBQUk7QUFDN0I7QUFDQSxTQUFTd0s7SUFDTCxJQUFJLElBQTZCLEVBQUUsT0FBTztJQUMxQyxJQUFJLE9BQU81UyxhQUFhLGFBQWEsT0FBTyxPQUFPLG9CQUFvQjtJQUN2RSxNQUFNNlMsZUFBZTdTLFNBQVM4UyxlQUFlLENBQUNDLFlBQVksQ0FBQztJQUMzRCxJQUFJRixpQkFBaUIsVUFBVSxDQUFDQSxjQUFjO1FBQzFDLE9BQU92UCxPQUFPMFAsZ0JBQWdCLENBQUNoVCxTQUFTOFMsZUFBZSxFQUFFRyxTQUFTO0lBQ3RFO0lBQ0EsT0FBT0o7QUFDWDtBQUNBLFNBQVNLLGFBQWFDLGFBQWEsRUFBRUMsWUFBWTtJQUM3QyxNQUFNQyxTQUFTLENBQUM7SUFDaEI7UUFDSUY7UUFDQUM7S0FDSCxDQUFDaFAsT0FBTyxDQUFDLENBQUNtSixRQUFReEo7UUFDZixNQUFNdVAsV0FBV3ZQLFVBQVU7UUFDM0IsTUFBTXdQLFNBQVNELFdBQVcsb0JBQW9CO1FBQzlDLE1BQU1FLGVBQWVGLFdBQVcvSyx5QkFBeUJEO1FBQ3pELFNBQVNtTCxVQUFVbEcsTUFBTTtZQUNyQjtnQkFDSTtnQkFDQTtnQkFDQTtnQkFDQTthQUNILENBQUNuSixPQUFPLENBQUMsQ0FBQ3ZDO2dCQUNQd1IsTUFBTSxDQUFDLEdBQUdFLE9BQU8sQ0FBQyxFQUFFMVIsS0FBSyxDQUFDLEdBQUcsT0FBTzBMLFdBQVcsV0FBVyxHQUFHQSxPQUFPLEVBQUUsQ0FBQyxHQUFHQTtZQUM5RTtRQUNKO1FBQ0EsSUFBSSxPQUFPQSxXQUFXLFlBQVksT0FBT0EsV0FBVyxVQUFVO1lBQzFEa0csVUFBVWxHO1FBQ2QsT0FBTyxJQUFJLE9BQU9BLFdBQVcsVUFBVTtZQUNuQztnQkFDSTtnQkFDQTtnQkFDQTtnQkFDQTthQUNILENBQUNuSixPQUFPLENBQUMsQ0FBQ3ZDO2dCQUNQLElBQUkwTCxNQUFNLENBQUMxTCxJQUFJLEtBQUtvRCxXQUFXO29CQUMzQm9PLE1BQU0sQ0FBQyxHQUFHRSxPQUFPLENBQUMsRUFBRTFSLEtBQUssQ0FBQyxHQUFHMlI7Z0JBQ2pDLE9BQU87b0JBQ0hILE1BQU0sQ0FBQyxHQUFHRSxPQUFPLENBQUMsRUFBRTFSLEtBQUssQ0FBQyxHQUFHLE9BQU8wTCxNQUFNLENBQUMxTCxJQUFJLEtBQUssV0FBVyxHQUFHMEwsTUFBTSxDQUFDMUwsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHMEwsTUFBTSxDQUFDMUwsSUFBSTtnQkFDbkc7WUFDSjtRQUNKLE9BQU87WUFDSDRSLFVBQVVEO1FBQ2Q7SUFDSjtJQUNBLE9BQU9IO0FBQ1g7QUFDQSxTQUFTSztJQUNMLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdsVCwyQ0FBYyxDQUFDLEVBQUU7SUFDekRBLDRDQUFlOytCQUFDO1lBQ1osT0FBT21ILFdBQVdsRSxTQUFTO3VDQUFDLENBQUNvQjtvQkFDekIsSUFBSUEsTUFBTU8sT0FBTyxFQUFFO3dCQUNmbUo7bURBQVc7Z0NBQ1A5TixnREFBa0I7MkRBQUM7d0NBQ2ZpVDttRUFBZ0IsQ0FBQ3RQLFNBQVNBLE9BQU8vQyxNQUFNOzJFQUFDLENBQUN1UyxJQUFJQSxFQUFFblAsRUFBRSxLQUFLSSxNQUFNSixFQUFFOzs7b0NBQ2xFOzs0QkFDSjs7d0JBQ0E7b0JBQ0o7b0JBQ0EsbUNBQW1DO29CQUNuQzhKOytDQUFXOzRCQUNQOU4sZ0RBQWtCO3VEQUFDO29DQUNmaVQ7K0RBQWdCLENBQUN0UDs0Q0FDYixNQUFNeVAsdUJBQXVCelAsT0FBTzZJLFNBQVM7NEZBQUMsQ0FBQzJHLElBQUlBLEVBQUVuUCxFQUFFLEtBQUtJLE1BQU1KLEVBQUU7OzRDQUNwRSx3Q0FBd0M7NENBQ3hDLElBQUlvUCx5QkFBeUIsQ0FBQyxHQUFHO2dEQUM3QixPQUFPO3VEQUNBelAsT0FBTzBQLEtBQUssQ0FBQyxHQUFHRDtvREFDbkI7d0RBQ0ksR0FBR3pQLE1BQU0sQ0FBQ3lQLHFCQUFxQjt3REFDL0IsR0FBR2hQLEtBQUs7b0RBQ1o7dURBQ0dULE9BQU8wUCxLQUFLLENBQUNELHVCQUF1QjtpREFDMUM7NENBQ0w7NENBQ0EsT0FBTztnREFDSGhQO21EQUNHVDs2Q0FDTjt3Q0FDTDs7Z0NBQ0o7O3dCQUNKOztnQkFDSjs7UUFDSjs4QkFBRyxFQUFFO0lBQ0wsT0FBTztRQUNIQSxRQUFRcVA7SUFDWjtBQUNKO0FBQ0EsTUFBTU0sVUFBVSxXQUFXLEdBQUd2VCw2Q0FBZ0IsQ0FBQyxTQUFTdVQsUUFBUTNLLEtBQUssRUFBRW1HLEdBQUc7SUFDdEUsTUFBTSxFQUFFekYsTUFBTSxFQUFFaEIsV0FBVyxjQUFjLEVBQUVtTCxTQUFTO1FBQ2hEO1FBQ0E7S0FDSCxFQUFFQyxNQUFNLEVBQUUxSixXQUFXLEVBQUVwSixTQUFTLEVBQUVpTSxNQUFNLEVBQUU2RixZQUFZLEVBQUVpQixRQUFRLE9BQU8sRUFBRTFFLFVBQVUsRUFBRTVFLFFBQVEsRUFBRTVLLEtBQUssRUFBRWtLLGdCQUFnQmhDLHFCQUFxQixFQUFFaU0sWUFBWSxFQUFFQyxNQUFNM0Isc0JBQXNCLEVBQUUzSCxNQUFNdkMsR0FBRyxFQUFFMEMsS0FBSyxFQUFFb0oscUJBQXFCLGVBQWUsRUFBRSxHQUFHbEw7SUFDclAsTUFBTSxDQUFDaEYsUUFBUW1RLFVBQVUsR0FBRy9ULDJDQUFjLENBQUMsRUFBRTtJQUM3QyxNQUFNZ1Usb0JBQW9CaFUsMENBQWE7c0RBQUM7WUFDcEMsT0FBT1EsTUFBTXlULElBQUksQ0FBQyxJQUFJL00sSUFBSTtnQkFDdEJvQjthQUNILENBQUM0TCxNQUFNLENBQUN0USxPQUFPL0MsTUFBTTs4REFBQyxDQUFDd0QsUUFBUUEsTUFBTWlFLFFBQVE7NkRBQUV0SCxHQUFHOzhEQUFDLENBQUNxRCxRQUFRQSxNQUFNaUUsUUFBUTs7UUFDL0U7cURBQUc7UUFDQzFFO1FBQ0EwRTtLQUNIO0lBQ0QsTUFBTSxDQUFDc0IsU0FBU0YsV0FBVyxHQUFHMUosMkNBQWMsQ0FBQyxFQUFFO0lBQy9DLE1BQU0sQ0FBQzZKLFVBQVVzSyxZQUFZLEdBQUduVSwyQ0FBYyxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3lKLGFBQWEySyxlQUFlLEdBQUdwVSwyQ0FBYyxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3FVLGFBQWFDLGVBQWUsR0FBR3RVLDJDQUFjLENBQUMyVCxVQUFVLFdBQVdBLFFBQVEsTUFBNkIsR0FBRy9RLENBQWlHLEdBQUc7SUFDdE4sTUFBTTZSLFVBQVV6VSx5Q0FBWSxDQUFDO0lBQzdCLE1BQU0wVSxjQUFjakIsT0FBTzFTLElBQUksQ0FBQyxLQUFLbVAsT0FBTyxDQUFDLFFBQVEsSUFBSUEsT0FBTyxDQUFDLFVBQVU7SUFDM0UsTUFBTXlFLHdCQUF3QjNVLHlDQUFZLENBQUM7SUFDM0MsTUFBTTRVLG1CQUFtQjVVLHlDQUFZLENBQUM7SUFDdEMsTUFBTThKLGNBQWM5Siw4Q0FBaUI7b0RBQUMsQ0FBQzZVO1lBQ25DZDs0REFBVSxDQUFDblE7b0JBQ1AsSUFBSWtSO29CQUNKLElBQUksQ0FBRSxFQUFDQSxlQUFlbFIsT0FBT1EsSUFBSTtvRUFBQyxDQUFDQyxRQUFRQSxNQUFNSixFQUFFLEtBQUs0USxjQUFjNVEsRUFBRTtrRUFBQSxLQUFNLE9BQU8sS0FBSyxJQUFJNlEsYUFBYXBRLE1BQU0sR0FBRzt3QkFDaEh5QyxXQUFXdkMsT0FBTyxDQUFDaVEsY0FBYzVRLEVBQUU7b0JBQ3ZDO29CQUNBLE9BQU9MLE9BQU8vQyxNQUFNO29FQUFDLENBQUMsRUFBRW9ELEVBQUUsRUFBRSxHQUFHQSxPQUFPNFEsY0FBYzVRLEVBQUU7O2dCQUMxRDs7UUFDSjttREFBRyxFQUFFO0lBQ0xqRSw0Q0FBZTtxQ0FBQztZQUNaLE9BQU9tSCxXQUFXbEUsU0FBUzs2Q0FBQyxDQUFDb0I7b0JBQ3pCLElBQUlBLE1BQU1PLE9BQU8sRUFBRTt3QkFDZiwwQ0FBMEM7d0JBQzFDRTt5REFBc0I7Z0NBQ2xCaVA7aUVBQVUsQ0FBQ25RLFNBQVNBLE9BQU81QyxHQUFHO3lFQUFDLENBQUNvUyxJQUFJQSxFQUFFblAsRUFBRSxLQUFLSSxNQUFNSixFQUFFLEdBQUc7b0RBQzVDLEdBQUdtUCxDQUFDO29EQUNKMU8sUUFBUTtnREFDWixJQUFJME87Ozs0QkFDaEI7O3dCQUNBO29CQUNKO29CQUNBLG1DQUFtQztvQkFDbkNyRjtxREFBVzs0QkFDUDlOLGdEQUFrQjs2REFBQztvQ0FDZjhUO3FFQUFVLENBQUNuUTs0Q0FDUCxNQUFNeVAsdUJBQXVCelAsT0FBTzZJLFNBQVM7a0dBQUMsQ0FBQzJHLElBQUlBLEVBQUVuUCxFQUFFLEtBQUtJLE1BQU1KLEVBQUU7OzRDQUNwRSx3Q0FBd0M7NENBQ3hDLElBQUlvUCx5QkFBeUIsQ0FBQyxHQUFHO2dEQUM3QixPQUFPO3VEQUNBelAsT0FBTzBQLEtBQUssQ0FBQyxHQUFHRDtvREFDbkI7d0RBQ0ksR0FBR3pQLE1BQU0sQ0FBQ3lQLHFCQUFxQjt3REFDL0IsR0FBR2hQLEtBQUs7b0RBQ1o7dURBQ0dULE9BQU8wUCxLQUFLLENBQUNELHVCQUF1QjtpREFDMUM7NENBQ0w7NENBQ0EsT0FBTztnREFDSGhQO21EQUNHVDs2Q0FDTjt3Q0FDTDs7Z0NBQ0o7O3dCQUNKOztnQkFDSjs7UUFDSjtvQ0FBRztRQUNDQTtLQUNIO0lBQ0Q1RCw0Q0FBZTtxQ0FBQztZQUNaLElBQUkyVCxVQUFVLFVBQVU7Z0JBQ3BCVyxlQUFlWDtnQkFDZjtZQUNKO1lBQ0EsSUFBSUEsVUFBVSxVQUFVO2dCQUNwQixzQ0FBc0M7Z0JBQ3RDLElBQUkvUSxPQUFPMlIsVUFBVSxJQUFJM1IsT0FBTzJSLFVBQVUsQ0FBQyxnQ0FBZ0NDLE9BQU8sRUFBRTtvQkFDaEYsc0JBQXNCO29CQUN0QkYsZUFBZTtnQkFDbkIsT0FBTztvQkFDSCxnQkFBZ0I7b0JBQ2hCQSxlQUFlO2dCQUNuQjtZQUNKO1lBQ0EsSUFBSSxJQUE2QixFQUFFO1lBQ25DLE1BQU1TLGlCQUFpQm5TLE9BQU8yUixVQUFVLENBQUM7WUFDekMsSUFBSTtnQkFDQSxtQkFBbUI7Z0JBQ25CUSxlQUFlcFMsZ0JBQWdCLENBQUM7aURBQVUsQ0FBQyxFQUFFNlIsT0FBTyxFQUFFO3dCQUNsRCxJQUFJQSxTQUFTOzRCQUNURixlQUFlO3dCQUNuQixPQUFPOzRCQUNIQSxlQUFlO3dCQUNuQjtvQkFDSjs7WUFDSixFQUFFLE9BQU92UCxPQUFPO2dCQUNaLGNBQWM7Z0JBQ2RnUSxlQUFlQyxXQUFXO2lEQUFDLENBQUMsRUFBRVIsT0FBTyxFQUFFO3dCQUNuQyxJQUFJOzRCQUNBLElBQUlBLFNBQVM7Z0NBQ1RGLGVBQWU7NEJBQ25CLE9BQU87Z0NBQ0hBLGVBQWU7NEJBQ25CO3dCQUNKLEVBQUUsT0FBT1csR0FBRzs0QkFDUkMsUUFBUW5RLEtBQUssQ0FBQ2tRO3dCQUNsQjtvQkFDSjs7WUFDSjtRQUNKO29DQUFHO1FBQ0N0QjtLQUNIO0lBQ0QzVCw0Q0FBZTtxQ0FBQztZQUNaLDZFQUE2RTtZQUM3RSxJQUFJNEQsT0FBT00sTUFBTSxJQUFJLEdBQUc7Z0JBQ3BCaVEsWUFBWTtZQUNoQjtRQUNKO29DQUFHO1FBQ0N2UTtLQUNIO0lBQ0Q1RCw0Q0FBZTtxQ0FBQztZQUNaLE1BQU1tVjsyREFBZ0IsQ0FBQy9GO29CQUNuQixJQUFJZ0c7b0JBQ0osTUFBTUMsa0JBQWtCNUIsT0FBTzZCLEtBQUs7bUZBQUMsQ0FBQ25VLE1BQU1pTyxLQUFLLENBQUNqTyxJQUFJLElBQUlpTyxNQUFNL1AsSUFBSSxLQUFLOEI7O29CQUN6RSxJQUFJa1UsaUJBQWlCO3dCQUNqQixJQUFJRTt3QkFDSnBCLFlBQVk7d0JBQ1hvQixDQUFBQSxvQkFBb0JkLFFBQVFuSCxPQUFPLEtBQUssT0FBTyxLQUFLLElBQUlpSSxrQkFBa0JDLEtBQUs7b0JBQ3BGO29CQUNBLElBQUlwRyxNQUFNL1AsSUFBSSxLQUFLLFlBQWFDLENBQUFBLFNBQVNtVyxhQUFhLEtBQUtoQixRQUFRbkgsT0FBTyxJQUFLLEVBQUM4SCxtQkFBbUJYLFFBQVFuSCxPQUFPLEtBQUssT0FBTyxLQUFLLElBQUk4SCxpQkFBaUJNLFFBQVEsQ0FBQ3BXLFNBQVNtVyxhQUFhLEVBQUMsR0FBSTt3QkFDeEx0QixZQUFZO29CQUNoQjtnQkFDSjs7WUFDQTdVLFNBQVNxRCxnQkFBZ0IsQ0FBQyxXQUFXd1M7WUFDckM7NkNBQU8sSUFBSTdWLFNBQVN1RCxtQkFBbUIsQ0FBQyxXQUFXc1M7O1FBQ3ZEO29DQUFHO1FBQ0MxQjtLQUNIO0lBQ0R6VCw0Q0FBZTtxQ0FBQztZQUNaLElBQUl5VSxRQUFRbkgsT0FBTyxFQUFFO2dCQUNqQjtpREFBTzt3QkFDSCxJQUFJcUgsc0JBQXNCckgsT0FBTyxFQUFFOzRCQUMvQnFILHNCQUFzQnJILE9BQU8sQ0FBQ2tJLEtBQUssQ0FBQztnQ0FDaENHLGVBQWU7NEJBQ25COzRCQUNBaEIsc0JBQXNCckgsT0FBTyxHQUFHOzRCQUNoQ3NILGlCQUFpQnRILE9BQU8sR0FBRzt3QkFDL0I7b0JBQ0o7O1lBQ0o7UUFDSjtvQ0FBRztRQUNDbUgsUUFBUW5ILE9BQU87S0FDbEI7SUFDRCxPQUNBLFdBQVcsR0FBR3ROLGdEQUFtQixDQUFDLFdBQVc7UUFDekMrTyxLQUFLQTtRQUNMLGNBQWMsR0FBRytFLG1CQUFtQixDQUFDLEVBQUVZLGFBQWE7UUFDcEQ1RixVQUFVLENBQUM7UUFDWCxhQUFhO1FBQ2IsaUJBQWlCO1FBQ2pCLGVBQWU7UUFDZjhHLDBCQUEwQjtJQUM5QixHQUFHNUIsa0JBQWtCaFQsR0FBRyxDQUFDLENBQUNzSCxVQUFVakY7UUFDaEMsSUFBSXdTO1FBQ0osTUFBTSxDQUFDdE4sR0FBR0MsRUFBRSxHQUFHRixTQUFTRyxLQUFLLENBQUM7UUFDOUIsSUFBSSxDQUFDN0UsT0FBT00sTUFBTSxFQUFFLE9BQU87UUFDM0IsT0FBTyxXQUFXLEdBQUdsRSxnREFBbUIsQ0FBQyxNQUFNO1lBQzNDbUIsS0FBS21IO1lBQ0x1TCxLQUFLQSxRQUFRLFNBQVMzQix5QkFBeUIyQjtZQUMvQy9FLFVBQVUsQ0FBQztZQUNYQyxLQUFLMEY7WUFDTDdULFdBQVdBO1lBQ1gsdUJBQXVCO1lBQ3ZCLHFCQUFxQnlUO1lBQ3JCLG1CQUFtQjlMO1lBQ25CLGVBQWVzQixZQUFZakcsT0FBT00sTUFBTSxHQUFHLEtBQUssQ0FBQ3dQO1lBQ2pELG1CQUFtQmxMO1lBQ25CL0ksT0FBTztnQkFDSCx3QkFBd0IsR0FBRyxDQUFDLENBQUNvVyxZQUFZak0sT0FBTyxDQUFDLEVBQUUsS0FBSyxPQUFPLEtBQUssSUFBSWlNLFVBQVV2VSxNQUFNLEtBQUssRUFBRSxFQUFFLENBQUM7Z0JBQ2xHLFdBQVcsR0FBR3lHLFlBQVksRUFBRSxDQUFDO2dCQUM3QixTQUFTLEdBQUd3QyxJQUFJLEVBQUUsQ0FBQztnQkFDbkIsR0FBRzlLLEtBQUs7Z0JBQ1IsR0FBRytTLGFBQWEzRixRQUFRNkYsYUFBYTtZQUN6QztZQUNBb0QsUUFBUSxDQUFDMUc7Z0JBQ0wsSUFBSXdGLGlCQUFpQnRILE9BQU8sSUFBSSxDQUFDOEIsTUFBTTJHLGFBQWEsQ0FBQ0wsUUFBUSxDQUFDdEcsTUFBTTRHLGFBQWEsR0FBRztvQkFDaEZwQixpQkFBaUJ0SCxPQUFPLEdBQUc7b0JBQzNCLElBQUlxSCxzQkFBc0JySCxPQUFPLEVBQUU7d0JBQy9CcUgsc0JBQXNCckgsT0FBTyxDQUFDa0ksS0FBSyxDQUFDOzRCQUNoQ0csZUFBZTt3QkFDbkI7d0JBQ0FoQixzQkFBc0JySCxPQUFPLEdBQUc7b0JBQ3BDO2dCQUNKO1lBQ0o7WUFDQTJJLFNBQVMsQ0FBQzdHO2dCQUNOLE1BQU04RyxtQkFBbUI5RyxNQUFNQyxNQUFNLFlBQVk4RyxlQUFlL0csTUFBTUMsTUFBTSxDQUFDK0csT0FBTyxDQUFDOVIsV0FBVyxLQUFLO2dCQUNyRyxJQUFJNFIsa0JBQWtCO2dCQUN0QixJQUFJLENBQUN0QixpQkFBaUJ0SCxPQUFPLEVBQUU7b0JBQzNCc0gsaUJBQWlCdEgsT0FBTyxHQUFHO29CQUMzQnFILHNCQUFzQnJILE9BQU8sR0FBRzhCLE1BQU00RyxhQUFhO2dCQUN2RDtZQUNKO1lBQ0FLLGNBQWMsSUFBSWxDLFlBQVk7WUFDOUJtQyxhQUFhLElBQUluQyxZQUFZO1lBQzdCb0MsY0FBYztnQkFDViw4RUFBOEU7Z0JBQzlFLElBQUksQ0FBQzlNLGFBQWE7b0JBQ2QwSyxZQUFZO2dCQUNoQjtZQUNKO1lBQ0FqRixXQUFXLElBQUlpRixZQUFZO1lBQzNCaEYsZUFBZSxDQUFDQztnQkFDWixNQUFNOEcsbUJBQW1COUcsTUFBTUMsTUFBTSxZQUFZOEcsZUFBZS9HLE1BQU1DLE1BQU0sQ0FBQytHLE9BQU8sQ0FBQzlSLFdBQVcsS0FBSztnQkFDckcsSUFBSTRSLGtCQUFrQjtnQkFDdEI5QixlQUFlO1lBQ25CO1lBQ0F6RSxhQUFhLElBQUl5RSxlQUFlO1FBQ3BDLEdBQUd4USxPQUFPL0MsTUFBTSxDQUFDLENBQUN3RCxRQUFRLENBQUNBLE1BQU1pRSxRQUFRLElBQUlqRixVQUFVLEtBQUtnQixNQUFNaUUsUUFBUSxLQUFLQSxVQUFVdEgsR0FBRyxDQUFDLENBQUNxRCxPQUFPaEI7WUFDakcsSUFBSW1ULHdCQUF3QkM7WUFDNUIsT0FBTyxXQUFXLEdBQUd6VyxnREFBbUIsQ0FBQzJJLE9BQU87Z0JBQzVDeEgsS0FBS2tELE1BQU1KLEVBQUU7Z0JBQ2J5RyxPQUFPQTtnQkFDUHJILE9BQU9BO2dCQUNQZ0IsT0FBT0E7Z0JBQ1AwRixtQkFBbUJrRjtnQkFDbkI1RSxVQUFVLENBQUNtTSx5QkFBeUI1QyxnQkFBZ0IsT0FBTyxLQUFLLElBQUlBLGFBQWF2SixRQUFRLEtBQUssT0FBT21NLHlCQUF5Qm5NO2dCQUM5SHpKLFdBQVdnVCxnQkFBZ0IsT0FBTyxLQUFLLElBQUlBLGFBQWFoVCxTQUFTO2dCQUNqRXdKLHNCQUFzQndKLGdCQUFnQixPQUFPLEtBQUssSUFBSUEsYUFBYXhKLG9CQUFvQjtnQkFDdkZkLFFBQVFBO2dCQUNSSyxlQUFlQTtnQkFDZkssYUFBYSxDQUFDeU0sNEJBQTRCN0MsZ0JBQWdCLE9BQU8sS0FBSyxJQUFJQSxhQUFhNUosV0FBVyxLQUFLLE9BQU95TSw0QkFBNEJ6TTtnQkFDMUlQLGFBQWFBO2dCQUNibkIsVUFBVUE7Z0JBQ1Y3SSxPQUFPbVUsZ0JBQWdCLE9BQU8sS0FBSyxJQUFJQSxhQUFhblUsS0FBSztnQkFDekQrSixVQUFVb0ssZ0JBQWdCLE9BQU8sS0FBSyxJQUFJQSxhQUFhcEssUUFBUTtnQkFDL0RpQixZQUFZbUosZ0JBQWdCLE9BQU8sS0FBSyxJQUFJQSxhQUFhbkosVUFBVTtnQkFDbkVQLG1CQUFtQjBKLGdCQUFnQixPQUFPLEtBQUssSUFBSUEsYUFBYTFKLGlCQUFpQjtnQkFDakZDLG1CQUFtQnlKLGdCQUFnQixPQUFPLEtBQUssSUFBSUEsYUFBYXpKLGlCQUFpQjtnQkFDakZRLHNCQUFzQmlKLGdCQUFnQixPQUFPLEtBQUssSUFBSUEsYUFBYWpKLG9CQUFvQjtnQkFDdkZiLGFBQWFBO2dCQUNibEcsUUFBUUEsT0FBTy9DLE1BQU0sQ0FBQyxDQUFDdVMsSUFBSUEsRUFBRTlLLFFBQVEsSUFBSWpFLE1BQU1pRSxRQUFRO2dCQUN2RHNCLFNBQVNBLFFBQVEvSSxNQUFNLENBQUMsQ0FBQzRNLElBQUlBLEVBQUVuRixRQUFRLElBQUlqRSxNQUFNaUUsUUFBUTtnQkFDekRvQixZQUFZQTtnQkFDWmMsaUJBQWlCa0o7Z0JBQ2pCbkosS0FBS0E7Z0JBQ0xWLFVBQVVBO2dCQUNWd0gsaUJBQWlCekksTUFBTXlJLGVBQWU7WUFDMUM7UUFDSjtJQUNKO0FBQ0o7QUFFcUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGZlbmVyYmFoY2UtZm9ybVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHNvbm5lclxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmZ1bmN0aW9uIF9faW5zZXJ0Q1NTKGNvZGUpIHtcbiAgaWYgKCFjb2RlIHx8IHR5cGVvZiBkb2N1bWVudCA9PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gIGxldCBoZWFkID0gZG9jdW1lbnQuaGVhZCB8fCBkb2N1bWVudC5nZXRFbGVtZW50c0J5VGFnTmFtZSgnaGVhZCcpWzBdXG4gIGxldCBzdHlsZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3N0eWxlJylcbiAgc3R5bGUudHlwZSA9ICd0ZXh0L2NzcydcbiAgaGVhZC5hcHBlbmRDaGlsZChzdHlsZSlcbiAgO3N0eWxlLnN0eWxlU2hlZXQgPyAoc3R5bGUuc3R5bGVTaGVldC5jc3NUZXh0ID0gY29kZSkgOiBzdHlsZS5hcHBlbmRDaGlsZChkb2N1bWVudC5jcmVhdGVUZXh0Tm9kZShjb2RlKSlcbn1cblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBSZWFjdERPTSBmcm9tICdyZWFjdC1kb20nO1xuXG5jb25zdCBnZXRBc3NldCA9ICh0eXBlKT0+e1xuICAgIHN3aXRjaCh0eXBlKXtcbiAgICAgICAgY2FzZSAnc3VjY2Vzcyc6XG4gICAgICAgICAgICByZXR1cm4gU3VjY2Vzc0ljb247XG4gICAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICAgICAgcmV0dXJuIEluZm9JY29uO1xuICAgICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgICAgIHJldHVybiBXYXJuaW5nSWNvbjtcbiAgICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICAgICAgcmV0dXJuIEVycm9ySWNvbjtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn07XG5jb25zdCBiYXJzID0gQXJyYXkoMTIpLmZpbGwoMCk7XG5jb25zdCBMb2FkZXIgPSAoeyB2aXNpYmxlLCBjbGFzc05hbWUgfSk9PntcbiAgICByZXR1cm4gLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICAgICAgY2xhc3NOYW1lOiBbXG4gICAgICAgICAgICAnc29ubmVyLWxvYWRpbmctd3JhcHBlcicsXG4gICAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgXS5maWx0ZXIoQm9vbGVhbikuam9pbignICcpLFxuICAgICAgICBcImRhdGEtdmlzaWJsZVwiOiB2aXNpYmxlXG4gICAgfSwgLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICAgICAgY2xhc3NOYW1lOiBcInNvbm5lci1zcGlubmVyXCJcbiAgICB9LCBiYXJzLm1hcCgoXywgaSk9Pi8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgICAgICAgICBjbGFzc05hbWU6IFwic29ubmVyLWxvYWRpbmctYmFyXCIsXG4gICAgICAgICAgICBrZXk6IGBzcGlubmVyLWJhci0ke2l9YFxuICAgICAgICB9KSkpKTtcbn07XG5jb25zdCBTdWNjZXNzSWNvbiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCB7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyMCAyMFwiLFxuICAgIGZpbGw6IFwiY3VycmVudENvbG9yXCIsXG4gICAgaGVpZ2h0OiBcIjIwXCIsXG4gICAgd2lkdGg6IFwiMjBcIlxufSwgLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwicGF0aFwiLCB7XG4gICAgZmlsbFJ1bGU6IFwiZXZlbm9kZFwiLFxuICAgIGQ6IFwiTTEwIDE4YTggOCAwIDEwMC0xNiA4IDggMCAwMDAgMTZ6bTMuODU3LTkuODA5YS43NS43NSAwIDAwLTEuMjE0LS44ODJsLTMuNDgzIDQuNzktMS44OC0xLjg4YS43NS43NSAwIDEwLTEuMDYgMS4wNjFsMi41IDIuNWEuNzUuNzUgMCAwMDEuMTM3LS4wODlsNC01LjV6XCIsXG4gICAgY2xpcFJ1bGU6IFwiZXZlbm9kZFwiXG59KSk7XG5jb25zdCBXYXJuaW5nSWNvbiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCB7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyNCAyNFwiLFxuICAgIGZpbGw6IFwiY3VycmVudENvbG9yXCIsXG4gICAgaGVpZ2h0OiBcIjIwXCIsXG4gICAgd2lkdGg6IFwiMjBcIlxufSwgLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwicGF0aFwiLCB7XG4gICAgZmlsbFJ1bGU6IFwiZXZlbm9kZFwiLFxuICAgIGQ6IFwiTTkuNDAxIDMuMDAzYzEuMTU1LTIgNC4wNDMtMiA1LjE5NyAwbDcuMzU1IDEyLjc0OGMxLjE1NCAyLS4yOSA0LjUtMi41OTkgNC41SDQuNjQ1Yy0yLjMwOSAwLTMuNzUyLTIuNS0yLjU5OC00LjVMOS40IDMuMDAzek0xMiA4LjI1YS43NS43NSAwIDAxLjc1Ljc1djMuNzVhLjc1Ljc1IDAgMDEtMS41IDBWOWEuNzUuNzUgMCAwMS43NS0uNzV6bTAgOC4yNWEuNzUuNzUgMCAxMDAtMS41Ljc1Ljc1IDAgMDAwIDEuNXpcIixcbiAgICBjbGlwUnVsZTogXCJldmVub2RkXCJcbn0pKTtcbmNvbnN0IEluZm9JY29uID0gLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwic3ZnXCIsIHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDIwIDIwXCIsXG4gICAgZmlsbDogXCJjdXJyZW50Q29sb3JcIixcbiAgICBoZWlnaHQ6IFwiMjBcIixcbiAgICB3aWR0aDogXCIyMFwiXG59LCAvKiNfX1BVUkVfXyovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwYXRoXCIsIHtcbiAgICBmaWxsUnVsZTogXCJldmVub2RkXCIsXG4gICAgZDogXCJNMTggMTBhOCA4IDAgMTEtMTYgMCA4IDggMCAwMTE2IDB6bS03LTRhMSAxIDAgMTEtMiAwIDEgMSAwIDAxMiAwek05IDlhLjc1Ljc1IDAgMDAwIDEuNWguMjUzYS4yNS4yNSAwIDAxLjI0NC4zMDRsLS40NTkgMi4wNjZBMS43NSAxLjc1IDAgMDAxMC43NDcgMTVIMTFhLjc1Ljc1IDAgMDAwLTEuNWgtLjI1M2EuMjUuMjUgMCAwMS0uMjQ0LS4zMDRsLjQ1OS0yLjA2NkExLjc1IDEuNzUgMCAwMDkuMjUzIDlIOXpcIixcbiAgICBjbGlwUnVsZTogXCJldmVub2RkXCJcbn0pKTtcbmNvbnN0IEVycm9ySWNvbiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCB7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyMCAyMFwiLFxuICAgIGZpbGw6IFwiY3VycmVudENvbG9yXCIsXG4gICAgaGVpZ2h0OiBcIjIwXCIsXG4gICAgd2lkdGg6IFwiMjBcIlxufSwgLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwicGF0aFwiLCB7XG4gICAgZmlsbFJ1bGU6IFwiZXZlbm9kZFwiLFxuICAgIGQ6IFwiTTE4IDEwYTggOCAwIDExLTE2IDAgOCA4IDAgMDExNiAwem0tOC01YS43NS43NSAwIDAxLjc1Ljc1djQuNWEuNzUuNzUgMCAwMS0xLjUgMHYtNC41QS43NS43NSAwIDAxMTAgNXptMCAxMGExIDEgMCAxMDAtMiAxIDEgMCAwMDAgMnpcIixcbiAgICBjbGlwUnVsZTogXCJldmVub2RkXCJcbn0pKTtcbmNvbnN0IENsb3NlSWNvbiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCB7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICB3aWR0aDogXCIxMlwiLFxuICAgIGhlaWdodDogXCIxMlwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgZmlsbDogXCJub25lXCIsXG4gICAgc3Ryb2tlOiBcImN1cnJlbnRDb2xvclwiLFxuICAgIHN0cm9rZVdpZHRoOiBcIjEuNVwiLFxuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiXG59LCAvKiNfX1BVUkVfXyovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJsaW5lXCIsIHtcbiAgICB4MTogXCIxOFwiLFxuICAgIHkxOiBcIjZcIixcbiAgICB4MjogXCI2XCIsXG4gICAgeTI6IFwiMThcIlxufSksIC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcImxpbmVcIiwge1xuICAgIHgxOiBcIjZcIixcbiAgICB5MTogXCI2XCIsXG4gICAgeDI6IFwiMThcIixcbiAgICB5MjogXCIxOFwiXG59KSk7XG5cbmNvbnN0IHVzZUlzRG9jdW1lbnRIaWRkZW4gPSAoKT0+e1xuICAgIGNvbnN0IFtpc0RvY3VtZW50SGlkZGVuLCBzZXRJc0RvY3VtZW50SGlkZGVuXSA9IFJlYWN0LnVzZVN0YXRlKGRvY3VtZW50LmhpZGRlbik7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpPT57XG4gICAgICAgIGNvbnN0IGNhbGxiYWNrID0gKCk9PntcbiAgICAgICAgICAgIHNldElzRG9jdW1lbnRIaWRkZW4oZG9jdW1lbnQuaGlkZGVuKTtcbiAgICAgICAgfTtcbiAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigndmlzaWJpbGl0eWNoYW5nZScsIGNhbGxiYWNrKTtcbiAgICAgICAgcmV0dXJuICgpPT53aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigndmlzaWJpbGl0eWNoYW5nZScsIGNhbGxiYWNrKTtcbiAgICB9LCBbXSk7XG4gICAgcmV0dXJuIGlzRG9jdW1lbnRIaWRkZW47XG59O1xuXG5sZXQgdG9hc3RzQ291bnRlciA9IDE7XG5jbGFzcyBPYnNlcnZlciB7XG4gICAgY29uc3RydWN0b3IoKXtcbiAgICAgICAgLy8gV2UgdXNlIGFycm93IGZ1bmN0aW9ucyB0byBtYWludGFpbiB0aGUgY29ycmVjdCBgdGhpc2AgcmVmZXJlbmNlXG4gICAgICAgIHRoaXMuc3Vic2NyaWJlID0gKHN1YnNjcmliZXIpPT57XG4gICAgICAgICAgICB0aGlzLnN1YnNjcmliZXJzLnB1c2goc3Vic2NyaWJlcik7XG4gICAgICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgICAgICBjb25zdCBpbmRleCA9IHRoaXMuc3Vic2NyaWJlcnMuaW5kZXhPZihzdWJzY3JpYmVyKTtcbiAgICAgICAgICAgICAgICB0aGlzLnN1YnNjcmliZXJzLnNwbGljZShpbmRleCwgMSk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLnB1Ymxpc2ggPSAoZGF0YSk9PntcbiAgICAgICAgICAgIHRoaXMuc3Vic2NyaWJlcnMuZm9yRWFjaCgoc3Vic2NyaWJlcik9PnN1YnNjcmliZXIoZGF0YSkpO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLmFkZFRvYXN0ID0gKGRhdGEpPT57XG4gICAgICAgICAgICB0aGlzLnB1Ymxpc2goZGF0YSk7XG4gICAgICAgICAgICB0aGlzLnRvYXN0cyA9IFtcbiAgICAgICAgICAgICAgICAuLi50aGlzLnRvYXN0cyxcbiAgICAgICAgICAgICAgICBkYXRhXG4gICAgICAgICAgICBdO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLmNyZWF0ZSA9IChkYXRhKT0+e1xuICAgICAgICAgICAgdmFyIF9kYXRhX2lkO1xuICAgICAgICAgICAgY29uc3QgeyBtZXNzYWdlLCAuLi5yZXN0IH0gPSBkYXRhO1xuICAgICAgICAgICAgY29uc3QgaWQgPSB0eXBlb2YgKGRhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IGRhdGEuaWQpID09PSAnbnVtYmVyJyB8fCAoKF9kYXRhX2lkID0gZGF0YS5pZCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9kYXRhX2lkLmxlbmd0aCkgPiAwID8gZGF0YS5pZCA6IHRvYXN0c0NvdW50ZXIrKztcbiAgICAgICAgICAgIGNvbnN0IGFscmVhZHlFeGlzdHMgPSB0aGlzLnRvYXN0cy5maW5kKCh0b2FzdCk9PntcbiAgICAgICAgICAgICAgICByZXR1cm4gdG9hc3QuaWQgPT09IGlkO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zdCBkaXNtaXNzaWJsZSA9IGRhdGEuZGlzbWlzc2libGUgPT09IHVuZGVmaW5lZCA/IHRydWUgOiBkYXRhLmRpc21pc3NpYmxlO1xuICAgICAgICAgICAgaWYgKHRoaXMuZGlzbWlzc2VkVG9hc3RzLmhhcyhpZCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmRpc21pc3NlZFRvYXN0cy5kZWxldGUoaWQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGFscmVhZHlFeGlzdHMpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnRvYXN0cyA9IHRoaXMudG9hc3RzLm1hcCgodG9hc3QpPT57XG4gICAgICAgICAgICAgICAgICAgIGlmICh0b2FzdC5pZCA9PT0gaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucHVibGlzaCh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udG9hc3QsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogbWVzc2FnZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnRvYXN0LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmRhdGEsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzbWlzc2libGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IG1lc3NhZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRvYXN0O1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLmFkZFRvYXN0KHtcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU6IG1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgICAgIC4uLnJlc3QsXG4gICAgICAgICAgICAgICAgICAgIGRpc21pc3NpYmxlLFxuICAgICAgICAgICAgICAgICAgICBpZFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGlkO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLmRpc21pc3MgPSAoaWQpPT57XG4gICAgICAgICAgICBpZiAoaWQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmRpc21pc3NlZFRvYXN0cy5hZGQoaWQpO1xuICAgICAgICAgICAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZSgoKT0+dGhpcy5zdWJzY3JpYmVycy5mb3JFYWNoKChzdWJzY3JpYmVyKT0+c3Vic2NyaWJlcih7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzbWlzczogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy50b2FzdHMuZm9yRWFjaCgodG9hc3QpPT57XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3Vic2NyaWJlcnMuZm9yRWFjaCgoc3Vic2NyaWJlcik9PnN1YnNjcmliZXIoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiB0b2FzdC5pZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNtaXNzOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gaWQ7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMubWVzc2FnZSA9IChtZXNzYWdlLCBkYXRhKT0+e1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY3JlYXRlKHtcbiAgICAgICAgICAgICAgICAuLi5kYXRhLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2VcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLmVycm9yID0gKG1lc3NhZ2UsIGRhdGEpPT57XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5jcmVhdGUoe1xuICAgICAgICAgICAgICAgIC4uLmRhdGEsXG4gICAgICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5zdWNjZXNzID0gKG1lc3NhZ2UsIGRhdGEpPT57XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5jcmVhdGUoe1xuICAgICAgICAgICAgICAgIC4uLmRhdGEsXG4gICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2VcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLmluZm8gPSAobWVzc2FnZSwgZGF0YSk9PntcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmNyZWF0ZSh7XG4gICAgICAgICAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgICAgICAgICB0eXBlOiAnaW5mbycsXG4gICAgICAgICAgICAgICAgbWVzc2FnZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMud2FybmluZyA9IChtZXNzYWdlLCBkYXRhKT0+e1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY3JlYXRlKHtcbiAgICAgICAgICAgICAgICAuLi5kYXRhLFxuICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJyxcbiAgICAgICAgICAgICAgICBtZXNzYWdlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5sb2FkaW5nID0gKG1lc3NhZ2UsIGRhdGEpPT57XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5jcmVhdGUoe1xuICAgICAgICAgICAgICAgIC4uLmRhdGEsXG4gICAgICAgICAgICAgICAgdHlwZTogJ2xvYWRpbmcnLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2VcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLnByb21pc2UgPSAocHJvbWlzZSwgZGF0YSk9PntcbiAgICAgICAgICAgIGlmICghZGF0YSkge1xuICAgICAgICAgICAgICAgIC8vIE5vdGhpbmcgdG8gc2hvd1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBpZCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIGlmIChkYXRhLmxvYWRpbmcgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIGlkID0gdGhpcy5jcmVhdGUoe1xuICAgICAgICAgICAgICAgICAgICAuLi5kYXRhLFxuICAgICAgICAgICAgICAgICAgICBwcm9taXNlLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAnbG9hZGluZycsXG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGRhdGEubG9hZGluZyxcbiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IHR5cGVvZiBkYXRhLmRlc2NyaXB0aW9uICE9PSAnZnVuY3Rpb24nID8gZGF0YS5kZXNjcmlwdGlvbiA6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgcCA9IFByb21pc2UucmVzb2x2ZShwcm9taXNlIGluc3RhbmNlb2YgRnVuY3Rpb24gPyBwcm9taXNlKCkgOiBwcm9taXNlKTtcbiAgICAgICAgICAgIGxldCBzaG91bGREaXNtaXNzID0gaWQgIT09IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIGxldCByZXN1bHQ7XG4gICAgICAgICAgICBjb25zdCBvcmlnaW5hbFByb21pc2UgPSBwLnRoZW4oYXN5bmMgKHJlc3BvbnNlKT0+e1xuICAgICAgICAgICAgICAgIHJlc3VsdCA9IFtcbiAgICAgICAgICAgICAgICAgICAgJ3Jlc29sdmUnLFxuICAgICAgICAgICAgICAgICAgICByZXNwb25zZVxuICAgICAgICAgICAgICAgIF07XG4gICAgICAgICAgICAgICAgY29uc3QgaXNSZWFjdEVsZW1lbnRSZXNwb25zZSA9IFJlYWN0LmlzVmFsaWRFbGVtZW50KHJlc3BvbnNlKTtcbiAgICAgICAgICAgICAgICBpZiAoaXNSZWFjdEVsZW1lbnRSZXNwb25zZSkge1xuICAgICAgICAgICAgICAgICAgICBzaG91bGREaXNtaXNzID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3JlYXRlKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2RlZmF1bHQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2VcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChpc0h0dHBSZXNwb25zZShyZXNwb25zZSkgJiYgIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAgICAgICAgIHNob3VsZERpc21pc3MgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvbWlzZURhdGEgPSB0eXBlb2YgZGF0YS5lcnJvciA9PT0gJ2Z1bmN0aW9uJyA/IGF3YWl0IGRhdGEuZXJyb3IoYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCkgOiBkYXRhLmVycm9yO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkZXNjcmlwdGlvbiA9IHR5cGVvZiBkYXRhLmRlc2NyaXB0aW9uID09PSAnZnVuY3Rpb24nID8gYXdhaXQgZGF0YS5kZXNjcmlwdGlvbihgSFRUUCBlcnJvciEgc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKSA6IGRhdGEuZGVzY3JpcHRpb247XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzRXh0ZW5kZWRSZXN1bHQgPSB0eXBlb2YgcHJvbWlzZURhdGEgPT09ICdvYmplY3QnICYmICFSZWFjdC5pc1ZhbGlkRWxlbWVudChwcm9taXNlRGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvYXN0U2V0dGluZ3MgPSBpc0V4dGVuZGVkUmVzdWx0ID8gcHJvbWlzZURhdGEgOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBwcm9taXNlRGF0YVxuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmNyZWF0ZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbixcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnRvYXN0U2V0dGluZ3NcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChyZXNwb25zZSBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgIHNob3VsZERpc21pc3MgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvbWlzZURhdGEgPSB0eXBlb2YgZGF0YS5lcnJvciA9PT0gJ2Z1bmN0aW9uJyA/IGF3YWl0IGRhdGEuZXJyb3IocmVzcG9uc2UpIDogZGF0YS5lcnJvcjtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGVzY3JpcHRpb24gPSB0eXBlb2YgZGF0YS5kZXNjcmlwdGlvbiA9PT0gJ2Z1bmN0aW9uJyA/IGF3YWl0IGRhdGEuZGVzY3JpcHRpb24ocmVzcG9uc2UpIDogZGF0YS5kZXNjcmlwdGlvbjtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNFeHRlbmRlZFJlc3VsdCA9IHR5cGVvZiBwcm9taXNlRGF0YSA9PT0gJ29iamVjdCcgJiYgIVJlYWN0LmlzVmFsaWRFbGVtZW50KHByb21pc2VEYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdG9hc3RTZXR0aW5ncyA9IGlzRXh0ZW5kZWRSZXN1bHQgPyBwcm9taXNlRGF0YSA6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHByb21pc2VEYXRhXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3JlYXRlKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgICAgICAgICAgICAgLi4udG9hc3RTZXR0aW5nc1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEuc3VjY2VzcyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIHNob3VsZERpc21pc3MgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvbWlzZURhdGEgPSB0eXBlb2YgZGF0YS5zdWNjZXNzID09PSAnZnVuY3Rpb24nID8gYXdhaXQgZGF0YS5zdWNjZXNzKHJlc3BvbnNlKSA6IGRhdGEuc3VjY2VzcztcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGVzY3JpcHRpb24gPSB0eXBlb2YgZGF0YS5kZXNjcmlwdGlvbiA9PT0gJ2Z1bmN0aW9uJyA/IGF3YWl0IGRhdGEuZGVzY3JpcHRpb24ocmVzcG9uc2UpIDogZGF0YS5kZXNjcmlwdGlvbjtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNFeHRlbmRlZFJlc3VsdCA9IHR5cGVvZiBwcm9taXNlRGF0YSA9PT0gJ29iamVjdCcgJiYgIVJlYWN0LmlzVmFsaWRFbGVtZW50KHByb21pc2VEYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdG9hc3RTZXR0aW5ncyA9IGlzRXh0ZW5kZWRSZXN1bHQgPyBwcm9taXNlRGF0YSA6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHByb21pc2VEYXRhXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3JlYXRlKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb24sXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi50b2FzdFNldHRpbmdzXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pLmNhdGNoKGFzeW5jIChlcnJvcik9PntcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBbXG4gICAgICAgICAgICAgICAgICAgICdyZWplY3QnLFxuICAgICAgICAgICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgIF07XG4gICAgICAgICAgICAgICAgaWYgKGRhdGEuZXJyb3IgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICBzaG91bGREaXNtaXNzID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByb21pc2VEYXRhID0gdHlwZW9mIGRhdGEuZXJyb3IgPT09ICdmdW5jdGlvbicgPyBhd2FpdCBkYXRhLmVycm9yKGVycm9yKSA6IGRhdGEuZXJyb3I7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGRlc2NyaXB0aW9uID0gdHlwZW9mIGRhdGEuZGVzY3JpcHRpb24gPT09ICdmdW5jdGlvbicgPyBhd2FpdCBkYXRhLmRlc2NyaXB0aW9uKGVycm9yKSA6IGRhdGEuZGVzY3JpcHRpb247XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzRXh0ZW5kZWRSZXN1bHQgPSB0eXBlb2YgcHJvbWlzZURhdGEgPT09ICdvYmplY3QnICYmICFSZWFjdC5pc1ZhbGlkRWxlbWVudChwcm9taXNlRGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvYXN0U2V0dGluZ3MgPSBpc0V4dGVuZGVkUmVzdWx0ID8gcHJvbWlzZURhdGEgOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBwcm9taXNlRGF0YVxuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmNyZWF0ZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbixcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnRvYXN0U2V0dGluZ3NcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSkuZmluYWxseSgoKT0+e1xuICAgICAgICAgICAgICAgIGlmIChzaG91bGREaXNtaXNzKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFRvYXN0IGlzIHN0aWxsIGluIGxvYWQgc3RhdGUgKGFuZCB3aWxsIGJlIGluZGVmaW5pdGVseSDigJQgZGlzbWlzcyBpdClcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kaXNtaXNzKGlkKTtcbiAgICAgICAgICAgICAgICAgICAgaWQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGRhdGEuZmluYWxseSA9PSBudWxsID8gdm9pZCAwIDogZGF0YS5maW5hbGx5LmNhbGwoZGF0YSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnN0IHVud3JhcCA9ICgpPT5uZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KT0+b3JpZ2luYWxQcm9taXNlLnRoZW4oKCk9PnJlc3VsdFswXSA9PT0gJ3JlamVjdCcgPyByZWplY3QocmVzdWx0WzFdKSA6IHJlc29sdmUocmVzdWx0WzFdKSkuY2F0Y2gocmVqZWN0KSk7XG4gICAgICAgICAgICBpZiAodHlwZW9mIGlkICE9PSAnc3RyaW5nJyAmJiB0eXBlb2YgaWQgIT09ICdudW1iZXInKSB7XG4gICAgICAgICAgICAgICAgLy8gY2Fubm90IE9iamVjdC5hc3NpZ24gb24gdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgdW53cmFwXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oaWQsIHtcbiAgICAgICAgICAgICAgICAgICAgdW53cmFwXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuY3VzdG9tID0gKGpzeCwgZGF0YSk9PntcbiAgICAgICAgICAgIGNvbnN0IGlkID0gKGRhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IGRhdGEuaWQpIHx8IHRvYXN0c0NvdW50ZXIrKztcbiAgICAgICAgICAgIHRoaXMuY3JlYXRlKHtcbiAgICAgICAgICAgICAgICBqc3g6IGpzeChpZCksXG4gICAgICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICAgICAgLi4uZGF0YVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gaWQ7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuZ2V0QWN0aXZlVG9hc3RzID0gKCk9PntcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnRvYXN0cy5maWx0ZXIoKHRvYXN0KT0+IXRoaXMuZGlzbWlzc2VkVG9hc3RzLmhhcyh0b2FzdC5pZCkpO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLnN1YnNjcmliZXJzID0gW107XG4gICAgICAgIHRoaXMudG9hc3RzID0gW107XG4gICAgICAgIHRoaXMuZGlzbWlzc2VkVG9hc3RzID0gbmV3IFNldCgpO1xuICAgIH1cbn1cbmNvbnN0IFRvYXN0U3RhdGUgPSBuZXcgT2JzZXJ2ZXIoKTtcbi8vIGJpbmQgdGhpcyB0byB0aGUgdG9hc3QgZnVuY3Rpb25cbmNvbnN0IHRvYXN0RnVuY3Rpb24gPSAobWVzc2FnZSwgZGF0YSk9PntcbiAgICBjb25zdCBpZCA9IChkYXRhID09IG51bGwgPyB2b2lkIDAgOiBkYXRhLmlkKSB8fCB0b2FzdHNDb3VudGVyKys7XG4gICAgVG9hc3RTdGF0ZS5hZGRUb2FzdCh7XG4gICAgICAgIHRpdGxlOiBtZXNzYWdlLFxuICAgICAgICAuLi5kYXRhLFxuICAgICAgICBpZFxuICAgIH0pO1xuICAgIHJldHVybiBpZDtcbn07XG5jb25zdCBpc0h0dHBSZXNwb25zZSA9IChkYXRhKT0+e1xuICAgIHJldHVybiBkYXRhICYmIHR5cGVvZiBkYXRhID09PSAnb2JqZWN0JyAmJiAnb2snIGluIGRhdGEgJiYgdHlwZW9mIGRhdGEub2sgPT09ICdib29sZWFuJyAmJiAnc3RhdHVzJyBpbiBkYXRhICYmIHR5cGVvZiBkYXRhLnN0YXR1cyA9PT0gJ251bWJlcic7XG59O1xuY29uc3QgYmFzaWNUb2FzdCA9IHRvYXN0RnVuY3Rpb247XG5jb25zdCBnZXRIaXN0b3J5ID0gKCk9PlRvYXN0U3RhdGUudG9hc3RzO1xuY29uc3QgZ2V0VG9hc3RzID0gKCk9PlRvYXN0U3RhdGUuZ2V0QWN0aXZlVG9hc3RzKCk7XG4vLyBXZSB1c2UgYE9iamVjdC5hc3NpZ25gIHRvIG1haW50YWluIHRoZSBjb3JyZWN0IHR5cGVzIGFzIHdlIHdvdWxkIGxvc2UgdGhlbSBvdGhlcndpc2VcbmNvbnN0IHRvYXN0ID0gT2JqZWN0LmFzc2lnbihiYXNpY1RvYXN0LCB7XG4gICAgc3VjY2VzczogVG9hc3RTdGF0ZS5zdWNjZXNzLFxuICAgIGluZm86IFRvYXN0U3RhdGUuaW5mbyxcbiAgICB3YXJuaW5nOiBUb2FzdFN0YXRlLndhcm5pbmcsXG4gICAgZXJyb3I6IFRvYXN0U3RhdGUuZXJyb3IsXG4gICAgY3VzdG9tOiBUb2FzdFN0YXRlLmN1c3RvbSxcbiAgICBtZXNzYWdlOiBUb2FzdFN0YXRlLm1lc3NhZ2UsXG4gICAgcHJvbWlzZTogVG9hc3RTdGF0ZS5wcm9taXNlLFxuICAgIGRpc21pc3M6IFRvYXN0U3RhdGUuZGlzbWlzcyxcbiAgICBsb2FkaW5nOiBUb2FzdFN0YXRlLmxvYWRpbmdcbn0sIHtcbiAgICBnZXRIaXN0b3J5LFxuICAgIGdldFRvYXN0c1xufSk7XG5cbl9faW5zZXJ0Q1NTKFwiW2RhdGEtc29ubmVyLXRvYXN0ZXJdW2Rpcj1sdHJdLGh0bWxbZGlyPWx0cl17LS10b2FzdC1pY29uLW1hcmdpbi1zdGFydDotM3B4Oy0tdG9hc3QtaWNvbi1tYXJnaW4tZW5kOjRweDstLXRvYXN0LXN2Zy1tYXJnaW4tc3RhcnQ6LTFweDstLXRvYXN0LXN2Zy1tYXJnaW4tZW5kOjBweDstLXRvYXN0LWJ1dHRvbi1tYXJnaW4tc3RhcnQ6YXV0bzstLXRvYXN0LWJ1dHRvbi1tYXJnaW4tZW5kOjA7LS10b2FzdC1jbG9zZS1idXR0b24tc3RhcnQ6MDstLXRvYXN0LWNsb3NlLWJ1dHRvbi1lbmQ6dW5zZXQ7LS10b2FzdC1jbG9zZS1idXR0b24tdHJhbnNmb3JtOnRyYW5zbGF0ZSgtMzUlLCAtMzUlKX1bZGF0YS1zb25uZXItdG9hc3Rlcl1bZGlyPXJ0bF0saHRtbFtkaXI9cnRsXXstLXRvYXN0LWljb24tbWFyZ2luLXN0YXJ0OjRweDstLXRvYXN0LWljb24tbWFyZ2luLWVuZDotM3B4Oy0tdG9hc3Qtc3ZnLW1hcmdpbi1zdGFydDowcHg7LS10b2FzdC1zdmctbWFyZ2luLWVuZDotMXB4Oy0tdG9hc3QtYnV0dG9uLW1hcmdpbi1zdGFydDowOy0tdG9hc3QtYnV0dG9uLW1hcmdpbi1lbmQ6YXV0bzstLXRvYXN0LWNsb3NlLWJ1dHRvbi1zdGFydDp1bnNldDstLXRvYXN0LWNsb3NlLWJ1dHRvbi1lbmQ6MDstLXRvYXN0LWNsb3NlLWJ1dHRvbi10cmFuc2Zvcm06dHJhbnNsYXRlKDM1JSwgLTM1JSl9W2RhdGEtc29ubmVyLXRvYXN0ZXJde3Bvc2l0aW9uOmZpeGVkO3dpZHRoOnZhcigtLXdpZHRoKTtmb250LWZhbWlseTp1aS1zYW5zLXNlcmlmLHN5c3RlbS11aSwtYXBwbGUtc3lzdGVtLEJsaW5rTWFjU3lzdGVtRm9udCxTZWdvZSBVSSxSb2JvdG8sSGVsdmV0aWNhIE5ldWUsQXJpYWwsTm90byBTYW5zLHNhbnMtc2VyaWYsQXBwbGUgQ29sb3IgRW1vamksU2Vnb2UgVUkgRW1vamksU2Vnb2UgVUkgU3ltYm9sLE5vdG8gQ29sb3IgRW1vamk7LS1ncmF5MTpoc2woMCwgMCUsIDk5JSk7LS1ncmF5Mjpoc2woMCwgMCUsIDk3LjMlKTstLWdyYXkzOmhzbCgwLCAwJSwgOTUuMSUpOy0tZ3JheTQ6aHNsKDAsIDAlLCA5MyUpOy0tZ3JheTU6aHNsKDAsIDAlLCA5MC45JSk7LS1ncmF5Njpoc2woMCwgMCUsIDg4LjclKTstLWdyYXk3OmhzbCgwLCAwJSwgODUuOCUpOy0tZ3JheTg6aHNsKDAsIDAlLCA3OCUpOy0tZ3JheTk6aHNsKDAsIDAlLCA1Ni4xJSk7LS1ncmF5MTA6aHNsKDAsIDAlLCA1Mi4zJSk7LS1ncmF5MTE6aHNsKDAsIDAlLCA0My41JSk7LS1ncmF5MTI6aHNsKDAsIDAlLCA5JSk7LS1ib3JkZXItcmFkaXVzOjhweDtib3gtc2l6aW5nOmJvcmRlci1ib3g7cGFkZGluZzowO21hcmdpbjowO2xpc3Qtc3R5bGU6bm9uZTtvdXRsaW5lOjA7ei1pbmRleDo5OTk5OTk5OTk7dHJhbnNpdGlvbjp0cmFuc2Zvcm0gLjRzIGVhc2V9W2RhdGEtc29ubmVyLXRvYXN0ZXJdW2RhdGEtbGlmdGVkPXRydWVde3RyYW5zZm9ybTp0cmFuc2xhdGVZKC04cHgpfUBtZWRpYSAoaG92ZXI6bm9uZSkgYW5kIChwb2ludGVyOmNvYXJzZSl7W2RhdGEtc29ubmVyLXRvYXN0ZXJdW2RhdGEtbGlmdGVkPXRydWVde3RyYW5zZm9ybTpub25lfX1bZGF0YS1zb25uZXItdG9hc3Rlcl1bZGF0YS14LXBvc2l0aW9uPXJpZ2h0XXtyaWdodDp2YXIoLS1vZmZzZXQtcmlnaHQpfVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXgtcG9zaXRpb249bGVmdF17bGVmdDp2YXIoLS1vZmZzZXQtbGVmdCl9W2RhdGEtc29ubmVyLXRvYXN0ZXJdW2RhdGEteC1wb3NpdGlvbj1jZW50ZXJde2xlZnQ6NTAlO3RyYW5zZm9ybTp0cmFuc2xhdGVYKC01MCUpfVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXktcG9zaXRpb249dG9wXXt0b3A6dmFyKC0tb2Zmc2V0LXRvcCl9W2RhdGEtc29ubmVyLXRvYXN0ZXJdW2RhdGEteS1wb3NpdGlvbj1ib3R0b21de2JvdHRvbTp2YXIoLS1vZmZzZXQtYm90dG9tKX1bZGF0YS1zb25uZXItdG9hc3Rdey0teTp0cmFuc2xhdGVZKDEwMCUpOy0tbGlmdC1hbW91bnQ6Y2FsYyh2YXIoLS1saWZ0KSAqIHZhcigtLWdhcCkpO3otaW5kZXg6dmFyKC0tei1pbmRleCk7cG9zaXRpb246YWJzb2x1dGU7b3BhY2l0eTowO3RyYW5zZm9ybTp2YXIoLS15KTt0b3VjaC1hY3Rpb246bm9uZTt0cmFuc2l0aW9uOnRyYW5zZm9ybSAuNHMsb3BhY2l0eSAuNHMsaGVpZ2h0IC40cyxib3gtc2hhZG93IC4ycztib3gtc2l6aW5nOmJvcmRlci1ib3g7b3V0bGluZTowO292ZXJmbG93LXdyYXA6YW55d2hlcmV9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN0eWxlZD10cnVlXXtwYWRkaW5nOjE2cHg7YmFja2dyb3VuZDp2YXIoLS1ub3JtYWwtYmcpO2JvcmRlcjoxcHggc29saWQgdmFyKC0tbm9ybWFsLWJvcmRlcik7Y29sb3I6dmFyKC0tbm9ybWFsLXRleHQpO2JvcmRlci1yYWRpdXM6dmFyKC0tYm9yZGVyLXJhZGl1cyk7Ym94LXNoYWRvdzowIDRweCAxMnB4IHJnYmEoMCwwLDAsLjEpO3dpZHRoOnZhcigtLXdpZHRoKTtmb250LXNpemU6MTNweDtkaXNwbGF5OmZsZXg7YWxpZ24taXRlbXM6Y2VudGVyO2dhcDo2cHh9W2RhdGEtc29ubmVyLXRvYXN0XTpmb2N1cy12aXNpYmxle2JveC1zaGFkb3c6MCA0cHggMTJweCByZ2JhKDAsMCwwLC4xKSwwIDAgMCAycHggcmdiYSgwLDAsMCwuMil9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXktcG9zaXRpb249dG9wXXt0b3A6MDstLXk6dHJhbnNsYXRlWSgtMTAwJSk7LS1saWZ0OjE7LS1saWZ0LWFtb3VudDpjYWxjKDEgKiB2YXIoLS1nYXApKX1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEteS1wb3NpdGlvbj1ib3R0b21de2JvdHRvbTowOy0teTp0cmFuc2xhdGVZKDEwMCUpOy0tbGlmdDotMTstLWxpZnQtYW1vdW50OmNhbGModmFyKC0tbGlmdCkgKiB2YXIoLS1nYXApKX1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3R5bGVkPXRydWVdIFtkYXRhLWRlc2NyaXB0aW9uXXtmb250LXdlaWdodDo0MDA7bGluZS1oZWlnaHQ6MS40O2NvbG9yOiMzZjNmM2Z9W2RhdGEtcmljaC1jb2xvcnM9dHJ1ZV1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3R5bGVkPXRydWVdIFtkYXRhLWRlc2NyaXB0aW9uXXtjb2xvcjppbmhlcml0fVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXNvbm5lci10aGVtZT1kYXJrXSBbZGF0YS1kZXNjcmlwdGlvbl17Y29sb3I6I2U4ZThlOH1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3R5bGVkPXRydWVdIFtkYXRhLXRpdGxlXXtmb250LXdlaWdodDo1MDA7bGluZS1oZWlnaHQ6MS41O2NvbG9yOmluaGVyaXR9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN0eWxlZD10cnVlXSBbZGF0YS1pY29uXXtkaXNwbGF5OmZsZXg7aGVpZ2h0OjE2cHg7d2lkdGg6MTZweDtwb3NpdGlvbjpyZWxhdGl2ZTtqdXN0aWZ5LWNvbnRlbnQ6ZmxleC1zdGFydDthbGlnbi1pdGVtczpjZW50ZXI7ZmxleC1zaHJpbms6MDttYXJnaW4tbGVmdDp2YXIoLS10b2FzdC1pY29uLW1hcmdpbi1zdGFydCk7bWFyZ2luLXJpZ2h0OnZhcigtLXRvYXN0LWljb24tbWFyZ2luLWVuZCl9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXByb21pc2U9dHJ1ZV0gW2RhdGEtaWNvbl0+c3Zne29wYWNpdHk6MDt0cmFuc2Zvcm06c2NhbGUoLjgpO3RyYW5zZm9ybS1vcmlnaW46Y2VudGVyO2FuaW1hdGlvbjpzb25uZXItZmFkZS1pbiAuM3MgZWFzZSBmb3J3YXJkc31bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3R5bGVkPXRydWVdIFtkYXRhLWljb25dPip7ZmxleC1zaHJpbms6MH1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3R5bGVkPXRydWVdIFtkYXRhLWljb25dIHN2Z3ttYXJnaW4tbGVmdDp2YXIoLS10b2FzdC1zdmctbWFyZ2luLXN0YXJ0KTttYXJnaW4tcmlnaHQ6dmFyKC0tdG9hc3Qtc3ZnLW1hcmdpbi1lbmQpfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1zdHlsZWQ9dHJ1ZV0gW2RhdGEtY29udGVudF17ZGlzcGxheTpmbGV4O2ZsZXgtZGlyZWN0aW9uOmNvbHVtbjtnYXA6MnB4fVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1zdHlsZWQ9dHJ1ZV0gW2RhdGEtYnV0dG9uXXtib3JkZXItcmFkaXVzOjRweDtwYWRkaW5nLWxlZnQ6OHB4O3BhZGRpbmctcmlnaHQ6OHB4O2hlaWdodDoyNHB4O2ZvbnQtc2l6ZToxMnB4O2NvbG9yOnZhcigtLW5vcm1hbC1iZyk7YmFja2dyb3VuZDp2YXIoLS1ub3JtYWwtdGV4dCk7bWFyZ2luLWxlZnQ6dmFyKC0tdG9hc3QtYnV0dG9uLW1hcmdpbi1zdGFydCk7bWFyZ2luLXJpZ2h0OnZhcigtLXRvYXN0LWJ1dHRvbi1tYXJnaW4tZW5kKTtib3JkZXI6bm9uZTtmb250LXdlaWdodDo1MDA7Y3Vyc29yOnBvaW50ZXI7b3V0bGluZTowO2Rpc3BsYXk6ZmxleDthbGlnbi1pdGVtczpjZW50ZXI7ZmxleC1zaHJpbms6MDt0cmFuc2l0aW9uOm9wYWNpdHkgLjRzLGJveC1zaGFkb3cgLjJzfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1zdHlsZWQ9dHJ1ZV0gW2RhdGEtYnV0dG9uXTpmb2N1cy12aXNpYmxle2JveC1zaGFkb3c6MCAwIDAgMnB4IHJnYmEoMCwwLDAsLjQpfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1zdHlsZWQ9dHJ1ZV0gW2RhdGEtYnV0dG9uXTpmaXJzdC1vZi10eXBle21hcmdpbi1sZWZ0OnZhcigtLXRvYXN0LWJ1dHRvbi1tYXJnaW4tc3RhcnQpO21hcmdpbi1yaWdodDp2YXIoLS10b2FzdC1idXR0b24tbWFyZ2luLWVuZCl9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN0eWxlZD10cnVlXSBbZGF0YS1jYW5jZWxde2NvbG9yOnZhcigtLW5vcm1hbC10ZXh0KTtiYWNrZ3JvdW5kOnJnYmEoMCwwLDAsLjA4KX1bZGF0YS1zb25uZXItdG9hc3Rlcl1bZGF0YS1zb25uZXItdGhlbWU9ZGFya10gW2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN0eWxlZD10cnVlXSBbZGF0YS1jYW5jZWxde2JhY2tncm91bmQ6cmdiYSgyNTUsMjU1LDI1NSwuMyl9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN0eWxlZD10cnVlXSBbZGF0YS1jbG9zZS1idXR0b25de3Bvc2l0aW9uOmFic29sdXRlO2xlZnQ6dmFyKC0tdG9hc3QtY2xvc2UtYnV0dG9uLXN0YXJ0KTtyaWdodDp2YXIoLS10b2FzdC1jbG9zZS1idXR0b24tZW5kKTt0b3A6MDtoZWlnaHQ6MjBweDt3aWR0aDoyMHB4O2Rpc3BsYXk6ZmxleDtqdXN0aWZ5LWNvbnRlbnQ6Y2VudGVyO2FsaWduLWl0ZW1zOmNlbnRlcjtwYWRkaW5nOjA7Y29sb3I6dmFyKC0tZ3JheTEyKTtiYWNrZ3JvdW5kOnZhcigtLW5vcm1hbC1iZyk7Ym9yZGVyOjFweCBzb2xpZCB2YXIoLS1ncmF5NCk7dHJhbnNmb3JtOnZhcigtLXRvYXN0LWNsb3NlLWJ1dHRvbi10cmFuc2Zvcm0pO2JvcmRlci1yYWRpdXM6NTAlO2N1cnNvcjpwb2ludGVyO3otaW5kZXg6MTt0cmFuc2l0aW9uOm9wYWNpdHkgLjFzLGJhY2tncm91bmQgLjJzLGJvcmRlci1jb2xvciAuMnN9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN0eWxlZD10cnVlXSBbZGF0YS1jbG9zZS1idXR0b25dOmZvY3VzLXZpc2libGV7Ym94LXNoYWRvdzowIDRweCAxMnB4IHJnYmEoMCwwLDAsLjEpLDAgMCAwIDJweCByZ2JhKDAsMCwwLC4yKX1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3R5bGVkPXRydWVdIFtkYXRhLWRpc2FibGVkPXRydWVde2N1cnNvcjpub3QtYWxsb3dlZH1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3R5bGVkPXRydWVdOmhvdmVyIFtkYXRhLWNsb3NlLWJ1dHRvbl06aG92ZXJ7YmFja2dyb3VuZDp2YXIoLS1ncmF5Mik7Ym9yZGVyLWNvbG9yOnZhcigtLWdyYXk1KX1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3dpcGluZz10cnVlXTo6YmVmb3Jle2NvbnRlbnQ6Jyc7cG9zaXRpb246YWJzb2x1dGU7bGVmdDotMTAwJTtyaWdodDotMTAwJTtoZWlnaHQ6MTAwJTt6LWluZGV4Oi0xfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS15LXBvc2l0aW9uPXRvcF1bZGF0YS1zd2lwaW5nPXRydWVdOjpiZWZvcmV7Ym90dG9tOjUwJTt0cmFuc2Zvcm06c2NhbGVZKDMpIHRyYW5zbGF0ZVkoNTAlKX1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEteS1wb3NpdGlvbj1ib3R0b21dW2RhdGEtc3dpcGluZz10cnVlXTo6YmVmb3Jle3RvcDo1MCU7dHJhbnNmb3JtOnNjYWxlWSgzKSB0cmFuc2xhdGVZKC01MCUpfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1zd2lwaW5nPWZhbHNlXVtkYXRhLXJlbW92ZWQ9dHJ1ZV06OmJlZm9yZXtjb250ZW50OicnO3Bvc2l0aW9uOmFic29sdXRlO2luc2V0OjA7dHJhbnNmb3JtOnNjYWxlWSgyKX1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtZXhwYW5kZWQ9dHJ1ZV06OmFmdGVye2NvbnRlbnQ6Jyc7cG9zaXRpb246YWJzb2x1dGU7bGVmdDowO2hlaWdodDpjYWxjKHZhcigtLWdhcCkgKyAxcHgpO2JvdHRvbToxMDAlO3dpZHRoOjEwMCV9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLW1vdW50ZWQ9dHJ1ZV17LS15OnRyYW5zbGF0ZVkoMCk7b3BhY2l0eToxfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1leHBhbmRlZD1mYWxzZV1bZGF0YS1mcm9udD1mYWxzZV17LS1zY2FsZTp2YXIoLS10b2FzdHMtYmVmb3JlKSAqIDAuMDUgKyAxOy0teTp0cmFuc2xhdGVZKGNhbGModmFyKC0tbGlmdC1hbW91bnQpICogdmFyKC0tdG9hc3RzLWJlZm9yZSkpKSBzY2FsZShjYWxjKC0xICogdmFyKC0tc2NhbGUpKSk7aGVpZ2h0OnZhcigtLWZyb250LXRvYXN0LWhlaWdodCl9W2RhdGEtc29ubmVyLXRvYXN0XT4qe3RyYW5zaXRpb246b3BhY2l0eSAuNHN9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXgtcG9zaXRpb249cmlnaHRde3JpZ2h0OjB9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXgtcG9zaXRpb249bGVmdF17bGVmdDowfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1leHBhbmRlZD1mYWxzZV1bZGF0YS1mcm9udD1mYWxzZV1bZGF0YS1zdHlsZWQ9dHJ1ZV0+KntvcGFjaXR5OjB9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXZpc2libGU9ZmFsc2Vde29wYWNpdHk6MDtwb2ludGVyLWV2ZW50czpub25lfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1tb3VudGVkPXRydWVdW2RhdGEtZXhwYW5kZWQ9dHJ1ZV17LS15OnRyYW5zbGF0ZVkoY2FsYyh2YXIoLS1saWZ0KSAqIHZhcigtLW9mZnNldCkpKTtoZWlnaHQ6dmFyKC0taW5pdGlhbC1oZWlnaHQpfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1yZW1vdmVkPXRydWVdW2RhdGEtZnJvbnQ9dHJ1ZV1bZGF0YS1zd2lwZS1vdXQ9ZmFsc2Vdey0teTp0cmFuc2xhdGVZKGNhbGModmFyKC0tbGlmdCkgKiAtMTAwJSkpO29wYWNpdHk6MH1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtcmVtb3ZlZD10cnVlXVtkYXRhLWZyb250PWZhbHNlXVtkYXRhLXN3aXBlLW91dD1mYWxzZV1bZGF0YS1leHBhbmRlZD10cnVlXXstLXk6dHJhbnNsYXRlWShjYWxjKHZhcigtLWxpZnQpICogdmFyKC0tb2Zmc2V0KSArIHZhcigtLWxpZnQpICogLTEwMCUpKTtvcGFjaXR5OjB9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXJlbW92ZWQ9dHJ1ZV1bZGF0YS1mcm9udD1mYWxzZV1bZGF0YS1zd2lwZS1vdXQ9ZmFsc2VdW2RhdGEtZXhwYW5kZWQ9ZmFsc2Vdey0teTp0cmFuc2xhdGVZKDQwJSk7b3BhY2l0eTowO3RyYW5zaXRpb246dHJhbnNmb3JtIC41cyxvcGFjaXR5IC4yc31bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtcmVtb3ZlZD10cnVlXVtkYXRhLWZyb250PWZhbHNlXTo6YmVmb3Jle2hlaWdodDpjYWxjKHZhcigtLWluaXRpYWwtaGVpZ2h0KSArIDIwJSl9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN3aXBpbmc9dHJ1ZV17dHJhbnNmb3JtOnZhcigtLXkpIHRyYW5zbGF0ZVkodmFyKC0tc3dpcGUtYW1vdW50LXksMCkpIHRyYW5zbGF0ZVgodmFyKC0tc3dpcGUtYW1vdW50LXgsMCkpO3RyYW5zaXRpb246bm9uZX1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3dpcGVkPXRydWVde3VzZXItc2VsZWN0Om5vbmV9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN3aXBlLW91dD10cnVlXVtkYXRhLXktcG9zaXRpb249Ym90dG9tXSxbZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3dpcGUtb3V0PXRydWVdW2RhdGEteS1wb3NpdGlvbj10b3Bde2FuaW1hdGlvbi1kdXJhdGlvbjouMnM7YW5pbWF0aW9uLXRpbWluZy1mdW5jdGlvbjplYXNlLW91dDthbmltYXRpb24tZmlsbC1tb2RlOmZvcndhcmRzfVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1zd2lwZS1vdXQ9dHJ1ZV1bZGF0YS1zd2lwZS1kaXJlY3Rpb249bGVmdF17YW5pbWF0aW9uLW5hbWU6c3dpcGUtb3V0LWxlZnR9W2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXN3aXBlLW91dD10cnVlXVtkYXRhLXN3aXBlLWRpcmVjdGlvbj1yaWdodF17YW5pbWF0aW9uLW5hbWU6c3dpcGUtb3V0LXJpZ2h0fVtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1zd2lwZS1vdXQ9dHJ1ZV1bZGF0YS1zd2lwZS1kaXJlY3Rpb249dXBde2FuaW1hdGlvbi1uYW1lOnN3aXBlLW91dC11cH1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtc3dpcGUtb3V0PXRydWVdW2RhdGEtc3dpcGUtZGlyZWN0aW9uPWRvd25de2FuaW1hdGlvbi1uYW1lOnN3aXBlLW91dC1kb3dufUBrZXlmcmFtZXMgc3dpcGUtb3V0LWxlZnR7ZnJvbXt0cmFuc2Zvcm06dmFyKC0teSkgdHJhbnNsYXRlWCh2YXIoLS1zd2lwZS1hbW91bnQteCkpO29wYWNpdHk6MX10b3t0cmFuc2Zvcm06dmFyKC0teSkgdHJhbnNsYXRlWChjYWxjKHZhcigtLXN3aXBlLWFtb3VudC14KSAtIDEwMCUpKTtvcGFjaXR5OjB9fUBrZXlmcmFtZXMgc3dpcGUtb3V0LXJpZ2h0e2Zyb217dHJhbnNmb3JtOnZhcigtLXkpIHRyYW5zbGF0ZVgodmFyKC0tc3dpcGUtYW1vdW50LXgpKTtvcGFjaXR5OjF9dG97dHJhbnNmb3JtOnZhcigtLXkpIHRyYW5zbGF0ZVgoY2FsYyh2YXIoLS1zd2lwZS1hbW91bnQteCkgKyAxMDAlKSk7b3BhY2l0eTowfX1Aa2V5ZnJhbWVzIHN3aXBlLW91dC11cHtmcm9te3RyYW5zZm9ybTp2YXIoLS15KSB0cmFuc2xhdGVZKHZhcigtLXN3aXBlLWFtb3VudC15KSk7b3BhY2l0eToxfXRve3RyYW5zZm9ybTp2YXIoLS15KSB0cmFuc2xhdGVZKGNhbGModmFyKC0tc3dpcGUtYW1vdW50LXkpIC0gMTAwJSkpO29wYWNpdHk6MH19QGtleWZyYW1lcyBzd2lwZS1vdXQtZG93bntmcm9te3RyYW5zZm9ybTp2YXIoLS15KSB0cmFuc2xhdGVZKHZhcigtLXN3aXBlLWFtb3VudC15KSk7b3BhY2l0eToxfXRve3RyYW5zZm9ybTp2YXIoLS15KSB0cmFuc2xhdGVZKGNhbGModmFyKC0tc3dpcGUtYW1vdW50LXkpICsgMTAwJSkpO29wYWNpdHk6MH19QG1lZGlhIChtYXgtd2lkdGg6NjAwcHgpe1tkYXRhLXNvbm5lci10b2FzdGVyXXtwb3NpdGlvbjpmaXhlZDtyaWdodDp2YXIoLS1tb2JpbGUtb2Zmc2V0LXJpZ2h0KTtsZWZ0OnZhcigtLW1vYmlsZS1vZmZzZXQtbGVmdCk7d2lkdGg6MTAwJX1bZGF0YS1zb25uZXItdG9hc3Rlcl1bZGlyPXJ0bF17bGVmdDpjYWxjKHZhcigtLW1vYmlsZS1vZmZzZXQtbGVmdCkgKiAtMSl9W2RhdGEtc29ubmVyLXRvYXN0ZXJdIFtkYXRhLXNvbm5lci10b2FzdF17bGVmdDowO3JpZ2h0OjA7d2lkdGg6Y2FsYygxMDAlIC0gdmFyKC0tbW9iaWxlLW9mZnNldC1sZWZ0KSAqIDIpfVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXgtcG9zaXRpb249bGVmdF17bGVmdDp2YXIoLS1tb2JpbGUtb2Zmc2V0LWxlZnQpfVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXktcG9zaXRpb249Ym90dG9tXXtib3R0b206dmFyKC0tbW9iaWxlLW9mZnNldC1ib3R0b20pfVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXktcG9zaXRpb249dG9wXXt0b3A6dmFyKC0tbW9iaWxlLW9mZnNldC10b3ApfVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXgtcG9zaXRpb249Y2VudGVyXXtsZWZ0OnZhcigtLW1vYmlsZS1vZmZzZXQtbGVmdCk7cmlnaHQ6dmFyKC0tbW9iaWxlLW9mZnNldC1yaWdodCk7dHJhbnNmb3JtOm5vbmV9fVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXNvbm5lci10aGVtZT1saWdodF17LS1ub3JtYWwtYmc6I2ZmZjstLW5vcm1hbC1ib3JkZXI6dmFyKC0tZ3JheTQpOy0tbm9ybWFsLXRleHQ6dmFyKC0tZ3JheTEyKTstLXN1Y2Nlc3MtYmc6aHNsKDE0MywgODUlLCA5NiUpOy0tc3VjY2Vzcy1ib3JkZXI6aHNsKDE0NSwgOTIlLCA4NyUpOy0tc3VjY2Vzcy10ZXh0OmhzbCgxNDAsIDEwMCUsIDI3JSk7LS1pbmZvLWJnOmhzbCgyMDgsIDEwMCUsIDk3JSk7LS1pbmZvLWJvcmRlcjpoc2woMjIxLCA5MSUsIDkzJSk7LS1pbmZvLXRleHQ6aHNsKDIxMCwgOTIlLCA0NSUpOy0td2FybmluZy1iZzpoc2woNDksIDEwMCUsIDk3JSk7LS13YXJuaW5nLWJvcmRlcjpoc2woNDksIDkxJSwgODQlKTstLXdhcm5pbmctdGV4dDpoc2woMzEsIDkyJSwgNDUlKTstLWVycm9yLWJnOmhzbCgzNTksIDEwMCUsIDk3JSk7LS1lcnJvci1ib3JkZXI6aHNsKDM1OSwgMTAwJSwgOTQlKTstLWVycm9yLXRleHQ6aHNsKDM2MCwgMTAwJSwgNDUlKX1bZGF0YS1zb25uZXItdG9hc3Rlcl1bZGF0YS1zb25uZXItdGhlbWU9bGlnaHRdIFtkYXRhLXNvbm5lci10b2FzdF1bZGF0YS1pbnZlcnQ9dHJ1ZV17LS1ub3JtYWwtYmc6IzAwMDstLW5vcm1hbC1ib3JkZXI6aHNsKDAsIDAlLCAyMCUpOy0tbm9ybWFsLXRleHQ6dmFyKC0tZ3JheTEpfVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXNvbm5lci10aGVtZT1kYXJrXSBbZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtaW52ZXJ0PXRydWVdey0tbm9ybWFsLWJnOiNmZmY7LS1ub3JtYWwtYm9yZGVyOnZhcigtLWdyYXkzKTstLW5vcm1hbC10ZXh0OnZhcigtLWdyYXkxMil9W2RhdGEtc29ubmVyLXRvYXN0ZXJdW2RhdGEtc29ubmVyLXRoZW1lPWRhcmtdey0tbm9ybWFsLWJnOiMwMDA7LS1ub3JtYWwtYmctaG92ZXI6aHNsKDAsIDAlLCAxMiUpOy0tbm9ybWFsLWJvcmRlcjpoc2woMCwgMCUsIDIwJSk7LS1ub3JtYWwtYm9yZGVyLWhvdmVyOmhzbCgwLCAwJSwgMjUlKTstLW5vcm1hbC10ZXh0OnZhcigtLWdyYXkxKTstLXN1Y2Nlc3MtYmc6aHNsKDE1MCwgMTAwJSwgNiUpOy0tc3VjY2Vzcy1ib3JkZXI6aHNsKDE0NywgMTAwJSwgMTIlKTstLXN1Y2Nlc3MtdGV4dDpoc2woMTUwLCA4NiUsIDY1JSk7LS1pbmZvLWJnOmhzbCgyMTUsIDEwMCUsIDYlKTstLWluZm8tYm9yZGVyOmhzbCgyMjMsIDQzJSwgMTclKTstLWluZm8tdGV4dDpoc2woMjE2LCA4NyUsIDY1JSk7LS13YXJuaW5nLWJnOmhzbCg2NCwgMTAwJSwgNiUpOy0td2FybmluZy1ib3JkZXI6aHNsKDYwLCAxMDAlLCA5JSk7LS13YXJuaW5nLXRleHQ6aHNsKDQ2LCA4NyUsIDY1JSk7LS1lcnJvci1iZzpoc2woMzU4LCA3NiUsIDEwJSk7LS1lcnJvci1ib3JkZXI6aHNsKDM1NywgODklLCAxNiUpOy0tZXJyb3ItdGV4dDpoc2woMzU4LCAxMDAlLCA4MSUpfVtkYXRhLXNvbm5lci10b2FzdGVyXVtkYXRhLXNvbm5lci10aGVtZT1kYXJrXSBbZGF0YS1zb25uZXItdG9hc3RdIFtkYXRhLWNsb3NlLWJ1dHRvbl17YmFja2dyb3VuZDp2YXIoLS1ub3JtYWwtYmcpO2JvcmRlci1jb2xvcjp2YXIoLS1ub3JtYWwtYm9yZGVyKTtjb2xvcjp2YXIoLS1ub3JtYWwtdGV4dCl9W2RhdGEtc29ubmVyLXRvYXN0ZXJdW2RhdGEtc29ubmVyLXRoZW1lPWRhcmtdIFtkYXRhLXNvbm5lci10b2FzdF0gW2RhdGEtY2xvc2UtYnV0dG9uXTpob3ZlcntiYWNrZ3JvdW5kOnZhcigtLW5vcm1hbC1iZy1ob3Zlcik7Ym9yZGVyLWNvbG9yOnZhcigtLW5vcm1hbC1ib3JkZXItaG92ZXIpfVtkYXRhLXJpY2gtY29sb3JzPXRydWVdW2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXR5cGU9c3VjY2Vzc117YmFja2dyb3VuZDp2YXIoLS1zdWNjZXNzLWJnKTtib3JkZXItY29sb3I6dmFyKC0tc3VjY2Vzcy1ib3JkZXIpO2NvbG9yOnZhcigtLXN1Y2Nlc3MtdGV4dCl9W2RhdGEtcmljaC1jb2xvcnM9dHJ1ZV1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtdHlwZT1zdWNjZXNzXSBbZGF0YS1jbG9zZS1idXR0b25de2JhY2tncm91bmQ6dmFyKC0tc3VjY2Vzcy1iZyk7Ym9yZGVyLWNvbG9yOnZhcigtLXN1Y2Nlc3MtYm9yZGVyKTtjb2xvcjp2YXIoLS1zdWNjZXNzLXRleHQpfVtkYXRhLXJpY2gtY29sb3JzPXRydWVdW2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXR5cGU9aW5mb117YmFja2dyb3VuZDp2YXIoLS1pbmZvLWJnKTtib3JkZXItY29sb3I6dmFyKC0taW5mby1ib3JkZXIpO2NvbG9yOnZhcigtLWluZm8tdGV4dCl9W2RhdGEtcmljaC1jb2xvcnM9dHJ1ZV1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtdHlwZT1pbmZvXSBbZGF0YS1jbG9zZS1idXR0b25de2JhY2tncm91bmQ6dmFyKC0taW5mby1iZyk7Ym9yZGVyLWNvbG9yOnZhcigtLWluZm8tYm9yZGVyKTtjb2xvcjp2YXIoLS1pbmZvLXRleHQpfVtkYXRhLXJpY2gtY29sb3JzPXRydWVdW2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXR5cGU9d2FybmluZ117YmFja2dyb3VuZDp2YXIoLS13YXJuaW5nLWJnKTtib3JkZXItY29sb3I6dmFyKC0td2FybmluZy1ib3JkZXIpO2NvbG9yOnZhcigtLXdhcm5pbmctdGV4dCl9W2RhdGEtcmljaC1jb2xvcnM9dHJ1ZV1bZGF0YS1zb25uZXItdG9hc3RdW2RhdGEtdHlwZT13YXJuaW5nXSBbZGF0YS1jbG9zZS1idXR0b25de2JhY2tncm91bmQ6dmFyKC0td2FybmluZy1iZyk7Ym9yZGVyLWNvbG9yOnZhcigtLXdhcm5pbmctYm9yZGVyKTtjb2xvcjp2YXIoLS13YXJuaW5nLXRleHQpfVtkYXRhLXJpY2gtY29sb3JzPXRydWVdW2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXR5cGU9ZXJyb3Jde2JhY2tncm91bmQ6dmFyKC0tZXJyb3ItYmcpO2JvcmRlci1jb2xvcjp2YXIoLS1lcnJvci1ib3JkZXIpO2NvbG9yOnZhcigtLWVycm9yLXRleHQpfVtkYXRhLXJpY2gtY29sb3JzPXRydWVdW2RhdGEtc29ubmVyLXRvYXN0XVtkYXRhLXR5cGU9ZXJyb3JdIFtkYXRhLWNsb3NlLWJ1dHRvbl17YmFja2dyb3VuZDp2YXIoLS1lcnJvci1iZyk7Ym9yZGVyLWNvbG9yOnZhcigtLWVycm9yLWJvcmRlcik7Y29sb3I6dmFyKC0tZXJyb3ItdGV4dCl9LnNvbm5lci1sb2FkaW5nLXdyYXBwZXJ7LS1zaXplOjE2cHg7aGVpZ2h0OnZhcigtLXNpemUpO3dpZHRoOnZhcigtLXNpemUpO3Bvc2l0aW9uOmFic29sdXRlO2luc2V0OjA7ei1pbmRleDoxMH0uc29ubmVyLWxvYWRpbmctd3JhcHBlcltkYXRhLXZpc2libGU9ZmFsc2Vde3RyYW5zZm9ybS1vcmlnaW46Y2VudGVyO2FuaW1hdGlvbjpzb25uZXItZmFkZS1vdXQgLjJzIGVhc2UgZm9yd2FyZHN9LnNvbm5lci1zcGlubmVye3Bvc2l0aW9uOnJlbGF0aXZlO3RvcDo1MCU7bGVmdDo1MCU7aGVpZ2h0OnZhcigtLXNpemUpO3dpZHRoOnZhcigtLXNpemUpfS5zb25uZXItbG9hZGluZy1iYXJ7YW5pbWF0aW9uOnNvbm5lci1zcGluIDEuMnMgbGluZWFyIGluZmluaXRlO2JhY2tncm91bmQ6dmFyKC0tZ3JheTExKTtib3JkZXItcmFkaXVzOjZweDtoZWlnaHQ6OCU7bGVmdDotMTAlO3Bvc2l0aW9uOmFic29sdXRlO3RvcDotMy45JTt3aWR0aDoyNCV9LnNvbm5lci1sb2FkaW5nLWJhcjpmaXJzdC1jaGlsZHthbmltYXRpb24tZGVsYXk6LTEuMnM7dHJhbnNmb3JtOnJvdGF0ZSguMDAwMWRlZykgdHJhbnNsYXRlKDE0NiUpfS5zb25uZXItbG9hZGluZy1iYXI6bnRoLWNoaWxkKDIpe2FuaW1hdGlvbi1kZWxheTotMS4xczt0cmFuc2Zvcm06cm90YXRlKDMwZGVnKSB0cmFuc2xhdGUoMTQ2JSl9LnNvbm5lci1sb2FkaW5nLWJhcjpudGgtY2hpbGQoMyl7YW5pbWF0aW9uLWRlbGF5Oi0xczt0cmFuc2Zvcm06cm90YXRlKDYwZGVnKSB0cmFuc2xhdGUoMTQ2JSl9LnNvbm5lci1sb2FkaW5nLWJhcjpudGgtY2hpbGQoNCl7YW5pbWF0aW9uLWRlbGF5Oi0uOXM7dHJhbnNmb3JtOnJvdGF0ZSg5MGRlZykgdHJhbnNsYXRlKDE0NiUpfS5zb25uZXItbG9hZGluZy1iYXI6bnRoLWNoaWxkKDUpe2FuaW1hdGlvbi1kZWxheTotLjhzO3RyYW5zZm9ybTpyb3RhdGUoMTIwZGVnKSB0cmFuc2xhdGUoMTQ2JSl9LnNvbm5lci1sb2FkaW5nLWJhcjpudGgtY2hpbGQoNil7YW5pbWF0aW9uLWRlbGF5Oi0uN3M7dHJhbnNmb3JtOnJvdGF0ZSgxNTBkZWcpIHRyYW5zbGF0ZSgxNDYlKX0uc29ubmVyLWxvYWRpbmctYmFyOm50aC1jaGlsZCg3KXthbmltYXRpb24tZGVsYXk6LS42czt0cmFuc2Zvcm06cm90YXRlKDE4MGRlZykgdHJhbnNsYXRlKDE0NiUpfS5zb25uZXItbG9hZGluZy1iYXI6bnRoLWNoaWxkKDgpe2FuaW1hdGlvbi1kZWxheTotLjVzO3RyYW5zZm9ybTpyb3RhdGUoMjEwZGVnKSB0cmFuc2xhdGUoMTQ2JSl9LnNvbm5lci1sb2FkaW5nLWJhcjpudGgtY2hpbGQoOSl7YW5pbWF0aW9uLWRlbGF5Oi0uNHM7dHJhbnNmb3JtOnJvdGF0ZSgyNDBkZWcpIHRyYW5zbGF0ZSgxNDYlKX0uc29ubmVyLWxvYWRpbmctYmFyOm50aC1jaGlsZCgxMCl7YW5pbWF0aW9uLWRlbGF5Oi0uM3M7dHJhbnNmb3JtOnJvdGF0ZSgyNzBkZWcpIHRyYW5zbGF0ZSgxNDYlKX0uc29ubmVyLWxvYWRpbmctYmFyOm50aC1jaGlsZCgxMSl7YW5pbWF0aW9uLWRlbGF5Oi0uMnM7dHJhbnNmb3JtOnJvdGF0ZSgzMDBkZWcpIHRyYW5zbGF0ZSgxNDYlKX0uc29ubmVyLWxvYWRpbmctYmFyOm50aC1jaGlsZCgxMil7YW5pbWF0aW9uLWRlbGF5Oi0uMXM7dHJhbnNmb3JtOnJvdGF0ZSgzMzBkZWcpIHRyYW5zbGF0ZSgxNDYlKX1Aa2V5ZnJhbWVzIHNvbm5lci1mYWRlLWluezAle29wYWNpdHk6MDt0cmFuc2Zvcm06c2NhbGUoLjgpfTEwMCV7b3BhY2l0eToxO3RyYW5zZm9ybTpzY2FsZSgxKX19QGtleWZyYW1lcyBzb25uZXItZmFkZS1vdXR7MCV7b3BhY2l0eToxO3RyYW5zZm9ybTpzY2FsZSgxKX0xMDAle29wYWNpdHk6MDt0cmFuc2Zvcm06c2NhbGUoLjgpfX1Aa2V5ZnJhbWVzIHNvbm5lci1zcGluezAle29wYWNpdHk6MX0xMDAle29wYWNpdHk6LjE1fX1AbWVkaWEgKHByZWZlcnMtcmVkdWNlZC1tb3Rpb24pey5zb25uZXItbG9hZGluZy1iYXIsW2RhdGEtc29ubmVyLXRvYXN0XSxbZGF0YS1zb25uZXItdG9hc3RdPip7dHJhbnNpdGlvbjpub25lIWltcG9ydGFudDthbmltYXRpb246bm9uZSFpbXBvcnRhbnR9fS5zb25uZXItbG9hZGVye3Bvc2l0aW9uOmFic29sdXRlO3RvcDo1MCU7bGVmdDo1MCU7dHJhbnNmb3JtOnRyYW5zbGF0ZSgtNTAlLC01MCUpO3RyYW5zZm9ybS1vcmlnaW46Y2VudGVyO3RyYW5zaXRpb246b3BhY2l0eSAuMnMsdHJhbnNmb3JtIC4yc30uc29ubmVyLWxvYWRlcltkYXRhLXZpc2libGU9ZmFsc2Vde29wYWNpdHk6MDt0cmFuc2Zvcm06c2NhbGUoLjgpIHRyYW5zbGF0ZSgtNTAlLC01MCUpfVwiKTtcblxuZnVuY3Rpb24gaXNBY3Rpb24oYWN0aW9uKSB7XG4gICAgcmV0dXJuIGFjdGlvbi5sYWJlbCAhPT0gdW5kZWZpbmVkO1xufVxuXG4vLyBWaXNpYmxlIHRvYXN0cyBhbW91bnRcbmNvbnN0IFZJU0lCTEVfVE9BU1RTX0FNT1VOVCA9IDM7XG4vLyBWaWV3cG9ydCBwYWRkaW5nXG5jb25zdCBWSUVXUE9SVF9PRkZTRVQgPSAnMjRweCc7XG4vLyBNb2JpbGUgdmlld3BvcnQgcGFkZGluZ1xuY29uc3QgTU9CSUxFX1ZJRVdQT1JUX09GRlNFVCA9ICcxNnB4Jztcbi8vIERlZmF1bHQgbGlmZXRpbWUgb2YgYSB0b2FzdHMgKGluIG1zKVxuY29uc3QgVE9BU1RfTElGRVRJTUUgPSA0MDAwO1xuLy8gRGVmYXVsdCB0b2FzdCB3aWR0aFxuY29uc3QgVE9BU1RfV0lEVEggPSAzNTY7XG4vLyBEZWZhdWx0IGdhcCBiZXR3ZWVuIHRvYXN0c1xuY29uc3QgR0FQID0gMTQ7XG4vLyBUaHJlc2hvbGQgdG8gZGlzbWlzcyBhIHRvYXN0XG5jb25zdCBTV0lQRV9USFJFU0hPTEQgPSA0NTtcbi8vIEVxdWFsIHRvIGV4aXQgYW5pbWF0aW9uIGR1cmF0aW9uXG5jb25zdCBUSU1FX0JFRk9SRV9VTk1PVU5UID0gMjAwO1xuZnVuY3Rpb24gY24oLi4uY2xhc3Nlcykge1xuICAgIHJldHVybiBjbGFzc2VzLmZpbHRlcihCb29sZWFuKS5qb2luKCcgJyk7XG59XG5mdW5jdGlvbiBnZXREZWZhdWx0U3dpcGVEaXJlY3Rpb25zKHBvc2l0aW9uKSB7XG4gICAgY29uc3QgW3ksIHhdID0gcG9zaXRpb24uc3BsaXQoJy0nKTtcbiAgICBjb25zdCBkaXJlY3Rpb25zID0gW107XG4gICAgaWYgKHkpIHtcbiAgICAgICAgZGlyZWN0aW9ucy5wdXNoKHkpO1xuICAgIH1cbiAgICBpZiAoeCkge1xuICAgICAgICBkaXJlY3Rpb25zLnB1c2goeCk7XG4gICAgfVxuICAgIHJldHVybiBkaXJlY3Rpb25zO1xufVxuY29uc3QgVG9hc3QgPSAocHJvcHMpPT57XG4gICAgdmFyIF90b2FzdF9jbGFzc05hbWVzLCBfdG9hc3RfY2xhc3NOYW1lczEsIF90b2FzdF9jbGFzc05hbWVzMiwgX3RvYXN0X2NsYXNzTmFtZXMzLCBfdG9hc3RfY2xhc3NOYW1lczQsIF90b2FzdF9jbGFzc05hbWVzNSwgX3RvYXN0X2NsYXNzTmFtZXM2LCBfdG9hc3RfY2xhc3NOYW1lczcsIF90b2FzdF9jbGFzc05hbWVzODtcbiAgICBjb25zdCB7IGludmVydDogVG9hc3RlckludmVydCwgdG9hc3QsIHVuc3R5bGVkLCBpbnRlcmFjdGluZywgc2V0SGVpZ2h0cywgdmlzaWJsZVRvYXN0cywgaGVpZ2h0cywgaW5kZXgsIHRvYXN0cywgZXhwYW5kZWQsIHJlbW92ZVRvYXN0LCBkZWZhdWx0UmljaENvbG9ycywgY2xvc2VCdXR0b246IGNsb3NlQnV0dG9uRnJvbVRvYXN0ZXIsIHN0eWxlLCBjYW5jZWxCdXR0b25TdHlsZSwgYWN0aW9uQnV0dG9uU3R5bGUsIGNsYXNzTmFtZSA9ICcnLCBkZXNjcmlwdGlvbkNsYXNzTmFtZSA9ICcnLCBkdXJhdGlvbjogZHVyYXRpb25Gcm9tVG9hc3RlciwgcG9zaXRpb24sIGdhcCwgZXhwYW5kQnlEZWZhdWx0LCBjbGFzc05hbWVzLCBpY29ucywgY2xvc2VCdXR0b25BcmlhTGFiZWwgPSAnQ2xvc2UgdG9hc3QnIH0gPSBwcm9wcztcbiAgICBjb25zdCBbc3dpcGVEaXJlY3Rpb24sIHNldFN3aXBlRGlyZWN0aW9uXSA9IFJlYWN0LnVzZVN0YXRlKG51bGwpO1xuICAgIGNvbnN0IFtzd2lwZU91dERpcmVjdGlvbiwgc2V0U3dpcGVPdXREaXJlY3Rpb25dID0gUmVhY3QudXNlU3RhdGUobnVsbCk7XG4gICAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpO1xuICAgIGNvbnN0IFtyZW1vdmVkLCBzZXRSZW1vdmVkXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcbiAgICBjb25zdCBbc3dpcGluZywgc2V0U3dpcGluZ10gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW3N3aXBlT3V0LCBzZXRTd2lwZU91dF0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW2lzU3dpcGVkLCBzZXRJc1N3aXBlZF0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW29mZnNldEJlZm9yZVJlbW92ZSwgc2V0T2Zmc2V0QmVmb3JlUmVtb3ZlXSA9IFJlYWN0LnVzZVN0YXRlKDApO1xuICAgIGNvbnN0IFtpbml0aWFsSGVpZ2h0LCBzZXRJbml0aWFsSGVpZ2h0XSA9IFJlYWN0LnVzZVN0YXRlKDApO1xuICAgIGNvbnN0IHJlbWFpbmluZ1RpbWUgPSBSZWFjdC51c2VSZWYodG9hc3QuZHVyYXRpb24gfHwgZHVyYXRpb25Gcm9tVG9hc3RlciB8fCBUT0FTVF9MSUZFVElNRSk7XG4gICAgY29uc3QgZHJhZ1N0YXJ0VGltZSA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgICBjb25zdCB0b2FzdFJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgICBjb25zdCBpc0Zyb250ID0gaW5kZXggPT09IDA7XG4gICAgY29uc3QgaXNWaXNpYmxlID0gaW5kZXggKyAxIDw9IHZpc2libGVUb2FzdHM7XG4gICAgY29uc3QgdG9hc3RUeXBlID0gdG9hc3QudHlwZTtcbiAgICBjb25zdCBkaXNtaXNzaWJsZSA9IHRvYXN0LmRpc21pc3NpYmxlICE9PSBmYWxzZTtcbiAgICBjb25zdCB0b2FzdENsYXNzbmFtZSA9IHRvYXN0LmNsYXNzTmFtZSB8fCAnJztcbiAgICBjb25zdCB0b2FzdERlc2NyaXB0aW9uQ2xhc3NuYW1lID0gdG9hc3QuZGVzY3JpcHRpb25DbGFzc05hbWUgfHwgJyc7XG4gICAgLy8gSGVpZ2h0IGluZGV4IGlzIHVzZWQgdG8gY2FsY3VsYXRlIHRoZSBvZmZzZXQgYXMgaXQgZ2V0cyB1cGRhdGVkIGJlZm9yZSB0aGUgdG9hc3QgYXJyYXksIHdoaWNoIG1lYW5zIHdlIGNhbiBjYWxjdWxhdGUgdGhlIG5ldyBsYXlvdXQgZmFzdGVyLlxuICAgIGNvbnN0IGhlaWdodEluZGV4ID0gUmVhY3QudXNlTWVtbygoKT0+aGVpZ2h0cy5maW5kSW5kZXgoKGhlaWdodCk9PmhlaWdodC50b2FzdElkID09PSB0b2FzdC5pZCkgfHwgMCwgW1xuICAgICAgICBoZWlnaHRzLFxuICAgICAgICB0b2FzdC5pZFxuICAgIF0pO1xuICAgIGNvbnN0IGNsb3NlQnV0dG9uID0gUmVhY3QudXNlTWVtbygoKT0+e1xuICAgICAgICB2YXIgX3RvYXN0X2Nsb3NlQnV0dG9uO1xuICAgICAgICByZXR1cm4gKF90b2FzdF9jbG9zZUJ1dHRvbiA9IHRvYXN0LmNsb3NlQnV0dG9uKSAhPSBudWxsID8gX3RvYXN0X2Nsb3NlQnV0dG9uIDogY2xvc2VCdXR0b25Gcm9tVG9hc3RlcjtcbiAgICB9LCBbXG4gICAgICAgIHRvYXN0LmNsb3NlQnV0dG9uLFxuICAgICAgICBjbG9zZUJ1dHRvbkZyb21Ub2FzdGVyXG4gICAgXSk7XG4gICAgY29uc3QgZHVyYXRpb24gPSBSZWFjdC51c2VNZW1vKCgpPT50b2FzdC5kdXJhdGlvbiB8fCBkdXJhdGlvbkZyb21Ub2FzdGVyIHx8IFRPQVNUX0xJRkVUSU1FLCBbXG4gICAgICAgIHRvYXN0LmR1cmF0aW9uLFxuICAgICAgICBkdXJhdGlvbkZyb21Ub2FzdGVyXG4gICAgXSk7XG4gICAgY29uc3QgY2xvc2VUaW1lclN0YXJ0VGltZVJlZiA9IFJlYWN0LnVzZVJlZigwKTtcbiAgICBjb25zdCBvZmZzZXQgPSBSZWFjdC51c2VSZWYoMCk7XG4gICAgY29uc3QgbGFzdENsb3NlVGltZXJTdGFydFRpbWVSZWYgPSBSZWFjdC51c2VSZWYoMCk7XG4gICAgY29uc3QgcG9pbnRlclN0YXJ0UmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICAgIGNvbnN0IFt5LCB4XSA9IHBvc2l0aW9uLnNwbGl0KCctJyk7XG4gICAgY29uc3QgdG9hc3RzSGVpZ2h0QmVmb3JlID0gUmVhY3QudXNlTWVtbygoKT0+e1xuICAgICAgICByZXR1cm4gaGVpZ2h0cy5yZWR1Y2UoKHByZXYsIGN1cnIsIHJlZHVjZXJJbmRleCk9PntcbiAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBvZmZzZXQgdXAgdW50aWwgY3VycmVudCB0b2FzdFxuICAgICAgICAgICAgaWYgKHJlZHVjZXJJbmRleCA+PSBoZWlnaHRJbmRleCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBwcmV2O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHByZXYgKyBjdXJyLmhlaWdodDtcbiAgICAgICAgfSwgMCk7XG4gICAgfSwgW1xuICAgICAgICBoZWlnaHRzLFxuICAgICAgICBoZWlnaHRJbmRleFxuICAgIF0pO1xuICAgIGNvbnN0IGlzRG9jdW1lbnRIaWRkZW4gPSB1c2VJc0RvY3VtZW50SGlkZGVuKCk7XG4gICAgY29uc3QgaW52ZXJ0ID0gdG9hc3QuaW52ZXJ0IHx8IFRvYXN0ZXJJbnZlcnQ7XG4gICAgY29uc3QgZGlzYWJsZWQgPSB0b2FzdFR5cGUgPT09ICdsb2FkaW5nJztcbiAgICBvZmZzZXQuY3VycmVudCA9IFJlYWN0LnVzZU1lbW8oKCk9PmhlaWdodEluZGV4ICogZ2FwICsgdG9hc3RzSGVpZ2h0QmVmb3JlLCBbXG4gICAgICAgIGhlaWdodEluZGV4LFxuICAgICAgICB0b2FzdHNIZWlnaHRCZWZvcmVcbiAgICBdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCk9PntcbiAgICAgICAgcmVtYWluaW5nVGltZS5jdXJyZW50ID0gZHVyYXRpb247XG4gICAgfSwgW1xuICAgICAgICBkdXJhdGlvblxuICAgIF0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKT0+e1xuICAgICAgICAvLyBUcmlnZ2VyIGVudGVyIGFuaW1hdGlvbiB3aXRob3V0IHVzaW5nIENTUyBhbmltYXRpb25cbiAgICAgICAgc2V0TW91bnRlZCh0cnVlKTtcbiAgICB9LCBbXSk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpPT57XG4gICAgICAgIGNvbnN0IHRvYXN0Tm9kZSA9IHRvYXN0UmVmLmN1cnJlbnQ7XG4gICAgICAgIGlmICh0b2FzdE5vZGUpIHtcbiAgICAgICAgICAgIGNvbnN0IGhlaWdodCA9IHRvYXN0Tm9kZS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKS5oZWlnaHQ7XG4gICAgICAgICAgICAvLyBBZGQgdG9hc3QgaGVpZ2h0IHRvIGhlaWdodHMgYXJyYXkgYWZ0ZXIgdGhlIHRvYXN0IGlzIG1vdW50ZWRcbiAgICAgICAgICAgIHNldEluaXRpYWxIZWlnaHQoaGVpZ2h0KTtcbiAgICAgICAgICAgIHNldEhlaWdodHMoKGgpPT5bXG4gICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvYXN0SWQ6IHRvYXN0LmlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0LFxuICAgICAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IHRvYXN0LnBvc2l0aW9uXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIC4uLmhcbiAgICAgICAgICAgICAgICBdKTtcbiAgICAgICAgICAgIHJldHVybiAoKT0+c2V0SGVpZ2h0cygoaCk9PmguZmlsdGVyKChoZWlnaHQpPT5oZWlnaHQudG9hc3RJZCAhPT0gdG9hc3QuaWQpKTtcbiAgICAgICAgfVxuICAgIH0sIFtcbiAgICAgICAgc2V0SGVpZ2h0cyxcbiAgICAgICAgdG9hc3QuaWRcbiAgICBdKTtcbiAgICBSZWFjdC51c2VMYXlvdXRFZmZlY3QoKCk9PntcbiAgICAgICAgaWYgKCFtb3VudGVkKSByZXR1cm47XG4gICAgICAgIGNvbnN0IHRvYXN0Tm9kZSA9IHRvYXN0UmVmLmN1cnJlbnQ7XG4gICAgICAgIGNvbnN0IG9yaWdpbmFsSGVpZ2h0ID0gdG9hc3ROb2RlLnN0eWxlLmhlaWdodDtcbiAgICAgICAgdG9hc3ROb2RlLnN0eWxlLmhlaWdodCA9ICdhdXRvJztcbiAgICAgICAgY29uc3QgbmV3SGVpZ2h0ID0gdG9hc3ROb2RlLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpLmhlaWdodDtcbiAgICAgICAgdG9hc3ROb2RlLnN0eWxlLmhlaWdodCA9IG9yaWdpbmFsSGVpZ2h0O1xuICAgICAgICBzZXRJbml0aWFsSGVpZ2h0KG5ld0hlaWdodCk7XG4gICAgICAgIHNldEhlaWdodHMoKGhlaWdodHMpPT57XG4gICAgICAgICAgICBjb25zdCBhbHJlYWR5RXhpc3RzID0gaGVpZ2h0cy5maW5kKChoZWlnaHQpPT5oZWlnaHQudG9hc3RJZCA9PT0gdG9hc3QuaWQpO1xuICAgICAgICAgICAgaWYgKCFhbHJlYWR5RXhpc3RzKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgdG9hc3RJZDogdG9hc3QuaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IG5ld0hlaWdodCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiB0b2FzdC5wb3NpdGlvblxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAuLi5oZWlnaHRzXG4gICAgICAgICAgICAgICAgXTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGhlaWdodHMubWFwKChoZWlnaHQpPT5oZWlnaHQudG9hc3RJZCA9PT0gdG9hc3QuaWQgPyB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5oZWlnaHQsXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IG5ld0hlaWdodFxuICAgICAgICAgICAgICAgICAgICB9IDogaGVpZ2h0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfSwgW1xuICAgICAgICBtb3VudGVkLFxuICAgICAgICB0b2FzdC50aXRsZSxcbiAgICAgICAgdG9hc3QuZGVzY3JpcHRpb24sXG4gICAgICAgIHNldEhlaWdodHMsXG4gICAgICAgIHRvYXN0LmlkXG4gICAgXSk7XG4gICAgY29uc3QgZGVsZXRlVG9hc3QgPSBSZWFjdC51c2VDYWxsYmFjaygoKT0+e1xuICAgICAgICAvLyBTYXZlIHRoZSBvZmZzZXQgZm9yIHRoZSBleGl0IHN3aXBlIGFuaW1hdGlvblxuICAgICAgICBzZXRSZW1vdmVkKHRydWUpO1xuICAgICAgICBzZXRPZmZzZXRCZWZvcmVSZW1vdmUob2Zmc2V0LmN1cnJlbnQpO1xuICAgICAgICBzZXRIZWlnaHRzKChoKT0+aC5maWx0ZXIoKGhlaWdodCk9PmhlaWdodC50b2FzdElkICE9PSB0b2FzdC5pZCkpO1xuICAgICAgICBzZXRUaW1lb3V0KCgpPT57XG4gICAgICAgICAgICByZW1vdmVUb2FzdCh0b2FzdCk7XG4gICAgICAgIH0sIFRJTUVfQkVGT1JFX1VOTU9VTlQpO1xuICAgIH0sIFtcbiAgICAgICAgdG9hc3QsXG4gICAgICAgIHJlbW92ZVRvYXN0LFxuICAgICAgICBzZXRIZWlnaHRzLFxuICAgICAgICBvZmZzZXRcbiAgICBdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCk9PntcbiAgICAgICAgaWYgKHRvYXN0LnByb21pc2UgJiYgdG9hc3RUeXBlID09PSAnbG9hZGluZycgfHwgdG9hc3QuZHVyYXRpb24gPT09IEluZmluaXR5IHx8IHRvYXN0LnR5cGUgPT09ICdsb2FkaW5nJykgcmV0dXJuO1xuICAgICAgICBsZXQgdGltZW91dElkO1xuICAgICAgICAvLyBQYXVzZSB0aGUgdGltZXIgb24gZWFjaCBob3ZlclxuICAgICAgICBjb25zdCBwYXVzZVRpbWVyID0gKCk9PntcbiAgICAgICAgICAgIGlmIChsYXN0Q2xvc2VUaW1lclN0YXJ0VGltZVJlZi5jdXJyZW50IDwgY2xvc2VUaW1lclN0YXJ0VGltZVJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgICAgLy8gR2V0IHRoZSBlbGFwc2VkIHRpbWUgc2luY2UgdGhlIHRpbWVyIHN0YXJ0ZWRcbiAgICAgICAgICAgICAgICBjb25zdCBlbGFwc2VkVGltZSA9IG5ldyBEYXRlKCkuZ2V0VGltZSgpIC0gY2xvc2VUaW1lclN0YXJ0VGltZVJlZi5jdXJyZW50O1xuICAgICAgICAgICAgICAgIHJlbWFpbmluZ1RpbWUuY3VycmVudCA9IHJlbWFpbmluZ1RpbWUuY3VycmVudCAtIGVsYXBzZWRUaW1lO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgbGFzdENsb3NlVGltZXJTdGFydFRpbWVSZWYuY3VycmVudCA9IG5ldyBEYXRlKCkuZ2V0VGltZSgpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBzdGFydFRpbWVyID0gKCk9PntcbiAgICAgICAgICAgIC8vIHNldFRpbWVvdXQoLCBJbmZpbml0eSkgYmVoYXZlcyBhcyBpZiB0aGUgZGVsYXkgaXMgMC5cbiAgICAgICAgICAgIC8vIEFzIGEgcmVzdWx0LCB0aGUgdG9hc3Qgd291bGQgYmUgY2xvc2VkIGltbWVkaWF0ZWx5LCBnaXZpbmcgdGhlIGFwcGVhcmFuY2UgdGhhdCBpdCB3YXMgbmV2ZXIgcmVuZGVyZWQuXG4gICAgICAgICAgICAvLyBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS9kZW55c2Rvdmhhbi93dGZqcz90YWI9cmVhZG1lLW92LWZpbGUjYW4taW5maW5pdGUtdGltZW91dFxuICAgICAgICAgICAgaWYgKHJlbWFpbmluZ1RpbWUuY3VycmVudCA9PT0gSW5maW5pdHkpIHJldHVybjtcbiAgICAgICAgICAgIGNsb3NlVGltZXJTdGFydFRpbWVSZWYuY3VycmVudCA9IG5ldyBEYXRlKCkuZ2V0VGltZSgpO1xuICAgICAgICAgICAgLy8gTGV0IHRoZSB0b2FzdCBrbm93IGl0IGhhcyBzdGFydGVkXG4gICAgICAgICAgICB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpPT57XG4gICAgICAgICAgICAgICAgdG9hc3Qub25BdXRvQ2xvc2UgPT0gbnVsbCA/IHZvaWQgMCA6IHRvYXN0Lm9uQXV0b0Nsb3NlLmNhbGwodG9hc3QsIHRvYXN0KTtcbiAgICAgICAgICAgICAgICBkZWxldGVUb2FzdCgpO1xuICAgICAgICAgICAgfSwgcmVtYWluaW5nVGltZS5jdXJyZW50KTtcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKGV4cGFuZGVkIHx8IGludGVyYWN0aW5nIHx8IGlzRG9jdW1lbnRIaWRkZW4pIHtcbiAgICAgICAgICAgIHBhdXNlVGltZXIoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHN0YXJ0VGltZXIoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gKCk9PmNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgIH0sIFtcbiAgICAgICAgZXhwYW5kZWQsXG4gICAgICAgIGludGVyYWN0aW5nLFxuICAgICAgICB0b2FzdCxcbiAgICAgICAgdG9hc3RUeXBlLFxuICAgICAgICBpc0RvY3VtZW50SGlkZGVuLFxuICAgICAgICBkZWxldGVUb2FzdFxuICAgIF0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKT0+e1xuICAgICAgICBpZiAodG9hc3QuZGVsZXRlKSB7XG4gICAgICAgICAgICBkZWxldGVUb2FzdCgpO1xuICAgICAgICB9XG4gICAgfSwgW1xuICAgICAgICBkZWxldGVUb2FzdCxcbiAgICAgICAgdG9hc3QuZGVsZXRlXG4gICAgXSk7XG4gICAgZnVuY3Rpb24gZ2V0TG9hZGluZ0ljb24oKSB7XG4gICAgICAgIHZhciBfdG9hc3RfY2xhc3NOYW1lcztcbiAgICAgICAgaWYgKGljb25zID09IG51bGwgPyB2b2lkIDAgOiBpY29ucy5sb2FkaW5nKSB7XG4gICAgICAgICAgICB2YXIgX3RvYXN0X2NsYXNzTmFtZXMxO1xuICAgICAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lOiBjbihjbGFzc05hbWVzID09IG51bGwgPyB2b2lkIDAgOiBjbGFzc05hbWVzLmxvYWRlciwgdG9hc3QgPT0gbnVsbCA/IHZvaWQgMCA6IChfdG9hc3RfY2xhc3NOYW1lczEgPSB0b2FzdC5jbGFzc05hbWVzKSA9PSBudWxsID8gdm9pZCAwIDogX3RvYXN0X2NsYXNzTmFtZXMxLmxvYWRlciwgJ3Nvbm5lci1sb2FkZXInKSxcbiAgICAgICAgICAgICAgICBcImRhdGEtdmlzaWJsZVwiOiB0b2FzdFR5cGUgPT09ICdsb2FkaW5nJ1xuICAgICAgICAgICAgfSwgaWNvbnMubG9hZGluZyk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChMb2FkZXIsIHtcbiAgICAgICAgICAgIGNsYXNzTmFtZTogY24oY2xhc3NOYW1lcyA9PSBudWxsID8gdm9pZCAwIDogY2xhc3NOYW1lcy5sb2FkZXIsIHRvYXN0ID09IG51bGwgPyB2b2lkIDAgOiAoX3RvYXN0X2NsYXNzTmFtZXMgPSB0b2FzdC5jbGFzc05hbWVzKSA9PSBudWxsID8gdm9pZCAwIDogX3RvYXN0X2NsYXNzTmFtZXMubG9hZGVyKSxcbiAgICAgICAgICAgIHZpc2libGU6IHRvYXN0VHlwZSA9PT0gJ2xvYWRpbmcnXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBjb25zdCBpY29uID0gdG9hc3QuaWNvbiB8fCAoaWNvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IGljb25zW3RvYXN0VHlwZV0pIHx8IGdldEFzc2V0KHRvYXN0VHlwZSk7XG4gICAgdmFyIF90b2FzdF9yaWNoQ29sb3JzLCBfaWNvbnNfY2xvc2U7XG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcImxpXCIsIHtcbiAgICAgICAgdGFiSW5kZXg6IDAsXG4gICAgICAgIHJlZjogdG9hc3RSZWYsXG4gICAgICAgIGNsYXNzTmFtZTogY24oY2xhc3NOYW1lLCB0b2FzdENsYXNzbmFtZSwgY2xhc3NOYW1lcyA9PSBudWxsID8gdm9pZCAwIDogY2xhc3NOYW1lcy50b2FzdCwgdG9hc3QgPT0gbnVsbCA/IHZvaWQgMCA6IChfdG9hc3RfY2xhc3NOYW1lcyA9IHRvYXN0LmNsYXNzTmFtZXMpID09IG51bGwgPyB2b2lkIDAgOiBfdG9hc3RfY2xhc3NOYW1lcy50b2FzdCwgY2xhc3NOYW1lcyA9PSBudWxsID8gdm9pZCAwIDogY2xhc3NOYW1lcy5kZWZhdWx0LCBjbGFzc05hbWVzID09IG51bGwgPyB2b2lkIDAgOiBjbGFzc05hbWVzW3RvYXN0VHlwZV0sIHRvYXN0ID09IG51bGwgPyB2b2lkIDAgOiAoX3RvYXN0X2NsYXNzTmFtZXMxID0gdG9hc3QuY2xhc3NOYW1lcykgPT0gbnVsbCA/IHZvaWQgMCA6IF90b2FzdF9jbGFzc05hbWVzMVt0b2FzdFR5cGVdKSxcbiAgICAgICAgXCJkYXRhLXNvbm5lci10b2FzdFwiOiBcIlwiLFxuICAgICAgICBcImRhdGEtcmljaC1jb2xvcnNcIjogKF90b2FzdF9yaWNoQ29sb3JzID0gdG9hc3QucmljaENvbG9ycykgIT0gbnVsbCA/IF90b2FzdF9yaWNoQ29sb3JzIDogZGVmYXVsdFJpY2hDb2xvcnMsXG4gICAgICAgIFwiZGF0YS1zdHlsZWRcIjogIUJvb2xlYW4odG9hc3QuanN4IHx8IHRvYXN0LnVuc3R5bGVkIHx8IHVuc3R5bGVkKSxcbiAgICAgICAgXCJkYXRhLW1vdW50ZWRcIjogbW91bnRlZCxcbiAgICAgICAgXCJkYXRhLXByb21pc2VcIjogQm9vbGVhbih0b2FzdC5wcm9taXNlKSxcbiAgICAgICAgXCJkYXRhLXN3aXBlZFwiOiBpc1N3aXBlZCxcbiAgICAgICAgXCJkYXRhLXJlbW92ZWRcIjogcmVtb3ZlZCxcbiAgICAgICAgXCJkYXRhLXZpc2libGVcIjogaXNWaXNpYmxlLFxuICAgICAgICBcImRhdGEteS1wb3NpdGlvblwiOiB5LFxuICAgICAgICBcImRhdGEteC1wb3NpdGlvblwiOiB4LFxuICAgICAgICBcImRhdGEtaW5kZXhcIjogaW5kZXgsXG4gICAgICAgIFwiZGF0YS1mcm9udFwiOiBpc0Zyb250LFxuICAgICAgICBcImRhdGEtc3dpcGluZ1wiOiBzd2lwaW5nLFxuICAgICAgICBcImRhdGEtZGlzbWlzc2libGVcIjogZGlzbWlzc2libGUsXG4gICAgICAgIFwiZGF0YS10eXBlXCI6IHRvYXN0VHlwZSxcbiAgICAgICAgXCJkYXRhLWludmVydFwiOiBpbnZlcnQsXG4gICAgICAgIFwiZGF0YS1zd2lwZS1vdXRcIjogc3dpcGVPdXQsXG4gICAgICAgIFwiZGF0YS1zd2lwZS1kaXJlY3Rpb25cIjogc3dpcGVPdXREaXJlY3Rpb24sXG4gICAgICAgIFwiZGF0YS1leHBhbmRlZFwiOiBCb29sZWFuKGV4cGFuZGVkIHx8IGV4cGFuZEJ5RGVmYXVsdCAmJiBtb3VudGVkKSxcbiAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgICctLWluZGV4JzogaW5kZXgsXG4gICAgICAgICAgICAnLS10b2FzdHMtYmVmb3JlJzogaW5kZXgsXG4gICAgICAgICAgICAnLS16LWluZGV4JzogdG9hc3RzLmxlbmd0aCAtIGluZGV4LFxuICAgICAgICAgICAgJy0tb2Zmc2V0JzogYCR7cmVtb3ZlZCA/IG9mZnNldEJlZm9yZVJlbW92ZSA6IG9mZnNldC5jdXJyZW50fXB4YCxcbiAgICAgICAgICAgICctLWluaXRpYWwtaGVpZ2h0JzogZXhwYW5kQnlEZWZhdWx0ID8gJ2F1dG8nIDogYCR7aW5pdGlhbEhlaWdodH1weGAsXG4gICAgICAgICAgICAuLi5zdHlsZSxcbiAgICAgICAgICAgIC4uLnRvYXN0LnN0eWxlXG4gICAgICAgIH0sXG4gICAgICAgIG9uRHJhZ0VuZDogKCk9PntcbiAgICAgICAgICAgIHNldFN3aXBpbmcoZmFsc2UpO1xuICAgICAgICAgICAgc2V0U3dpcGVEaXJlY3Rpb24obnVsbCk7XG4gICAgICAgICAgICBwb2ludGVyU3RhcnRSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgIH0sXG4gICAgICAgIG9uUG9pbnRlckRvd246IChldmVudCk9PntcbiAgICAgICAgICAgIGlmIChkaXNhYmxlZCB8fCAhZGlzbWlzc2libGUpIHJldHVybjtcbiAgICAgICAgICAgIGRyYWdTdGFydFRpbWUuY3VycmVudCA9IG5ldyBEYXRlKCk7XG4gICAgICAgICAgICBzZXRPZmZzZXRCZWZvcmVSZW1vdmUob2Zmc2V0LmN1cnJlbnQpO1xuICAgICAgICAgICAgLy8gRW5zdXJlIHdlIG1haW50YWluIGNvcnJlY3QgcG9pbnRlciBjYXB0dXJlIGV2ZW4gd2hlbiBnb2luZyBvdXRzaWRlIG9mIHRoZSB0b2FzdCAoZS5nLiB3aGVuIHN3aXBpbmcpXG4gICAgICAgICAgICBldmVudC50YXJnZXQuc2V0UG9pbnRlckNhcHR1cmUoZXZlbnQucG9pbnRlcklkKTtcbiAgICAgICAgICAgIGlmIChldmVudC50YXJnZXQudGFnTmFtZSA9PT0gJ0JVVFRPTicpIHJldHVybjtcbiAgICAgICAgICAgIHNldFN3aXBpbmcodHJ1ZSk7XG4gICAgICAgICAgICBwb2ludGVyU3RhcnRSZWYuY3VycmVudCA9IHtcbiAgICAgICAgICAgICAgICB4OiBldmVudC5jbGllbnRYLFxuICAgICAgICAgICAgICAgIHk6IGV2ZW50LmNsaWVudFlcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0sXG4gICAgICAgIG9uUG9pbnRlclVwOiAoKT0+e1xuICAgICAgICAgICAgdmFyIF90b2FzdFJlZl9jdXJyZW50LCBfdG9hc3RSZWZfY3VycmVudDEsIF9kcmFnU3RhcnRUaW1lX2N1cnJlbnQ7XG4gICAgICAgICAgICBpZiAoc3dpcGVPdXQgfHwgIWRpc21pc3NpYmxlKSByZXR1cm47XG4gICAgICAgICAgICBwb2ludGVyU3RhcnRSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgICAgICBjb25zdCBzd2lwZUFtb3VudFggPSBOdW1iZXIoKChfdG9hc3RSZWZfY3VycmVudCA9IHRvYXN0UmVmLmN1cnJlbnQpID09IG51bGwgPyB2b2lkIDAgOiBfdG9hc3RSZWZfY3VycmVudC5zdHlsZS5nZXRQcm9wZXJ0eVZhbHVlKCctLXN3aXBlLWFtb3VudC14JykucmVwbGFjZSgncHgnLCAnJykpIHx8IDApO1xuICAgICAgICAgICAgY29uc3Qgc3dpcGVBbW91bnRZID0gTnVtYmVyKCgoX3RvYXN0UmVmX2N1cnJlbnQxID0gdG9hc3RSZWYuY3VycmVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF90b2FzdFJlZl9jdXJyZW50MS5zdHlsZS5nZXRQcm9wZXJ0eVZhbHVlKCctLXN3aXBlLWFtb3VudC15JykucmVwbGFjZSgncHgnLCAnJykpIHx8IDApO1xuICAgICAgICAgICAgY29uc3QgdGltZVRha2VuID0gbmV3IERhdGUoKS5nZXRUaW1lKCkgLSAoKF9kcmFnU3RhcnRUaW1lX2N1cnJlbnQgPSBkcmFnU3RhcnRUaW1lLmN1cnJlbnQpID09IG51bGwgPyB2b2lkIDAgOiBfZHJhZ1N0YXJ0VGltZV9jdXJyZW50LmdldFRpbWUoKSk7XG4gICAgICAgICAgICBjb25zdCBzd2lwZUFtb3VudCA9IHN3aXBlRGlyZWN0aW9uID09PSAneCcgPyBzd2lwZUFtb3VudFggOiBzd2lwZUFtb3VudFk7XG4gICAgICAgICAgICBjb25zdCB2ZWxvY2l0eSA9IE1hdGguYWJzKHN3aXBlQW1vdW50KSAvIHRpbWVUYWtlbjtcbiAgICAgICAgICAgIGlmIChNYXRoLmFicyhzd2lwZUFtb3VudCkgPj0gU1dJUEVfVEhSRVNIT0xEIHx8IHZlbG9jaXR5ID4gMC4xMSkge1xuICAgICAgICAgICAgICAgIHNldE9mZnNldEJlZm9yZVJlbW92ZShvZmZzZXQuY3VycmVudCk7XG4gICAgICAgICAgICAgICAgdG9hc3Qub25EaXNtaXNzID09IG51bGwgPyB2b2lkIDAgOiB0b2FzdC5vbkRpc21pc3MuY2FsbCh0b2FzdCwgdG9hc3QpO1xuICAgICAgICAgICAgICAgIGlmIChzd2lwZURpcmVjdGlvbiA9PT0gJ3gnKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldFN3aXBlT3V0RGlyZWN0aW9uKHN3aXBlQW1vdW50WCA+IDAgPyAncmlnaHQnIDogJ2xlZnQnKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBzZXRTd2lwZU91dERpcmVjdGlvbihzd2lwZUFtb3VudFkgPiAwID8gJ2Rvd24nIDogJ3VwJyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGRlbGV0ZVRvYXN0KCk7XG4gICAgICAgICAgICAgICAgc2V0U3dpcGVPdXQodHJ1ZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB2YXIgX3RvYXN0UmVmX2N1cnJlbnQyLCBfdG9hc3RSZWZfY3VycmVudDM7XG4gICAgICAgICAgICAgICAgKF90b2FzdFJlZl9jdXJyZW50MiA9IHRvYXN0UmVmLmN1cnJlbnQpID09IG51bGwgPyB2b2lkIDAgOiBfdG9hc3RSZWZfY3VycmVudDIuc3R5bGUuc2V0UHJvcGVydHkoJy0tc3dpcGUtYW1vdW50LXgnLCBgMHB4YCk7XG4gICAgICAgICAgICAgICAgKF90b2FzdFJlZl9jdXJyZW50MyA9IHRvYXN0UmVmLmN1cnJlbnQpID09IG51bGwgPyB2b2lkIDAgOiBfdG9hc3RSZWZfY3VycmVudDMuc3R5bGUuc2V0UHJvcGVydHkoJy0tc3dpcGUtYW1vdW50LXknLCBgMHB4YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzZXRJc1N3aXBlZChmYWxzZSk7XG4gICAgICAgICAgICBzZXRTd2lwaW5nKGZhbHNlKTtcbiAgICAgICAgICAgIHNldFN3aXBlRGlyZWN0aW9uKG51bGwpO1xuICAgICAgICB9LFxuICAgICAgICBvblBvaW50ZXJNb3ZlOiAoZXZlbnQpPT57XG4gICAgICAgICAgICB2YXIgX3dpbmRvd19nZXRTZWxlY3Rpb24sIC8vIEFwcGx5IHRyYW5zZm9ybSB1c2luZyBib3RoIHggYW5kIHkgdmFsdWVzXG4gICAgICAgICAgICBfdG9hc3RSZWZfY3VycmVudCwgX3RvYXN0UmVmX2N1cnJlbnQxO1xuICAgICAgICAgICAgaWYgKCFwb2ludGVyU3RhcnRSZWYuY3VycmVudCB8fCAhZGlzbWlzc2libGUpIHJldHVybjtcbiAgICAgICAgICAgIGNvbnN0IGlzSGlnaGxpZ2h0ZWQgPSAoKF93aW5kb3dfZ2V0U2VsZWN0aW9uID0gd2luZG93LmdldFNlbGVjdGlvbigpKSA9PSBudWxsID8gdm9pZCAwIDogX3dpbmRvd19nZXRTZWxlY3Rpb24udG9TdHJpbmcoKS5sZW5ndGgpID4gMDtcbiAgICAgICAgICAgIGlmIChpc0hpZ2hsaWdodGVkKSByZXR1cm47XG4gICAgICAgICAgICBjb25zdCB5RGVsdGEgPSBldmVudC5jbGllbnRZIC0gcG9pbnRlclN0YXJ0UmVmLmN1cnJlbnQueTtcbiAgICAgICAgICAgIGNvbnN0IHhEZWx0YSA9IGV2ZW50LmNsaWVudFggLSBwb2ludGVyU3RhcnRSZWYuY3VycmVudC54O1xuICAgICAgICAgICAgdmFyIF9wcm9wc19zd2lwZURpcmVjdGlvbnM7XG4gICAgICAgICAgICBjb25zdCBzd2lwZURpcmVjdGlvbnMgPSAoX3Byb3BzX3N3aXBlRGlyZWN0aW9ucyA9IHByb3BzLnN3aXBlRGlyZWN0aW9ucykgIT0gbnVsbCA/IF9wcm9wc19zd2lwZURpcmVjdGlvbnMgOiBnZXREZWZhdWx0U3dpcGVEaXJlY3Rpb25zKHBvc2l0aW9uKTtcbiAgICAgICAgICAgIC8vIERldGVybWluZSBzd2lwZSBkaXJlY3Rpb24gaWYgbm90IGFscmVhZHkgbG9ja2VkXG4gICAgICAgICAgICBpZiAoIXN3aXBlRGlyZWN0aW9uICYmIChNYXRoLmFicyh4RGVsdGEpID4gMSB8fCBNYXRoLmFicyh5RGVsdGEpID4gMSkpIHtcbiAgICAgICAgICAgICAgICBzZXRTd2lwZURpcmVjdGlvbihNYXRoLmFicyh4RGVsdGEpID4gTWF0aC5hYnMoeURlbHRhKSA/ICd4JyA6ICd5Jyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgc3dpcGVBbW91bnQgPSB7XG4gICAgICAgICAgICAgICAgeDogMCxcbiAgICAgICAgICAgICAgICB5OiAwXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgZ2V0RGFtcGVuaW5nID0gKGRlbHRhKT0+e1xuICAgICAgICAgICAgICAgIGNvbnN0IGZhY3RvciA9IE1hdGguYWJzKGRlbHRhKSAvIDIwO1xuICAgICAgICAgICAgICAgIHJldHVybiAxIC8gKDEuNSArIGZhY3Rvcik7XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgLy8gT25seSBhcHBseSBzd2lwZSBpbiB0aGUgbG9ja2VkIGRpcmVjdGlvblxuICAgICAgICAgICAgaWYgKHN3aXBlRGlyZWN0aW9uID09PSAneScpIHtcbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgdmVydGljYWwgc3dpcGVzXG4gICAgICAgICAgICAgICAgaWYgKHN3aXBlRGlyZWN0aW9ucy5pbmNsdWRlcygndG9wJykgfHwgc3dpcGVEaXJlY3Rpb25zLmluY2x1ZGVzKCdib3R0b20nKSkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoc3dpcGVEaXJlY3Rpb25zLmluY2x1ZGVzKCd0b3AnKSAmJiB5RGVsdGEgPCAwIHx8IHN3aXBlRGlyZWN0aW9ucy5pbmNsdWRlcygnYm90dG9tJykgJiYgeURlbHRhID4gMCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3dpcGVBbW91bnQueSA9IHlEZWx0YTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFNtb290aGx5IHRyYW5zaXRpb24gdG8gZGFtcGVuZWQgbW92ZW1lbnRcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRhbXBlbmVkRGVsdGEgPSB5RGVsdGEgKiBnZXREYW1wZW5pbmcoeURlbHRhKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIEVuc3VyZSB3ZSBkb24ndCBqdW1wIHdoZW4gdHJhbnNpdGlvbmluZyB0byBkYW1wZW5lZCBtb3ZlbWVudFxuICAgICAgICAgICAgICAgICAgICAgICAgc3dpcGVBbW91bnQueSA9IE1hdGguYWJzKGRhbXBlbmVkRGVsdGEpIDwgTWF0aC5hYnMoeURlbHRhKSA/IGRhbXBlbmVkRGVsdGEgOiB5RGVsdGE7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2UgaWYgKHN3aXBlRGlyZWN0aW9uID09PSAneCcpIHtcbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgaG9yaXpvbnRhbCBzd2lwZXNcbiAgICAgICAgICAgICAgICBpZiAoc3dpcGVEaXJlY3Rpb25zLmluY2x1ZGVzKCdsZWZ0JykgfHwgc3dpcGVEaXJlY3Rpb25zLmluY2x1ZGVzKCdyaWdodCcpKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChzd2lwZURpcmVjdGlvbnMuaW5jbHVkZXMoJ2xlZnQnKSAmJiB4RGVsdGEgPCAwIHx8IHN3aXBlRGlyZWN0aW9ucy5pbmNsdWRlcygncmlnaHQnKSAmJiB4RGVsdGEgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzd2lwZUFtb3VudC54ID0geERlbHRhO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gU21vb3RobHkgdHJhbnNpdGlvbiB0byBkYW1wZW5lZCBtb3ZlbWVudFxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGFtcGVuZWREZWx0YSA9IHhEZWx0YSAqIGdldERhbXBlbmluZyh4RGVsdGEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gRW5zdXJlIHdlIGRvbid0IGp1bXAgd2hlbiB0cmFuc2l0aW9uaW5nIHRvIGRhbXBlbmVkIG1vdmVtZW50XG4gICAgICAgICAgICAgICAgICAgICAgICBzd2lwZUFtb3VudC54ID0gTWF0aC5hYnMoZGFtcGVuZWREZWx0YSkgPCBNYXRoLmFicyh4RGVsdGEpID8gZGFtcGVuZWREZWx0YSA6IHhEZWx0YTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChNYXRoLmFicyhzd2lwZUFtb3VudC54KSA+IDAgfHwgTWF0aC5hYnMoc3dpcGVBbW91bnQueSkgPiAwKSB7XG4gICAgICAgICAgICAgICAgc2V0SXNTd2lwZWQodHJ1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAoX3RvYXN0UmVmX2N1cnJlbnQgPSB0b2FzdFJlZi5jdXJyZW50KSA9PSBudWxsID8gdm9pZCAwIDogX3RvYXN0UmVmX2N1cnJlbnQuc3R5bGUuc2V0UHJvcGVydHkoJy0tc3dpcGUtYW1vdW50LXgnLCBgJHtzd2lwZUFtb3VudC54fXB4YCk7XG4gICAgICAgICAgICAoX3RvYXN0UmVmX2N1cnJlbnQxID0gdG9hc3RSZWYuY3VycmVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF90b2FzdFJlZl9jdXJyZW50MS5zdHlsZS5zZXRQcm9wZXJ0eSgnLS1zd2lwZS1hbW91bnQteScsIGAke3N3aXBlQW1vdW50Lnl9cHhgKTtcbiAgICAgICAgfVxuICAgIH0sIGNsb3NlQnV0dG9uICYmICF0b2FzdC5qc3ggJiYgdG9hc3RUeXBlICE9PSAnbG9hZGluZycgPyAvKiNfX1BVUkVfXyovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJidXR0b25cIiwge1xuICAgICAgICBcImFyaWEtbGFiZWxcIjogY2xvc2VCdXR0b25BcmlhTGFiZWwsXG4gICAgICAgIFwiZGF0YS1kaXNhYmxlZFwiOiBkaXNhYmxlZCxcbiAgICAgICAgXCJkYXRhLWNsb3NlLWJ1dHRvblwiOiB0cnVlLFxuICAgICAgICBvbkNsaWNrOiBkaXNhYmxlZCB8fCAhZGlzbWlzc2libGUgPyAoKT0+e30gOiAoKT0+e1xuICAgICAgICAgICAgZGVsZXRlVG9hc3QoKTtcbiAgICAgICAgICAgIHRvYXN0Lm9uRGlzbWlzcyA9PSBudWxsID8gdm9pZCAwIDogdG9hc3Qub25EaXNtaXNzLmNhbGwodG9hc3QsIHRvYXN0KTtcbiAgICAgICAgfSxcbiAgICAgICAgY2xhc3NOYW1lOiBjbihjbGFzc05hbWVzID09IG51bGwgPyB2b2lkIDAgOiBjbGFzc05hbWVzLmNsb3NlQnV0dG9uLCB0b2FzdCA9PSBudWxsID8gdm9pZCAwIDogKF90b2FzdF9jbGFzc05hbWVzMiA9IHRvYXN0LmNsYXNzTmFtZXMpID09IG51bGwgPyB2b2lkIDAgOiBfdG9hc3RfY2xhc3NOYW1lczIuY2xvc2VCdXR0b24pXG4gICAgfSwgKF9pY29uc19jbG9zZSA9IGljb25zID09IG51bGwgPyB2b2lkIDAgOiBpY29ucy5jbG9zZSkgIT0gbnVsbCA/IF9pY29uc19jbG9zZSA6IENsb3NlSWNvbikgOiBudWxsLCAodG9hc3RUeXBlIHx8IHRvYXN0Lmljb24gfHwgdG9hc3QucHJvbWlzZSkgJiYgdG9hc3QuaWNvbiAhPT0gbnVsbCAmJiAoKGljb25zID09IG51bGwgPyB2b2lkIDAgOiBpY29uc1t0b2FzdFR5cGVdKSAhPT0gbnVsbCB8fCB0b2FzdC5pY29uKSA/IC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgICAgIFwiZGF0YS1pY29uXCI6IFwiXCIsXG4gICAgICAgIGNsYXNzTmFtZTogY24oY2xhc3NOYW1lcyA9PSBudWxsID8gdm9pZCAwIDogY2xhc3NOYW1lcy5pY29uLCB0b2FzdCA9PSBudWxsID8gdm9pZCAwIDogKF90b2FzdF9jbGFzc05hbWVzMyA9IHRvYXN0LmNsYXNzTmFtZXMpID09IG51bGwgPyB2b2lkIDAgOiBfdG9hc3RfY2xhc3NOYW1lczMuaWNvbilcbiAgICB9LCB0b2FzdC5wcm9taXNlIHx8IHRvYXN0LnR5cGUgPT09ICdsb2FkaW5nJyAmJiAhdG9hc3QuaWNvbiA/IHRvYXN0Lmljb24gfHwgZ2V0TG9hZGluZ0ljb24oKSA6IG51bGwsIHRvYXN0LnR5cGUgIT09ICdsb2FkaW5nJyA/IGljb24gOiBudWxsKSA6IG51bGwsIC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgICAgIFwiZGF0YS1jb250ZW50XCI6IFwiXCIsXG4gICAgICAgIGNsYXNzTmFtZTogY24oY2xhc3NOYW1lcyA9PSBudWxsID8gdm9pZCAwIDogY2xhc3NOYW1lcy5jb250ZW50LCB0b2FzdCA9PSBudWxsID8gdm9pZCAwIDogKF90b2FzdF9jbGFzc05hbWVzNCA9IHRvYXN0LmNsYXNzTmFtZXMpID09IG51bGwgPyB2b2lkIDAgOiBfdG9hc3RfY2xhc3NOYW1lczQuY29udGVudClcbiAgICB9LCAvKiNfX1BVUkVfXyovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgICAgICBcImRhdGEtdGl0bGVcIjogXCJcIixcbiAgICAgICAgY2xhc3NOYW1lOiBjbihjbGFzc05hbWVzID09IG51bGwgPyB2b2lkIDAgOiBjbGFzc05hbWVzLnRpdGxlLCB0b2FzdCA9PSBudWxsID8gdm9pZCAwIDogKF90b2FzdF9jbGFzc05hbWVzNSA9IHRvYXN0LmNsYXNzTmFtZXMpID09IG51bGwgPyB2b2lkIDAgOiBfdG9hc3RfY2xhc3NOYW1lczUudGl0bGUpXG4gICAgfSwgdG9hc3QuanN4ID8gdG9hc3QuanN4IDogdHlwZW9mIHRvYXN0LnRpdGxlID09PSAnZnVuY3Rpb24nID8gdG9hc3QudGl0bGUoKSA6IHRvYXN0LnRpdGxlKSwgdG9hc3QuZGVzY3JpcHRpb24gPyAvKiNfX1BVUkVfXyovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgICAgICBcImRhdGEtZGVzY3JpcHRpb25cIjogXCJcIixcbiAgICAgICAgY2xhc3NOYW1lOiBjbihkZXNjcmlwdGlvbkNsYXNzTmFtZSwgdG9hc3REZXNjcmlwdGlvbkNsYXNzbmFtZSwgY2xhc3NOYW1lcyA9PSBudWxsID8gdm9pZCAwIDogY2xhc3NOYW1lcy5kZXNjcmlwdGlvbiwgdG9hc3QgPT0gbnVsbCA/IHZvaWQgMCA6IChfdG9hc3RfY2xhc3NOYW1lczYgPSB0b2FzdC5jbGFzc05hbWVzKSA9PSBudWxsID8gdm9pZCAwIDogX3RvYXN0X2NsYXNzTmFtZXM2LmRlc2NyaXB0aW9uKVxuICAgIH0sIHR5cGVvZiB0b2FzdC5kZXNjcmlwdGlvbiA9PT0gJ2Z1bmN0aW9uJyA/IHRvYXN0LmRlc2NyaXB0aW9uKCkgOiB0b2FzdC5kZXNjcmlwdGlvbikgOiBudWxsKSwgLyojX19QVVJFX18qLyBSZWFjdC5pc1ZhbGlkRWxlbWVudCh0b2FzdC5jYW5jZWwpID8gdG9hc3QuY2FuY2VsIDogdG9hc3QuY2FuY2VsICYmIGlzQWN0aW9uKHRvYXN0LmNhbmNlbCkgPyAvKiNfX1BVUkVfXyovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJidXR0b25cIiwge1xuICAgICAgICBcImRhdGEtYnV0dG9uXCI6IHRydWUsXG4gICAgICAgIFwiZGF0YS1jYW5jZWxcIjogdHJ1ZSxcbiAgICAgICAgc3R5bGU6IHRvYXN0LmNhbmNlbEJ1dHRvblN0eWxlIHx8IGNhbmNlbEJ1dHRvblN0eWxlLFxuICAgICAgICBvbkNsaWNrOiAoZXZlbnQpPT57XG4gICAgICAgICAgICAvLyBXZSBuZWVkIHRvIGNoZWNrIHR3aWNlIGJlY2F1c2UgdHlwZXNjcmlwdFxuICAgICAgICAgICAgaWYgKCFpc0FjdGlvbih0b2FzdC5jYW5jZWwpKSByZXR1cm47XG4gICAgICAgICAgICBpZiAoIWRpc21pc3NpYmxlKSByZXR1cm47XG4gICAgICAgICAgICB0b2FzdC5jYW5jZWwub25DbGljayA9PSBudWxsID8gdm9pZCAwIDogdG9hc3QuY2FuY2VsLm9uQ2xpY2suY2FsbCh0b2FzdC5jYW5jZWwsIGV2ZW50KTtcbiAgICAgICAgICAgIGRlbGV0ZVRvYXN0KCk7XG4gICAgICAgIH0sXG4gICAgICAgIGNsYXNzTmFtZTogY24oY2xhc3NOYW1lcyA9PSBudWxsID8gdm9pZCAwIDogY2xhc3NOYW1lcy5jYW5jZWxCdXR0b24sIHRvYXN0ID09IG51bGwgPyB2b2lkIDAgOiAoX3RvYXN0X2NsYXNzTmFtZXM3ID0gdG9hc3QuY2xhc3NOYW1lcykgPT0gbnVsbCA/IHZvaWQgMCA6IF90b2FzdF9jbGFzc05hbWVzNy5jYW5jZWxCdXR0b24pXG4gICAgfSwgdG9hc3QuY2FuY2VsLmxhYmVsKSA6IG51bGwsIC8qI19fUFVSRV9fKi8gUmVhY3QuaXNWYWxpZEVsZW1lbnQodG9hc3QuYWN0aW9uKSA/IHRvYXN0LmFjdGlvbiA6IHRvYXN0LmFjdGlvbiAmJiBpc0FjdGlvbih0b2FzdC5hY3Rpb24pID8gLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwiYnV0dG9uXCIsIHtcbiAgICAgICAgXCJkYXRhLWJ1dHRvblwiOiB0cnVlLFxuICAgICAgICBcImRhdGEtYWN0aW9uXCI6IHRydWUsXG4gICAgICAgIHN0eWxlOiB0b2FzdC5hY3Rpb25CdXR0b25TdHlsZSB8fCBhY3Rpb25CdXR0b25TdHlsZSxcbiAgICAgICAgb25DbGljazogKGV2ZW50KT0+e1xuICAgICAgICAgICAgLy8gV2UgbmVlZCB0byBjaGVjayB0d2ljZSBiZWNhdXNlIHR5cGVzY3JpcHRcbiAgICAgICAgICAgIGlmICghaXNBY3Rpb24odG9hc3QuYWN0aW9uKSkgcmV0dXJuO1xuICAgICAgICAgICAgdG9hc3QuYWN0aW9uLm9uQ2xpY2sgPT0gbnVsbCA/IHZvaWQgMCA6IHRvYXN0LmFjdGlvbi5vbkNsaWNrLmNhbGwodG9hc3QuYWN0aW9uLCBldmVudCk7XG4gICAgICAgICAgICBpZiAoZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkgcmV0dXJuO1xuICAgICAgICAgICAgZGVsZXRlVG9hc3QoKTtcbiAgICAgICAgfSxcbiAgICAgICAgY2xhc3NOYW1lOiBjbihjbGFzc05hbWVzID09IG51bGwgPyB2b2lkIDAgOiBjbGFzc05hbWVzLmFjdGlvbkJ1dHRvbiwgdG9hc3QgPT0gbnVsbCA/IHZvaWQgMCA6IChfdG9hc3RfY2xhc3NOYW1lczggPSB0b2FzdC5jbGFzc05hbWVzKSA9PSBudWxsID8gdm9pZCAwIDogX3RvYXN0X2NsYXNzTmFtZXM4LmFjdGlvbkJ1dHRvbilcbiAgICB9LCB0b2FzdC5hY3Rpb24ubGFiZWwpIDogbnVsbCk7XG59O1xuZnVuY3Rpb24gZ2V0RG9jdW1lbnREaXJlY3Rpb24oKSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gJ2x0cic7XG4gICAgaWYgKHR5cGVvZiBkb2N1bWVudCA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiAnbHRyJzsgLy8gRm9yIEZyZXNoIHB1cnBvc2VcbiAgICBjb25zdCBkaXJBdHRyaWJ1dGUgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuZ2V0QXR0cmlidXRlKCdkaXInKTtcbiAgICBpZiAoZGlyQXR0cmlidXRlID09PSAnYXV0bycgfHwgIWRpckF0dHJpYnV0ZSkge1xuICAgICAgICByZXR1cm4gd2luZG93LmdldENvbXB1dGVkU3R5bGUoZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50KS5kaXJlY3Rpb247XG4gICAgfVxuICAgIHJldHVybiBkaXJBdHRyaWJ1dGU7XG59XG5mdW5jdGlvbiBhc3NpZ25PZmZzZXQoZGVmYXVsdE9mZnNldCwgbW9iaWxlT2Zmc2V0KSB7XG4gICAgY29uc3Qgc3R5bGVzID0ge307XG4gICAgW1xuICAgICAgICBkZWZhdWx0T2Zmc2V0LFxuICAgICAgICBtb2JpbGVPZmZzZXRcbiAgICBdLmZvckVhY2goKG9mZnNldCwgaW5kZXgpPT57XG4gICAgICAgIGNvbnN0IGlzTW9iaWxlID0gaW5kZXggPT09IDE7XG4gICAgICAgIGNvbnN0IHByZWZpeCA9IGlzTW9iaWxlID8gJy0tbW9iaWxlLW9mZnNldCcgOiAnLS1vZmZzZXQnO1xuICAgICAgICBjb25zdCBkZWZhdWx0VmFsdWUgPSBpc01vYmlsZSA/IE1PQklMRV9WSUVXUE9SVF9PRkZTRVQgOiBWSUVXUE9SVF9PRkZTRVQ7XG4gICAgICAgIGZ1bmN0aW9uIGFzc2lnbkFsbChvZmZzZXQpIHtcbiAgICAgICAgICAgIFtcbiAgICAgICAgICAgICAgICAndG9wJyxcbiAgICAgICAgICAgICAgICAncmlnaHQnLFxuICAgICAgICAgICAgICAgICdib3R0b20nLFxuICAgICAgICAgICAgICAgICdsZWZ0J1xuICAgICAgICAgICAgXS5mb3JFYWNoKChrZXkpPT57XG4gICAgICAgICAgICAgICAgc3R5bGVzW2Ake3ByZWZpeH0tJHtrZXl9YF0gPSB0eXBlb2Ygb2Zmc2V0ID09PSAnbnVtYmVyJyA/IGAke29mZnNldH1weGAgOiBvZmZzZXQ7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG9mZnNldCA9PT0gJ251bWJlcicgfHwgdHlwZW9mIG9mZnNldCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgIGFzc2lnbkFsbChvZmZzZXQpO1xuICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiBvZmZzZXQgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICBbXG4gICAgICAgICAgICAgICAgJ3RvcCcsXG4gICAgICAgICAgICAgICAgJ3JpZ2h0JyxcbiAgICAgICAgICAgICAgICAnYm90dG9tJyxcbiAgICAgICAgICAgICAgICAnbGVmdCdcbiAgICAgICAgICAgIF0uZm9yRWFjaCgoa2V5KT0+e1xuICAgICAgICAgICAgICAgIGlmIChvZmZzZXRba2V5XSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlc1tgJHtwcmVmaXh9LSR7a2V5fWBdID0gZGVmYXVsdFZhbHVlO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlc1tgJHtwcmVmaXh9LSR7a2V5fWBdID0gdHlwZW9mIG9mZnNldFtrZXldID09PSAnbnVtYmVyJyA/IGAke29mZnNldFtrZXldfXB4YCA6IG9mZnNldFtrZXldO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgYXNzaWduQWxsKGRlZmF1bHRWYWx1ZSk7XG4gICAgICAgIH1cbiAgICB9KTtcbiAgICByZXR1cm4gc3R5bGVzO1xufVxuZnVuY3Rpb24gdXNlU29ubmVyKCkge1xuICAgIGNvbnN0IFthY3RpdmVUb2FzdHMsIHNldEFjdGl2ZVRvYXN0c10gPSBSZWFjdC51c2VTdGF0ZShbXSk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpPT57XG4gICAgICAgIHJldHVybiBUb2FzdFN0YXRlLnN1YnNjcmliZSgodG9hc3QpPT57XG4gICAgICAgICAgICBpZiAodG9hc3QuZGlzbWlzcykge1xuICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCk9PntcbiAgICAgICAgICAgICAgICAgICAgUmVhY3RET00uZmx1c2hTeW5jKCgpPT57XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRBY3RpdmVUb2FzdHMoKHRvYXN0cyk9PnRvYXN0cy5maWx0ZXIoKHQpPT50LmlkICE9PSB0b2FzdC5pZCkpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBQcmV2ZW50IGJhdGNoaW5nLCB0ZW1wIHNvbHV0aW9uLlxuICAgICAgICAgICAgc2V0VGltZW91dCgoKT0+e1xuICAgICAgICAgICAgICAgIFJlYWN0RE9NLmZsdXNoU3luYygoKT0+e1xuICAgICAgICAgICAgICAgICAgICBzZXRBY3RpdmVUb2FzdHMoKHRvYXN0cyk9PntcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4T2ZFeGlzdGluZ1RvYXN0ID0gdG9hc3RzLmZpbmRJbmRleCgodCk9PnQuaWQgPT09IHRvYXN0LmlkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSB0aGUgdG9hc3QgaWYgaXQgYWxyZWFkeSBleGlzdHNcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbmRleE9mRXhpc3RpbmdUb2FzdCAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi50b2FzdHMuc2xpY2UoMCwgaW5kZXhPZkV4aXN0aW5nVG9hc3QpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi50b2FzdHNbaW5kZXhPZkV4aXN0aW5nVG9hc3RdLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udG9hc3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udG9hc3RzLnNsaWNlKGluZGV4T2ZFeGlzdGluZ1RvYXN0ICsgMSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b2FzdCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi50b2FzdHNcbiAgICAgICAgICAgICAgICAgICAgICAgIF07XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIH0sIFtdKTtcbiAgICByZXR1cm4ge1xuICAgICAgICB0b2FzdHM6IGFjdGl2ZVRvYXN0c1xuICAgIH07XG59XG5jb25zdCBUb2FzdGVyID0gLyojX19QVVJFX18qLyBSZWFjdC5mb3J3YXJkUmVmKGZ1bmN0aW9uIFRvYXN0ZXIocHJvcHMsIHJlZikge1xuICAgIGNvbnN0IHsgaW52ZXJ0LCBwb3NpdGlvbiA9ICdib3R0b20tcmlnaHQnLCBob3RrZXkgPSBbXG4gICAgICAgICdhbHRLZXknLFxuICAgICAgICAnS2V5VCdcbiAgICBdLCBleHBhbmQsIGNsb3NlQnV0dG9uLCBjbGFzc05hbWUsIG9mZnNldCwgbW9iaWxlT2Zmc2V0LCB0aGVtZSA9ICdsaWdodCcsIHJpY2hDb2xvcnMsIGR1cmF0aW9uLCBzdHlsZSwgdmlzaWJsZVRvYXN0cyA9IFZJU0lCTEVfVE9BU1RTX0FNT1VOVCwgdG9hc3RPcHRpb25zLCBkaXIgPSBnZXREb2N1bWVudERpcmVjdGlvbigpLCBnYXAgPSBHQVAsIGljb25zLCBjb250YWluZXJBcmlhTGFiZWwgPSAnTm90aWZpY2F0aW9ucycgfSA9IHByb3BzO1xuICAgIGNvbnN0IFt0b2FzdHMsIHNldFRvYXN0c10gPSBSZWFjdC51c2VTdGF0ZShbXSk7XG4gICAgY29uc3QgcG9zc2libGVQb3NpdGlvbnMgPSBSZWFjdC51c2VNZW1vKCgpPT57XG4gICAgICAgIHJldHVybiBBcnJheS5mcm9tKG5ldyBTZXQoW1xuICAgICAgICAgICAgcG9zaXRpb25cbiAgICAgICAgXS5jb25jYXQodG9hc3RzLmZpbHRlcigodG9hc3QpPT50b2FzdC5wb3NpdGlvbikubWFwKCh0b2FzdCk9PnRvYXN0LnBvc2l0aW9uKSkpKTtcbiAgICB9LCBbXG4gICAgICAgIHRvYXN0cyxcbiAgICAgICAgcG9zaXRpb25cbiAgICBdKTtcbiAgICBjb25zdCBbaGVpZ2h0cywgc2V0SGVpZ2h0c10gPSBSZWFjdC51c2VTdGF0ZShbXSk7XG4gICAgY29uc3QgW2V4cGFuZGVkLCBzZXRFeHBhbmRlZF0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW2ludGVyYWN0aW5nLCBzZXRJbnRlcmFjdGluZ10gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW2FjdHVhbFRoZW1lLCBzZXRBY3R1YWxUaGVtZV0gPSBSZWFjdC51c2VTdGF0ZSh0aGVtZSAhPT0gJ3N5c3RlbScgPyB0aGVtZSA6IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gd2luZG93Lm1hdGNoTWVkaWEgJiYgd2luZG93Lm1hdGNoTWVkaWEoJyhwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyayknKS5tYXRjaGVzID8gJ2RhcmsnIDogJ2xpZ2h0JyA6ICdsaWdodCcpO1xuICAgIGNvbnN0IGxpc3RSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gICAgY29uc3QgaG90a2V5TGFiZWwgPSBob3RrZXkuam9pbignKycpLnJlcGxhY2UoL0tleS9nLCAnJykucmVwbGFjZSgvRGlnaXQvZywgJycpO1xuICAgIGNvbnN0IGxhc3RGb2N1c2VkRWxlbWVudFJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgICBjb25zdCBpc0ZvY3VzV2l0aGluUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcbiAgICBjb25zdCByZW1vdmVUb2FzdCA9IFJlYWN0LnVzZUNhbGxiYWNrKCh0b2FzdFRvUmVtb3ZlKT0+e1xuICAgICAgICBzZXRUb2FzdHMoKHRvYXN0cyk9PntcbiAgICAgICAgICAgIHZhciBfdG9hc3RzX2ZpbmQ7XG4gICAgICAgICAgICBpZiAoISgoX3RvYXN0c19maW5kID0gdG9hc3RzLmZpbmQoKHRvYXN0KT0+dG9hc3QuaWQgPT09IHRvYXN0VG9SZW1vdmUuaWQpKSA9PSBudWxsID8gdm9pZCAwIDogX3RvYXN0c19maW5kLmRlbGV0ZSkpIHtcbiAgICAgICAgICAgICAgICBUb2FzdFN0YXRlLmRpc21pc3ModG9hc3RUb1JlbW92ZS5pZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdG9hc3RzLmZpbHRlcigoeyBpZCB9KT0+aWQgIT09IHRvYXN0VG9SZW1vdmUuaWQpO1xuICAgICAgICB9KTtcbiAgICB9LCBbXSk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpPT57XG4gICAgICAgIHJldHVybiBUb2FzdFN0YXRlLnN1YnNjcmliZSgodG9hc3QpPT57XG4gICAgICAgICAgICBpZiAodG9hc3QuZGlzbWlzcykge1xuICAgICAgICAgICAgICAgIC8vIFByZXZlbnQgYmF0Y2hpbmcgb2Ygb3RoZXIgc3RhdGUgdXBkYXRlc1xuICAgICAgICAgICAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZSgoKT0+e1xuICAgICAgICAgICAgICAgICAgICBzZXRUb2FzdHMoKHRvYXN0cyk9PnRvYXN0cy5tYXAoKHQpPT50LmlkID09PSB0b2FzdC5pZCA/IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSA6IHQpKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBQcmV2ZW50IGJhdGNoaW5nLCB0ZW1wIHNvbHV0aW9uLlxuICAgICAgICAgICAgc2V0VGltZW91dCgoKT0+e1xuICAgICAgICAgICAgICAgIFJlYWN0RE9NLmZsdXNoU3luYygoKT0+e1xuICAgICAgICAgICAgICAgICAgICBzZXRUb2FzdHMoKHRvYXN0cyk9PntcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4T2ZFeGlzdGluZ1RvYXN0ID0gdG9hc3RzLmZpbmRJbmRleCgodCk9PnQuaWQgPT09IHRvYXN0LmlkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSB0aGUgdG9hc3QgaWYgaXQgYWxyZWFkeSBleGlzdHNcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbmRleE9mRXhpc3RpbmdUb2FzdCAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi50b2FzdHMuc2xpY2UoMCwgaW5kZXhPZkV4aXN0aW5nVG9hc3QpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi50b2FzdHNbaW5kZXhPZkV4aXN0aW5nVG9hc3RdLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udG9hc3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udG9hc3RzLnNsaWNlKGluZGV4T2ZFeGlzdGluZ1RvYXN0ICsgMSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b2FzdCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi50b2FzdHNcbiAgICAgICAgICAgICAgICAgICAgICAgIF07XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIH0sIFtcbiAgICAgICAgdG9hc3RzXG4gICAgXSk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpPT57XG4gICAgICAgIGlmICh0aGVtZSAhPT0gJ3N5c3RlbScpIHtcbiAgICAgICAgICAgIHNldEFjdHVhbFRoZW1lKHRoZW1lKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhlbWUgPT09ICdzeXN0ZW0nKSB7XG4gICAgICAgICAgICAvLyBjaGVjayBpZiBjdXJyZW50IHByZWZlcmVuY2UgaXMgZGFya1xuICAgICAgICAgICAgaWYgKHdpbmRvdy5tYXRjaE1lZGlhICYmIHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlcykge1xuICAgICAgICAgICAgICAgIC8vIGl0J3MgY3VycmVudGx5IGRhcmtcbiAgICAgICAgICAgICAgICBzZXRBY3R1YWxUaGVtZSgnZGFyaycpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyBpdCdzIG5vdCBkYXJrXG4gICAgICAgICAgICAgICAgc2V0QWN0dWFsVGhlbWUoJ2xpZ2h0Jyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm47XG4gICAgICAgIGNvbnN0IGRhcmtNZWRpYVF1ZXJ5ID0gd2luZG93Lm1hdGNoTWVkaWEoJyhwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyayknKTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIENocm9tZSAmIEZpcmVmb3hcbiAgICAgICAgICAgIGRhcmtNZWRpYVF1ZXJ5LmFkZEV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsICh7IG1hdGNoZXMgfSk9PntcbiAgICAgICAgICAgICAgICBpZiAobWF0Y2hlcykge1xuICAgICAgICAgICAgICAgICAgICBzZXRBY3R1YWxUaGVtZSgnZGFyaycpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHNldEFjdHVhbFRoZW1lKCdsaWdodCcpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgLy8gU2FmYXJpIDwgMTRcbiAgICAgICAgICAgIGRhcmtNZWRpYVF1ZXJ5LmFkZExpc3RlbmVyKCh7IG1hdGNoZXMgfSk9PntcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBpZiAobWF0Y2hlcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgc2V0QWN0dWFsVGhlbWUoJ2RhcmsnKTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEFjdHVhbFRoZW1lKCdsaWdodCcpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfSwgW1xuICAgICAgICB0aGVtZVxuICAgIF0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKT0+e1xuICAgICAgICAvLyBFbnN1cmUgZXhwYW5kZWQgaXMgYWx3YXlzIGZhbHNlIHdoZW4gbm8gdG9hc3RzIGFyZSBwcmVzZW50IC8gb25seSBvbmUgbGVmdFxuICAgICAgICBpZiAodG9hc3RzLmxlbmd0aCA8PSAxKSB7XG4gICAgICAgICAgICBzZXRFeHBhbmRlZChmYWxzZSk7XG4gICAgICAgIH1cbiAgICB9LCBbXG4gICAgICAgIHRvYXN0c1xuICAgIF0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKT0+e1xuICAgICAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50KT0+e1xuICAgICAgICAgICAgdmFyIF9saXN0UmVmX2N1cnJlbnQ7XG4gICAgICAgICAgICBjb25zdCBpc0hvdGtleVByZXNzZWQgPSBob3RrZXkuZXZlcnkoKGtleSk9PmV2ZW50W2tleV0gfHwgZXZlbnQuY29kZSA9PT0ga2V5KTtcbiAgICAgICAgICAgIGlmIChpc0hvdGtleVByZXNzZWQpIHtcbiAgICAgICAgICAgICAgICB2YXIgX2xpc3RSZWZfY3VycmVudDE7XG4gICAgICAgICAgICAgICAgc2V0RXhwYW5kZWQodHJ1ZSk7XG4gICAgICAgICAgICAgICAgKF9saXN0UmVmX2N1cnJlbnQxID0gbGlzdFJlZi5jdXJyZW50KSA9PSBudWxsID8gdm9pZCAwIDogX2xpc3RSZWZfY3VycmVudDEuZm9jdXMoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChldmVudC5jb2RlID09PSAnRXNjYXBlJyAmJiAoZG9jdW1lbnQuYWN0aXZlRWxlbWVudCA9PT0gbGlzdFJlZi5jdXJyZW50IHx8ICgoX2xpc3RSZWZfY3VycmVudCA9IGxpc3RSZWYuY3VycmVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9saXN0UmVmX2N1cnJlbnQuY29udGFpbnMoZG9jdW1lbnQuYWN0aXZlRWxlbWVudCkpKSkge1xuICAgICAgICAgICAgICAgIHNldEV4cGFuZGVkKGZhbHNlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pO1xuICAgICAgICByZXR1cm4gKCk9PmRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgICB9LCBbXG4gICAgICAgIGhvdGtleVxuICAgIF0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKT0+e1xuICAgICAgICBpZiAobGlzdFJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgICAgICBpZiAobGFzdEZvY3VzZWRFbGVtZW50UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgbGFzdEZvY3VzZWRFbGVtZW50UmVmLmN1cnJlbnQuZm9jdXMoe1xuICAgICAgICAgICAgICAgICAgICAgICAgcHJldmVudFNjcm9sbDogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgbGFzdEZvY3VzZWRFbGVtZW50UmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgICAgICAgICAgICAgICBpc0ZvY3VzV2l0aGluUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSwgW1xuICAgICAgICBsaXN0UmVmLmN1cnJlbnRcbiAgICBdKTtcbiAgICByZXR1cm4oLy8gUmVtb3ZlIGl0ZW0gZnJvbSBub3JtYWwgbmF2aWdhdGlvbiBmbG93LCBvbmx5IGF2YWlsYWJsZSB2aWEgaG90a2V5XG4gICAgLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwic2VjdGlvblwiLCB7XG4gICAgICAgIHJlZjogcmVmLFxuICAgICAgICBcImFyaWEtbGFiZWxcIjogYCR7Y29udGFpbmVyQXJpYUxhYmVsfSAke2hvdGtleUxhYmVsfWAsXG4gICAgICAgIHRhYkluZGV4OiAtMSxcbiAgICAgICAgXCJhcmlhLWxpdmVcIjogXCJwb2xpdGVcIixcbiAgICAgICAgXCJhcmlhLXJlbGV2YW50XCI6IFwiYWRkaXRpb25zIHRleHRcIixcbiAgICAgICAgXCJhcmlhLWF0b21pY1wiOiBcImZhbHNlXCIsXG4gICAgICAgIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZzogdHJ1ZVxuICAgIH0sIHBvc3NpYmxlUG9zaXRpb25zLm1hcCgocG9zaXRpb24sIGluZGV4KT0+e1xuICAgICAgICB2YXIgX2hlaWdodHNfO1xuICAgICAgICBjb25zdCBbeSwgeF0gPSBwb3NpdGlvbi5zcGxpdCgnLScpO1xuICAgICAgICBpZiAoIXRvYXN0cy5sZW5ndGgpIHJldHVybiBudWxsO1xuICAgICAgICByZXR1cm4gLyojX19QVVJFX18qLyBSZWFjdC5jcmVhdGVFbGVtZW50KFwib2xcIiwge1xuICAgICAgICAgICAga2V5OiBwb3NpdGlvbixcbiAgICAgICAgICAgIGRpcjogZGlyID09PSAnYXV0bycgPyBnZXREb2N1bWVudERpcmVjdGlvbigpIDogZGlyLFxuICAgICAgICAgICAgdGFiSW5kZXg6IC0xLFxuICAgICAgICAgICAgcmVmOiBsaXN0UmVmLFxuICAgICAgICAgICAgY2xhc3NOYW1lOiBjbGFzc05hbWUsXG4gICAgICAgICAgICBcImRhdGEtc29ubmVyLXRvYXN0ZXJcIjogdHJ1ZSxcbiAgICAgICAgICAgIFwiZGF0YS1zb25uZXItdGhlbWVcIjogYWN0dWFsVGhlbWUsXG4gICAgICAgICAgICBcImRhdGEteS1wb3NpdGlvblwiOiB5LFxuICAgICAgICAgICAgXCJkYXRhLWxpZnRlZFwiOiBleHBhbmRlZCAmJiB0b2FzdHMubGVuZ3RoID4gMSAmJiAhZXhwYW5kLFxuICAgICAgICAgICAgXCJkYXRhLXgtcG9zaXRpb25cIjogeCxcbiAgICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICAgICAgJy0tZnJvbnQtdG9hc3QtaGVpZ2h0JzogYCR7KChfaGVpZ2h0c18gPSBoZWlnaHRzWzBdKSA9PSBudWxsID8gdm9pZCAwIDogX2hlaWdodHNfLmhlaWdodCkgfHwgMH1weGAsXG4gICAgICAgICAgICAgICAgJy0td2lkdGgnOiBgJHtUT0FTVF9XSURUSH1weGAsXG4gICAgICAgICAgICAgICAgJy0tZ2FwJzogYCR7Z2FwfXB4YCxcbiAgICAgICAgICAgICAgICAuLi5zdHlsZSxcbiAgICAgICAgICAgICAgICAuLi5hc3NpZ25PZmZzZXQob2Zmc2V0LCBtb2JpbGVPZmZzZXQpXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgb25CbHVyOiAoZXZlbnQpPT57XG4gICAgICAgICAgICAgICAgaWYgKGlzRm9jdXNXaXRoaW5SZWYuY3VycmVudCAmJiAhZXZlbnQuY3VycmVudFRhcmdldC5jb250YWlucyhldmVudC5yZWxhdGVkVGFyZ2V0KSkge1xuICAgICAgICAgICAgICAgICAgICBpc0ZvY3VzV2l0aGluUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGxhc3RGb2N1c2VkRWxlbWVudFJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsYXN0Rm9jdXNlZEVsZW1lbnRSZWYuY3VycmVudC5mb2N1cyh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJldmVudFNjcm9sbDogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBsYXN0Rm9jdXNlZEVsZW1lbnRSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgb25Gb2N1czogKGV2ZW50KT0+e1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzTm90RGlzbWlzc2libGUgPSBldmVudC50YXJnZXQgaW5zdGFuY2VvZiBIVE1MRWxlbWVudCAmJiBldmVudC50YXJnZXQuZGF0YXNldC5kaXNtaXNzaWJsZSA9PT0gJ2ZhbHNlJztcbiAgICAgICAgICAgICAgICBpZiAoaXNOb3REaXNtaXNzaWJsZSkgcmV0dXJuO1xuICAgICAgICAgICAgICAgIGlmICghaXNGb2N1c1dpdGhpblJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgICAgICAgIGlzRm9jdXNXaXRoaW5SZWYuY3VycmVudCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIGxhc3RGb2N1c2VkRWxlbWVudFJlZi5jdXJyZW50ID0gZXZlbnQucmVsYXRlZFRhcmdldDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgb25Nb3VzZUVudGVyOiAoKT0+c2V0RXhwYW5kZWQodHJ1ZSksXG4gICAgICAgICAgICBvbk1vdXNlTW92ZTogKCk9PnNldEV4cGFuZGVkKHRydWUpLFxuICAgICAgICAgICAgb25Nb3VzZUxlYXZlOiAoKT0+e1xuICAgICAgICAgICAgICAgIC8vIEF2b2lkIHNldHRpbmcgZXhwYW5kZWQgdG8gZmFsc2Ugd2hlbiBpbnRlcmFjdGluZyB3aXRoIGEgdG9hc3QsIGUuZy4gc3dpcGluZ1xuICAgICAgICAgICAgICAgIGlmICghaW50ZXJhY3RpbmcpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0RXhwYW5kZWQoZmFsc2UpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBvbkRyYWdFbmQ6ICgpPT5zZXRFeHBhbmRlZChmYWxzZSksXG4gICAgICAgICAgICBvblBvaW50ZXJEb3duOiAoZXZlbnQpPT57XG4gICAgICAgICAgICAgICAgY29uc3QgaXNOb3REaXNtaXNzaWJsZSA9IGV2ZW50LnRhcmdldCBpbnN0YW5jZW9mIEhUTUxFbGVtZW50ICYmIGV2ZW50LnRhcmdldC5kYXRhc2V0LmRpc21pc3NpYmxlID09PSAnZmFsc2UnO1xuICAgICAgICAgICAgICAgIGlmIChpc05vdERpc21pc3NpYmxlKSByZXR1cm47XG4gICAgICAgICAgICAgICAgc2V0SW50ZXJhY3RpbmcodHJ1ZSk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgb25Qb2ludGVyVXA6ICgpPT5zZXRJbnRlcmFjdGluZyhmYWxzZSlcbiAgICAgICAgfSwgdG9hc3RzLmZpbHRlcigodG9hc3QpPT4hdG9hc3QucG9zaXRpb24gJiYgaW5kZXggPT09IDAgfHwgdG9hc3QucG9zaXRpb24gPT09IHBvc2l0aW9uKS5tYXAoKHRvYXN0LCBpbmRleCk9PntcbiAgICAgICAgICAgIHZhciBfdG9hc3RPcHRpb25zX2R1cmF0aW9uLCBfdG9hc3RPcHRpb25zX2Nsb3NlQnV0dG9uO1xuICAgICAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gUmVhY3QuY3JlYXRlRWxlbWVudChUb2FzdCwge1xuICAgICAgICAgICAgICAgIGtleTogdG9hc3QuaWQsXG4gICAgICAgICAgICAgICAgaWNvbnM6IGljb25zLFxuICAgICAgICAgICAgICAgIGluZGV4OiBpbmRleCxcbiAgICAgICAgICAgICAgICB0b2FzdDogdG9hc3QsXG4gICAgICAgICAgICAgICAgZGVmYXVsdFJpY2hDb2xvcnM6IHJpY2hDb2xvcnMsXG4gICAgICAgICAgICAgICAgZHVyYXRpb246IChfdG9hc3RPcHRpb25zX2R1cmF0aW9uID0gdG9hc3RPcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiB0b2FzdE9wdGlvbnMuZHVyYXRpb24pICE9IG51bGwgPyBfdG9hc3RPcHRpb25zX2R1cmF0aW9uIDogZHVyYXRpb24sXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lOiB0b2FzdE9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IHRvYXN0T3B0aW9ucy5jbGFzc05hbWUsXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb25DbGFzc05hbWU6IHRvYXN0T3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogdG9hc3RPcHRpb25zLmRlc2NyaXB0aW9uQ2xhc3NOYW1lLFxuICAgICAgICAgICAgICAgIGludmVydDogaW52ZXJ0LFxuICAgICAgICAgICAgICAgIHZpc2libGVUb2FzdHM6IHZpc2libGVUb2FzdHMsXG4gICAgICAgICAgICAgICAgY2xvc2VCdXR0b246IChfdG9hc3RPcHRpb25zX2Nsb3NlQnV0dG9uID0gdG9hc3RPcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiB0b2FzdE9wdGlvbnMuY2xvc2VCdXR0b24pICE9IG51bGwgPyBfdG9hc3RPcHRpb25zX2Nsb3NlQnV0dG9uIDogY2xvc2VCdXR0b24sXG4gICAgICAgICAgICAgICAgaW50ZXJhY3Rpbmc6IGludGVyYWN0aW5nLFxuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBwb3NpdGlvbixcbiAgICAgICAgICAgICAgICBzdHlsZTogdG9hc3RPcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiB0b2FzdE9wdGlvbnMuc3R5bGUsXG4gICAgICAgICAgICAgICAgdW5zdHlsZWQ6IHRvYXN0T3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogdG9hc3RPcHRpb25zLnVuc3R5bGVkLFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZXM6IHRvYXN0T3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogdG9hc3RPcHRpb25zLmNsYXNzTmFtZXMsXG4gICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uU3R5bGU6IHRvYXN0T3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogdG9hc3RPcHRpb25zLmNhbmNlbEJ1dHRvblN0eWxlLFxuICAgICAgICAgICAgICAgIGFjdGlvbkJ1dHRvblN0eWxlOiB0b2FzdE9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IHRvYXN0T3B0aW9ucy5hY3Rpb25CdXR0b25TdHlsZSxcbiAgICAgICAgICAgICAgICBjbG9zZUJ1dHRvbkFyaWFMYWJlbDogdG9hc3RPcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiB0b2FzdE9wdGlvbnMuY2xvc2VCdXR0b25BcmlhTGFiZWwsXG4gICAgICAgICAgICAgICAgcmVtb3ZlVG9hc3Q6IHJlbW92ZVRvYXN0LFxuICAgICAgICAgICAgICAgIHRvYXN0czogdG9hc3RzLmZpbHRlcigodCk9PnQucG9zaXRpb24gPT0gdG9hc3QucG9zaXRpb24pLFxuICAgICAgICAgICAgICAgIGhlaWdodHM6IGhlaWdodHMuZmlsdGVyKChoKT0+aC5wb3NpdGlvbiA9PSB0b2FzdC5wb3NpdGlvbiksXG4gICAgICAgICAgICAgICAgc2V0SGVpZ2h0czogc2V0SGVpZ2h0cyxcbiAgICAgICAgICAgICAgICBleHBhbmRCeURlZmF1bHQ6IGV4cGFuZCxcbiAgICAgICAgICAgICAgICBnYXA6IGdhcCxcbiAgICAgICAgICAgICAgICBleHBhbmRlZDogZXhwYW5kZWQsXG4gICAgICAgICAgICAgICAgc3dpcGVEaXJlY3Rpb25zOiBwcm9wcy5zd2lwZURpcmVjdGlvbnNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9KSk7XG4gICAgfSkpKTtcbn0pO1xuXG5leHBvcnQgeyBUb2FzdGVyLCB0b2FzdCwgdXNlU29ubmVyIH07XG4iXSwibmFtZXMiOlsiX19pbnNlcnRDU1MiLCJjb2RlIiwiZG9jdW1lbnQiLCJoZWFkIiwiZ2V0RWxlbWVudHNCeVRhZ05hbWUiLCJzdHlsZSIsImNyZWF0ZUVsZW1lbnQiLCJ0eXBlIiwiYXBwZW5kQ2hpbGQiLCJzdHlsZVNoZWV0IiwiY3NzVGV4dCIsImNyZWF0ZVRleHROb2RlIiwiUmVhY3QiLCJSZWFjdERPTSIsImdldEFzc2V0IiwiU3VjY2Vzc0ljb24iLCJJbmZvSWNvbiIsIldhcm5pbmdJY29uIiwiRXJyb3JJY29uIiwiYmFycyIsIkFycmF5IiwiZmlsbCIsIkxvYWRlciIsInZpc2libGUiLCJjbGFzc05hbWUiLCJmaWx0ZXIiLCJCb29sZWFuIiwiam9pbiIsIm1hcCIsIl8iLCJpIiwia2V5IiwieG1sbnMiLCJ2aWV3Qm94IiwiaGVpZ2h0Iiwid2lkdGgiLCJmaWxsUnVsZSIsImQiLCJjbGlwUnVsZSIsIkNsb3NlSWNvbiIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwieDEiLCJ5MSIsIngyIiwieTIiLCJ1c2VJc0RvY3VtZW50SGlkZGVuIiwiaXNEb2N1bWVudEhpZGRlbiIsInNldElzRG9jdW1lbnRIaWRkZW4iLCJ1c2VTdGF0ZSIsImhpZGRlbiIsInVzZUVmZmVjdCIsImNhbGxiYWNrIiwiYWRkRXZlbnRMaXN0ZW5lciIsIndpbmRvdyIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJ0b2FzdHNDb3VudGVyIiwiT2JzZXJ2ZXIiLCJjb25zdHJ1Y3RvciIsInN1YnNjcmliZSIsInN1YnNjcmliZXIiLCJzdWJzY3JpYmVycyIsInB1c2giLCJpbmRleCIsImluZGV4T2YiLCJzcGxpY2UiLCJwdWJsaXNoIiwiZGF0YSIsImZvckVhY2giLCJhZGRUb2FzdCIsInRvYXN0cyIsImNyZWF0ZSIsIl9kYXRhX2lkIiwibWVzc2FnZSIsInJlc3QiLCJpZCIsImxlbmd0aCIsImFscmVhZHlFeGlzdHMiLCJmaW5kIiwidG9hc3QiLCJkaXNtaXNzaWJsZSIsInVuZGVmaW5lZCIsImRpc21pc3NlZFRvYXN0cyIsImhhcyIsImRlbGV0ZSIsInRpdGxlIiwiZGlzbWlzcyIsImFkZCIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsImVycm9yIiwic3VjY2VzcyIsImluZm8iLCJ3YXJuaW5nIiwibG9hZGluZyIsInByb21pc2UiLCJkZXNjcmlwdGlvbiIsInAiLCJQcm9taXNlIiwicmVzb2x2ZSIsIkZ1bmN0aW9uIiwic2hvdWxkRGlzbWlzcyIsInJlc3VsdCIsIm9yaWdpbmFsUHJvbWlzZSIsInRoZW4iLCJyZXNwb25zZSIsImlzUmVhY3RFbGVtZW50UmVzcG9uc2UiLCJpc1ZhbGlkRWxlbWVudCIsImlzSHR0cFJlc3BvbnNlIiwib2siLCJwcm9taXNlRGF0YSIsInN0YXR1cyIsImlzRXh0ZW5kZWRSZXN1bHQiLCJ0b2FzdFNldHRpbmdzIiwiRXJyb3IiLCJjYXRjaCIsImZpbmFsbHkiLCJjYWxsIiwidW53cmFwIiwicmVqZWN0IiwiT2JqZWN0IiwiYXNzaWduIiwiY3VzdG9tIiwianN4IiwiZ2V0QWN0aXZlVG9hc3RzIiwiU2V0IiwiVG9hc3RTdGF0ZSIsInRvYXN0RnVuY3Rpb24iLCJiYXNpY1RvYXN0IiwiZ2V0SGlzdG9yeSIsImdldFRvYXN0cyIsImlzQWN0aW9uIiwiYWN0aW9uIiwibGFiZWwiLCJWSVNJQkxFX1RPQVNUU19BTU9VTlQiLCJWSUVXUE9SVF9PRkZTRVQiLCJNT0JJTEVfVklFV1BPUlRfT0ZGU0VUIiwiVE9BU1RfTElGRVRJTUUiLCJUT0FTVF9XSURUSCIsIkdBUCIsIlNXSVBFX1RIUkVTSE9MRCIsIlRJTUVfQkVGT1JFX1VOTU9VTlQiLCJjbiIsImNsYXNzZXMiLCJnZXREZWZhdWx0U3dpcGVEaXJlY3Rpb25zIiwicG9zaXRpb24iLCJ5IiwieCIsInNwbGl0IiwiZGlyZWN0aW9ucyIsIlRvYXN0IiwicHJvcHMiLCJfdG9hc3RfY2xhc3NOYW1lcyIsIl90b2FzdF9jbGFzc05hbWVzMSIsIl90b2FzdF9jbGFzc05hbWVzMiIsIl90b2FzdF9jbGFzc05hbWVzMyIsIl90b2FzdF9jbGFzc05hbWVzNCIsIl90b2FzdF9jbGFzc05hbWVzNSIsIl90b2FzdF9jbGFzc05hbWVzNiIsIl90b2FzdF9jbGFzc05hbWVzNyIsIl90b2FzdF9jbGFzc05hbWVzOCIsImludmVydCIsIlRvYXN0ZXJJbnZlcnQiLCJ1bnN0eWxlZCIsImludGVyYWN0aW5nIiwic2V0SGVpZ2h0cyIsInZpc2libGVUb2FzdHMiLCJoZWlnaHRzIiwiZXhwYW5kZWQiLCJyZW1vdmVUb2FzdCIsImRlZmF1bHRSaWNoQ29sb3JzIiwiY2xvc2VCdXR0b24iLCJjbG9zZUJ1dHRvbkZyb21Ub2FzdGVyIiwiY2FuY2VsQnV0dG9uU3R5bGUiLCJhY3Rpb25CdXR0b25TdHlsZSIsImRlc2NyaXB0aW9uQ2xhc3NOYW1lIiwiZHVyYXRpb24iLCJkdXJhdGlvbkZyb21Ub2FzdGVyIiwiZ2FwIiwiZXhwYW5kQnlEZWZhdWx0IiwiY2xhc3NOYW1lcyIsImljb25zIiwiY2xvc2VCdXR0b25BcmlhTGFiZWwiLCJzd2lwZURpcmVjdGlvbiIsInNldFN3aXBlRGlyZWN0aW9uIiwic3dpcGVPdXREaXJlY3Rpb24iLCJzZXRTd2lwZU91dERpcmVjdGlvbiIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwicmVtb3ZlZCIsInNldFJlbW92ZWQiLCJzd2lwaW5nIiwic2V0U3dpcGluZyIsInN3aXBlT3V0Iiwic2V0U3dpcGVPdXQiLCJpc1N3aXBlZCIsInNldElzU3dpcGVkIiwib2Zmc2V0QmVmb3JlUmVtb3ZlIiwic2V0T2Zmc2V0QmVmb3JlUmVtb3ZlIiwiaW5pdGlhbEhlaWdodCIsInNldEluaXRpYWxIZWlnaHQiLCJyZW1haW5pbmdUaW1lIiwidXNlUmVmIiwiZHJhZ1N0YXJ0VGltZSIsInRvYXN0UmVmIiwiaXNGcm9udCIsImlzVmlzaWJsZSIsInRvYXN0VHlwZSIsInRvYXN0Q2xhc3NuYW1lIiwidG9hc3REZXNjcmlwdGlvbkNsYXNzbmFtZSIsImhlaWdodEluZGV4IiwidXNlTWVtbyIsImZpbmRJbmRleCIsInRvYXN0SWQiLCJfdG9hc3RfY2xvc2VCdXR0b24iLCJjbG9zZVRpbWVyU3RhcnRUaW1lUmVmIiwib2Zmc2V0IiwibGFzdENsb3NlVGltZXJTdGFydFRpbWVSZWYiLCJwb2ludGVyU3RhcnRSZWYiLCJ0b2FzdHNIZWlnaHRCZWZvcmUiLCJyZWR1Y2UiLCJwcmV2IiwiY3VyciIsInJlZHVjZXJJbmRleCIsImRpc2FibGVkIiwiY3VycmVudCIsInRvYXN0Tm9kZSIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsImgiLCJ1c2VMYXlvdXRFZmZlY3QiLCJvcmlnaW5hbEhlaWdodCIsIm5ld0hlaWdodCIsImRlbGV0ZVRvYXN0IiwidXNlQ2FsbGJhY2siLCJzZXRUaW1lb3V0IiwiSW5maW5pdHkiLCJ0aW1lb3V0SWQiLCJwYXVzZVRpbWVyIiwiZWxhcHNlZFRpbWUiLCJEYXRlIiwiZ2V0VGltZSIsInN0YXJ0VGltZXIiLCJvbkF1dG9DbG9zZSIsImNsZWFyVGltZW91dCIsImdldExvYWRpbmdJY29uIiwibG9hZGVyIiwiaWNvbiIsIl90b2FzdF9yaWNoQ29sb3JzIiwiX2ljb25zX2Nsb3NlIiwidGFiSW5kZXgiLCJyZWYiLCJkZWZhdWx0IiwicmljaENvbG9ycyIsIm9uRHJhZ0VuZCIsIm9uUG9pbnRlckRvd24iLCJldmVudCIsInRhcmdldCIsInNldFBvaW50ZXJDYXB0dXJlIiwicG9pbnRlcklkIiwidGFnTmFtZSIsImNsaWVudFgiLCJjbGllbnRZIiwib25Qb2ludGVyVXAiLCJfdG9hc3RSZWZfY3VycmVudCIsIl90b2FzdFJlZl9jdXJyZW50MSIsIl9kcmFnU3RhcnRUaW1lX2N1cnJlbnQiLCJzd2lwZUFtb3VudFgiLCJOdW1iZXIiLCJnZXRQcm9wZXJ0eVZhbHVlIiwicmVwbGFjZSIsInN3aXBlQW1vdW50WSIsInRpbWVUYWtlbiIsInN3aXBlQW1vdW50IiwidmVsb2NpdHkiLCJNYXRoIiwiYWJzIiwib25EaXNtaXNzIiwiX3RvYXN0UmVmX2N1cnJlbnQyIiwiX3RvYXN0UmVmX2N1cnJlbnQzIiwic2V0UHJvcGVydHkiLCJvblBvaW50ZXJNb3ZlIiwiX3dpbmRvd19nZXRTZWxlY3Rpb24iLCJpc0hpZ2hsaWdodGVkIiwiZ2V0U2VsZWN0aW9uIiwidG9TdHJpbmciLCJ5RGVsdGEiLCJ4RGVsdGEiLCJfcHJvcHNfc3dpcGVEaXJlY3Rpb25zIiwic3dpcGVEaXJlY3Rpb25zIiwiZ2V0RGFtcGVuaW5nIiwiZGVsdGEiLCJmYWN0b3IiLCJpbmNsdWRlcyIsImRhbXBlbmVkRGVsdGEiLCJvbkNsaWNrIiwiY2xvc2UiLCJjb250ZW50IiwiY2FuY2VsIiwiY2FuY2VsQnV0dG9uIiwiZGVmYXVsdFByZXZlbnRlZCIsImFjdGlvbkJ1dHRvbiIsImdldERvY3VtZW50RGlyZWN0aW9uIiwiZGlyQXR0cmlidXRlIiwiZG9jdW1lbnRFbGVtZW50IiwiZ2V0QXR0cmlidXRlIiwiZ2V0Q29tcHV0ZWRTdHlsZSIsImRpcmVjdGlvbiIsImFzc2lnbk9mZnNldCIsImRlZmF1bHRPZmZzZXQiLCJtb2JpbGVPZmZzZXQiLCJzdHlsZXMiLCJpc01vYmlsZSIsInByZWZpeCIsImRlZmF1bHRWYWx1ZSIsImFzc2lnbkFsbCIsInVzZVNvbm5lciIsImFjdGl2ZVRvYXN0cyIsInNldEFjdGl2ZVRvYXN0cyIsImZsdXNoU3luYyIsInQiLCJpbmRleE9mRXhpc3RpbmdUb2FzdCIsInNsaWNlIiwiVG9hc3RlciIsImZvcndhcmRSZWYiLCJob3RrZXkiLCJleHBhbmQiLCJ0aGVtZSIsInRvYXN0T3B0aW9ucyIsImRpciIsImNvbnRhaW5lckFyaWFMYWJlbCIsInNldFRvYXN0cyIsInBvc3NpYmxlUG9zaXRpb25zIiwiZnJvbSIsImNvbmNhdCIsInNldEV4cGFuZGVkIiwic2V0SW50ZXJhY3RpbmciLCJhY3R1YWxUaGVtZSIsInNldEFjdHVhbFRoZW1lIiwibWF0Y2hNZWRpYSIsIm1hdGNoZXMiLCJsaXN0UmVmIiwiaG90a2V5TGFiZWwiLCJsYXN0Rm9jdXNlZEVsZW1lbnRSZWYiLCJpc0ZvY3VzV2l0aGluUmVmIiwidG9hc3RUb1JlbW92ZSIsIl90b2FzdHNfZmluZCIsImRhcmtNZWRpYVF1ZXJ5IiwiYWRkTGlzdGVuZXIiLCJlIiwiY29uc29sZSIsImhhbmRsZUtleURvd24iLCJfbGlzdFJlZl9jdXJyZW50IiwiaXNIb3RrZXlQcmVzc2VkIiwiZXZlcnkiLCJfbGlzdFJlZl9jdXJyZW50MSIsImZvY3VzIiwiYWN0aXZlRWxlbWVudCIsImNvbnRhaW5zIiwicHJldmVudFNjcm9sbCIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsIl9oZWlnaHRzXyIsIm9uQmx1ciIsImN1cnJlbnRUYXJnZXQiLCJyZWxhdGVkVGFyZ2V0Iiwib25Gb2N1cyIsImlzTm90RGlzbWlzc2libGUiLCJIVE1MRWxlbWVudCIsImRhdGFzZXQiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTW92ZSIsIm9uTW91c2VMZWF2ZSIsIl90b2FzdE9wdGlvbnNfZHVyYXRpb24iLCJfdG9hc3RPcHRpb25zX2Nsb3NlQnV0dG9uIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;