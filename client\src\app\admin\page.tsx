"use client";

import {
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  StarOutlined,
  UserOutlined,
  TeamOutlined,
  TrophyOutlined,
  BarChartOutlined,
} from "@ant-design/icons";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Space,
  Spin,
  Table,
  Tag,
  Tooltip,
  Tabs,
  Card,
  Statistic,
  InputNumber,
  Row,
  Col,
  Select,
} from "antd";
import dayjs from "dayjs";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { QRCodeSVG } from "qrcode.react";
import { API_BASE_URL, apiEndpoints, fetchApi } from "@/config/api";
import {
  isAuthenticated,
  logoutAdmin,
  isAdmin,
  isModerator,
  getCurrentUser,
  fetchAllRatings,
  fetchMyRatings,
  fetchMyRatingForUser,
  createOrUpdateRating,
  createModerator,
  fetchModerators,
  deleteModerator,
  updateModerator,
  fetchAgeStatistics,
} from "@/services/api";

// Add missing type definitions
interface UserWithParentDTO {
  id: number;
  fullName: string;
  dateOfBirth: string;
  height: number;
  weight: number;
  phoneNumber: string;
  email?: string;
  province?: string;
  district?: string;
  armSpan?: number;
  shoeSize: number;
  palmSize?: number;
  jumpHeight?: number;
  jumpRecordDate?: Date | null;
  hasPlayedForClub: boolean;
  clubName?: string;
  hasHealthIssues: boolean;
  healthIssues?: string;
  trackingCode: string;
  status?: number | null;
  parent?: ParentDTO;
}

interface ParentDTO {
  id: number;
  fullName: string;
  mobilePhone: string;
  relationshipType: string;
  motherHeight?: number;
  fatherHeight?: number;
  approvalGiven: boolean;
}

interface ModeratorDTO {
  id: string;
  fullName: string;
  email: string;
  userName: string;
}

const formSchema = z
  .object({
    fullName: z
      .string()
      .min(2, { message: "Ad ve soyad en az 2 karakter olmalıdır." }),
    birthDate: z.date({ required_error: "Doğum tarihi gereklidir." }).refine(
      (date) => {
        const birthYear = date.getFullYear();
        return birthYear >= 2011 && birthYear <= 2016;
      },
      {
        message:
          "Başvuru yaş aralığı: Sadece 2011-2016 arası doğumlular başvuru yapabilir.",
      }
    ),
    height: z
      .string()
      .optional()
      .transform((val) => Number(val))
      .refine((val) => !isNaN(val), {
        message: "Geçerli bir sayı giriniz.",
      }),
    weight: z
      .string()
      .optional()
      .transform((val) => Number(val))
      .refine((val) => !isNaN(val), {
        message: "Geçerli bir sayı giriniz.",
      }),
    email: z.string().email({ message: "Geçerli bir e-posta adresi giriniz." }),
    phoneNumber: z
      .string()
      .min(10, { message: "Telefon numarası en az 10 karakter olmalıdır." }),
    province: z.string().optional(),
    district: z.string().optional(),
    armSpan: z
      .string()
      .optional()
      .transform((val) => Number(val))
      .refine((val) => !isNaN(val), {
        message: "Geçerli bir sayı giriniz.",
      }),
    shoeSize: z
      .string()
      .optional()
      .transform((val) => Number(val))
      .refine((val) => !isNaN(val), {
        message: "Geçerli bir sayı giriniz.",
      }),
    palmSize: z
      .string()
      .optional()
      .transform((val) => Number(val))
      .refine((val) => !isNaN(val), {
        message: "Geçerli bir sayı giriniz.",
      }),
    jumpHeight: z
      .string()
      .optional()
      .transform((val) =>
        val === undefined || val === "" ? undefined : Number(val)
      ),
    jumpRecordDate: z.date().optional(),

    hasPlayedForClub: z.boolean().default(false),
    clubName: z.string().optional(),
    hasHealthIssues: z.boolean().default(false),
    healthIssues: z.string().optional(),
    parentFullName: z
      .string()
      .min(2, { message: "Veli adı soyadı en az 2 karakter olmalıdır." }),
    parentPhone: z.string().min(10, {
      message: "Veli telefon numarası en az 10 karakter olmalıdır.",
    }),
    parentRelationshipType: z
      .string()
      .min(1, { message: "Veli yakınlık derecesi gereklidir." }),
    customRelationshipType: z.string().optional(),
    motherHeight: z
      .string()
      .optional()
      .transform((val) => Number(val))
      .refine((val) => !isNaN(val), {
        message: "Geçerli bir sayı giriniz.",
      }),
    fatherHeight: z
      .string()
      .optional()
      .transform((val) => Number(val))
      .refine((val) => !isNaN(val), {
        message: "Geçerli bir sayı giriniz.",
      }),
    approvalGiven: z.boolean().refine((value) => value === true, {
      message: "Veli onayı zorunludur.",
    }),
    status: z.number().optional().nullable(),
  })
  .refine(
    (data) => {
      if (data.hasPlayedForClub) {
        return data.clubName && data.clubName.trim().length > 0;
      }
      return true;
    },
    {
      message: "Kulüp adı girilmesi zorunludur.",
      path: ["clubName"],
    }
  )
  .refine(
    (data) => {
      if (data.hasHealthIssues) {
        return data.healthIssues && data.healthIssues.trim().length > 0;
      }
      return true;
    },
    {
      message: "Sağlık sorunu detayları girilmesi zorunludur.",
      path: ["healthIssues"],
    }
  )
  .refine(
    (data) => {
      if (data.parentRelationshipType === "Diğer") {
        return (
          !!data.customRelationshipType &&
          data.customRelationshipType.trim().length > 0
        );
      }
      return true;
    },
    {
      message: "Yakınlık derecesi belirtmeniz gereklidir.",
      path: ["customRelationshipType"],
    }
  );

type FormValues = z.infer<typeof formSchema>;

const Admin = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [authLoading, setAuthLoading] = useState(true);
  const [applications, setApplications] = useState<UserWithParentDTO[]>([]);
  const [filteredData, setFilteredData] = useState<UserWithParentDTO[]>([]);
  const [searchText, setSearchText] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserWithParentDTO | null>(
    null
  );
  const [currentParent, setCurrentParent] = useState<ParentDTO | null>(null);
  const [isViewMode, setIsViewMode] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isQRCodeModalVisible, setIsQRCodeModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();
  const [backendErrors, setBackendErrors] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<number | null>(null);
  const [ageFilter, setAgeFilter] = useState<number | null>(null);
  const [ageStatistics, setAgeStatistics] = useState<any>(null);

  // Add missing state variables
  const [userRole, setUserRole] = useState<string>("");
  const [currentUserInfo, setCurrentUserInfo] = useState<any>(null);

  // Rating system states
  const [allRatings, setAllRatings] = useState<any[]>([]);
  const [myRatings, setMyRatings] = useState<any[]>([]);
  const [isRatingModalVisible, setIsRatingModalVisible] = useState(false);
  const [currentRatingUser, setCurrentRatingUser] =
    useState<UserWithParentDTO | null>(null);
  const [ratingForm, setRatingForm] = useState({
    toplaBeceri: undefined as number | undefined,
    fizikselOzellik: undefined as number | undefined,
    notes: "",
  });
  const [activeTab, setActiveTab] = useState("applications");
  const [ratingsSortBy, setRatingsSortBy] = useState<
    "topla" | "fiziksel" | "combined" | "count"
  >("combined");
  const [ratingsSortOrder, setRatingsSortOrder] = useState<"asc" | "desc">(
    "desc"
  );

  // Add pagination states
  const [ratingsCurrentPage, setRatingsCurrentPage] = useState(1);
  const [ratingsPageSize, setRatingsPageSize] = useState(10);
  const [ratingsLoading, setRatingsLoading] = useState(false);

  // Moderator management states
  const [moderators, setModerators] = useState<ModeratorDTO[]>([]);
  const [moderatorLoading, setModeratorLoading] = useState(false);
  const [isModeratorModalVisible, setIsModeratorModalVisible] = useState(false);
  const [isDeleteModeratorModalVisible, setIsDeleteModeratorModalVisible] =
    useState(false);
  const [currentModerator, setCurrentModerator] = useState<ModeratorDTO | null>(
    null
  );
  const [moderatorForm] = Form.useForm();

  const zodForm = useForm<FormValues>({
    resolver: zodResolver(formSchema) as any,
    defaultValues: {
      fullName: "",
      height: "",
      weight: "",
      email: "",
      phoneNumber: "",
      province: "",
      district: "",
      armSpan: "",
      shoeSize: "",
      palmSize: "",
      jumpHeight: "",
      jumpRecordDate: undefined,
      hasPlayedForClub: false,
      clubName: "",
      parentFullName: "",
      parentPhone: "",
      parentRelationshipType: "",
      customRelationshipType: "",
      motherHeight: "",
      fatherHeight: "",
      hasHealthIssues: false,
      healthIssues: "",
      approvalGiven: false,
      status: -1,
    },
  });

  const fetchApplications = useCallback(async () => {
    console.log("fetchApplications called"); // Debug log
    setLoading(true);
    try {
      console.log("Making API request to:", apiEndpoints.getApplications); // Debug log

      const response = await fetchApi(apiEndpoints.getApplications, {
        method: "GET",
      });

      console.log("Response status:", response.status); // Debug log

      if (!response.ok) {
        if (response.status === 401) {
          console.log("Unauthorized, logging out"); // Debug log
          logoutAdmin();
          router.push("/admin/login");
          return;
        }
        const errorText = await response.text();
        console.error("API Error:", errorText); // Debug log
        throw new Error("Başvuru alınırken bir hata oluştu");
      }

      const data = await response.json();
      console.log("Received data:", data); // Debug log

      const normalized = data.map((user: any) => ({
        ...user,
        email: user.email || "",
        province: user.province || "",
        district: user.district || "",
        armSpan: user.armSpan?.toString() ?? "",
        shoeSize: user.shoeSize?.toString() ?? "",
        palmSize: user.palmSize?.toString() ?? "",
        jumpHeight: user.jumpHeight?.toString() ?? "",
        jumpRecordDate: user.jumpRecordDate
          ? new Date(user.jumpRecordDate)
          : null,
        hasPlayedForClub: user.hasPlayedForClub ?? false,
        hasHealthIssues: user.hasHealthIssues ?? false,
        clubName: user.clubName || "",
        healthIssues: user.healthIssues || "",
        status: user.status ?? -1,
        parent: {
          ...user.parent,
          fullName: user.parent?.fullName || "",
          mobilePhone: user.parent?.mobilePhone || "",
          motherHeight: user.parent?.motherHeight?.toString() ?? "",
          fatherHeight: user.parent?.fatherHeight?.toString() ?? "",
          approvalGiven: user.parent?.approvalGiven ?? false,
        },
      }));

      console.log("Normalized data:", normalized); // Debug log
      setApplications(normalized);
      setFilteredData(normalized);
    } catch (error) {
      console.error("Error fetching applications:", error);
      messageApi.error(
        "Başvurular alınırken bir hata oluştu: " + error.message
      );
    } finally {
      setLoading(false);
    }
  }, [messageApi, router]);

  useEffect(() => {
    const checkAuthAndFetch = async () => {
      console.log("checkAuthAndFetch called"); // Debug log
      setAuthLoading(true);

      if (isAuthenticated()) {
        console.log("User is authenticated"); // Debug log
        try {
          // Get user role and info
          const userInfo = getCurrentUser();
          console.log("Current user info:", userInfo); // Debug log
          setCurrentUserInfo(userInfo);

          if (isAdmin()) {
            console.log("User is admin"); // Debug log
            setUserRole("Admin");
            await fetchApplications();
            await fetchRatingsData();
            await fetchAgeStatisticsData();
          } else if (isModerator()) {
            console.log("User is moderator"); // Debug log
            setUserRole("Moderator");
            await fetchApplications();
            await fetchMyRatingsData();
            await fetchAgeStatisticsData();
          } else {
            console.log("User has no valid role"); // Debug log
            router.push("/admin/login");
          }
        } catch (error) {
          console.error("Error in checkAuthAndFetch:", error);
          messageApi.error("Yetkilendirme kontrolünde hata oluştu");
        } finally {
          setAuthLoading(false);
        }
      } else {
        console.log("User not authenticated, redirecting to login"); // Debug log
        router.push("/admin/login");
        setTimeout(() => {
          setAuthLoading(false);
        }, 500);
      }
    };

    checkAuthAndFetch();
  }, [fetchApplications, router, messageApi]);

  // Fetch all ratings data (Admin only)
  const fetchRatingsData = async () => {
    setRatingsLoading(true);
    try {
      console.log("Fetching ratings data"); // Debug log
      const ratings = await fetchAllRatings();
      console.log("Ratings data:", ratings); // Debug log
      setAllRatings(ratings);
    } catch (error) {
      console.error("Error fetching ratings:", error);
      messageApi.error("Puanlar alınırken hata oluştu");
    } finally {
      setRatingsLoading(false);
    }
  };

  // Fetch moderator's own ratings
  const fetchMyRatingsData = async () => {
    try {
      console.log("Fetching my ratings data"); // Debug log
      const ratings = await fetchMyRatings();
      console.log("My ratings data:", ratings); // Debug log
      setMyRatings(ratings);
    } catch (error) {
      console.error("Error fetching my ratings:", error);
      messageApi.error("Puanlarım alınırken hata oluştu");
    }
  };

  // Fetch age statistics
  const fetchAgeStatisticsData = async () => {
    try {
      const data = await fetchAgeStatistics();
      setAgeStatistics(data);
    } catch (error) {
      console.error("Error fetching age statistics:", error);
      messageApi.error("Yaş istatistikleri alınırken bir hata oluştu");
    }
  };

  // Get filtered age statistics based on selected age
  const getFilteredAgeStatistics = () => {
    if (!ageStatistics || ageFilter === null) {
      return ageStatistics;
    }

    // Filter age groups to show only the selected age
    const filteredAgeGroups = ageStatistics.ageGroups.filter(
      (group: any) => group.age === ageFilter
    );

    // Calculate totals for the filtered groups
    const totalApplications = filteredAgeGroups.reduce(
      (sum: number, group: any) => sum + group.count,
      0
    );

    return {
      ...ageStatistics,
      totalApplications,
      ageGroups: filteredAgeGroups,
      ageRanges: ageStatistics.ageRanges
        .map((range: any) => ({
          ...range,
          count: filteredAgeGroups.reduce((sum: number, group: any) => {
            // Check if this age falls within the range
            if (range.range.includes(`${ageFilter} yaş`)) {
              return sum + group.count;
            }
            return sum;
          }, 0),
        }))
        .filter((range: any) => range.count > 0),
    };
  };

  // Handle rating for a user
  const handleRateUser = async (user: UserWithParentDTO) => {
    setCurrentRatingUser(user);

    // Check if current user already rated this user
    try {
      const existingRating = await fetchMyRatingForUser(user.id);
      if (existingRating) {
        setRatingForm({
          toplaBeceri: existingRating.toplaBeceri,
          fizikselOzellik: existingRating.fizikselOzellik,
          notes: existingRating.notes || "",
        });
      } else {
        setRatingForm({
          toplaBeceri: undefined,
          fizikselOzellik: undefined,
          notes: "",
        });
      }
    } catch (error) {
      setRatingForm({
        toplaBeceri: undefined,
        fizikselOzellik: undefined,
        notes: "",
      });
      console.error("Error fetching existing rating:", error);
    }

    setIsRatingModalVisible(true);
  };

  // Submit rating
  const handleSubmitRating = async () => {
    if (!currentRatingUser) return;

    try {
      await createOrUpdateRating({
        userId: currentRatingUser.id,
        toplaBeceri: ratingForm.toplaBeceri,
        fizikselOzellik: ratingForm.fizikselOzellik,
        notes: ratingForm.notes,
      });

      messageApi.success("Puanlama başarıyla kaydedildi");
      setIsRatingModalVisible(false);

      // Refresh ratings data
      if (isAdmin()) {
        await fetchRatingsData();
      } else {
        await fetchMyRatingsData();
      }
    } catch (error: any) {
      messageApi.error(error.message || "Puanlama kaydedilirken hata oluştu");
    }
  };

  // Helper function to get status text
  const getStatusText = (status: number | null | undefined): string => {
    switch (status) {
      case -1:
        return "Seçim Yapılmadı";
      case 0:
        return "Seçmeye Katılmadı";
      case 1:
        return "Takıma Seçildi";
      case 2:
        return "Takıma Seçilmedi";
      default:
        return "Seçim Yapılmadı";
    }
  };

  // Helper function to get status color
  const getStatusColor = (status: number | null | undefined): string => {
    switch (status) {
      case -1:
        return "default";
      case 0:
        return "orange";
      case 1:
        return "green";
      case 2:
        return "red";
      default:
        return "default";
    }
  };

  const getCombinedAverage = (userRating: any) => {
    const topla = userRating.averageToplaBeceri || 0;
    const fiziksel = userRating.averageFizikselOzellik || 0;
    if (topla === 0 && fiziksel === 0) return 0;
    if (topla === 0) return fiziksel;
    if (fiziksel === 0) return topla;
    return (topla + fiziksel) / 2;
  };

  const getSortedRatings = () => {
    if (!allRatings.length) return [];

    const sorted = [...allRatings].sort((a, b) => {
      let valueA, valueB;

      switch (ratingsSortBy) {
        case "topla":
          valueA = a.averageToplaBeceri || 0;
          valueB = b.averageToplaBeceri || 0;
          break;
        case "fiziksel":
          valueA = a.averageFizikselOzellik || 0;
          valueB = b.averageFizikselOzellik || 0;
          break;
        case "combined":
          valueA = getCombinedAverage(a);
          valueB = getCombinedAverage(b);
          break;
        case "count":
          valueA = a.ratingCount || 0;
          valueB = b.ratingCount || 0;
          break;
        default:
          valueA = getCombinedAverage(a);
          valueB = getCombinedAverage(b);
      }

      return ratingsSortOrder === "desc" ? valueB - valueA : valueA - valueB;
    });

    return sorted;
  };

  // Add pagination helper function
  const getPaginatedRatings = () => {
    const sortedRatings = getSortedRatings();
    const startIndex = (ratingsCurrentPage - 1) * ratingsPageSize;
    const endIndex = startIndex + ratingsPageSize;
    return sortedRatings.slice(startIndex, endIndex);
  };

  // Add function to handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    setRatingsPageSize(pageSize);
    setRatingsCurrentPage(1); // Reset to first page when changing page size
  };

  const getRankBadgeColor = (index: number) => {
    if (index === 0) return "#FFD700";
    if (index === 1) return "#C0C0C0";
    if (index === 2) return "#CD7F32";
    return "#1890ff";
  };

  const columns = [
    {
      title: "Takip Kodu",
      dataIndex: "trackingCode",
      key: "trackingCode",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        a.trackingCode.localeCompare(b.trackingCode),
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: "Tam Ad",
      dataIndex: "fullName",
      key: "fullName",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        a.fullName.localeCompare(b.fullName),
    },
    {
      title: "İl",
      dataIndex: "province",
      key: "province",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        (a.province || "").localeCompare(b.province || ""),
      render: (text: string) => text || "-",
    },
    {
      title: "İlçe",
      dataIndex: "district",
      key: "district",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        (a.district || "").localeCompare(b.district || ""),
      render: (text: string) => text || "-",
    },
    {
      title: "Doğum Tarihi",
      dataIndex: "dateOfBirth",
      key: "dateOfBirth",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        new Date(a.dateOfBirth).getTime() - new Date(b.dateOfBirth).getTime(),
      render: (text: string) => dayjs(text).format("DD/MM/YYYY"),
    },
    {
      title: "Boy",
      dataIndex: "height",
      key: "height",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        a.height - b.height,
      render: (text: number) => `${text} cm`,
    },
    {
      title: "Kilo",
      dataIndex: "weight",
      key: "weight",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        a.weight - b.weight,
      render: (text: number) => `${text} kg`,
    },
    {
      title: "Kulüp Tecrübesi",
      dataIndex: "hasPlayedForClub",
      key: "hasPlayedForClub",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        a.hasPlayedForClub === b.hasPlayedForClub
          ? 0
          : a.hasPlayedForClub
          ? -1
          : 1,
      render: (hasClub: boolean) => (
        <Tag color={hasClub ? "green" : "orange"}>
          {hasClub ? "Var" : "Yok"}
        </Tag>
      ),
    },
    {
      title: "Sıçrama Yüksekliği",
      dataIndex: "jumpHeight",
      key: "jumpHeight",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        (a.jumpHeight || 0) - (b.jumpHeight || 0),
      render: (text: number) => (text ? `${text} cm` : "-"),
    },

    {
      title: "Durum",
      dataIndex: "status",
      key: "status",
      sorter: (a: UserWithParentDTO, b: UserWithParentDTO) =>
        (a.status ?? 0) - (b.status ?? 0),
      render: (status: number | null | undefined) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: "İşlemler",
      key: "action",
      render: (_: any, record: UserWithParentDTO) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            title="Görüntüle"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="Düzenle"
          />
          {isModerator() && (
            <Button
              type="text"
              icon={<StarOutlined />}
              onClick={() => handleRateUser(record)}
              title="Puanla"
              style={{ color: "#faad14" }}
            />
          )}
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              setCurrentUser(record);
              setIsDeleteModalVisible(true);
            }}
            title="Sil"
          />
          <Button
            type="text"
            icon={
              <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                <rect
                  x="3"
                  y="3"
                  width="7"
                  height="7"
                  stroke="#333"
                  strokeWidth="2"
                />
                <rect
                  x="14"
                  y="3"
                  width="7"
                  height="7"
                  stroke="#333"
                  strokeWidth="2"
                />
                <rect
                  x="3"
                  y="14"
                  width="7"
                  height="7"
                  stroke="#333"
                  strokeWidth="2"
                />
                <path d="M14 17h3v-3" stroke="#333" strokeWidth="2" />
                <circle cx="17.5" cy="17.5" r="1.5" fill="#333" />
              </svg>
            }
            onClick={() => {
              setCurrentUser(record);
              setIsQRCodeModalVisible(true);
            }}
            title="QR Kod"
          />
        </Space>
      ),
    },
  ];

  const handleRelationshipTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value;
    form.setFieldsValue({ parentRelationshipType: value });

    const customField = document.querySelector(".custom-relationship-field");
    if (customField) {
      (customField as HTMLElement).style.display =
        value === "Diğer" ? "block" : "none";
    }
  };

  useEffect(() => {
    if (form.getFieldValue("parentRelationshipType") === "Diğer") {
      // Make sure the custom field is visible when "Diğer" is selected
      const customField = document.querySelector(".custom-relationship-field");
      if (customField) {
        (customField as HTMLElement).style.display = "block";
      }
    }
  }, [form, isModalVisible]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    applyFilters(value, statusFilter, ageFilter);
  };

  const handleStatusFilter = (status: number | null) => {
    setStatusFilter(status);
    applyFilters(searchText, status, ageFilter);
  };

  const handleAgeFilter = (age: number | null) => {
    setAgeFilter(age);
    applyFilters(searchText, statusFilter, age);
  };

  const applyFilters = (
    search: string,
    status: number | null,
    age: number | null
  ) => {
    let filtered = applications;

    // Apply text search if active
    if (search) {
      filtered = filtered.filter(
        (user) =>
          user.fullName.toLowerCase().includes(search.toLowerCase()) ||
          user.trackingCode.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply status filter
    if (status !== null) {
      filtered = filtered.filter((user) => user.status === status);
    }

    // Apply age filter
    if (age !== null) {
      const currentYear = new Date().getFullYear();
      filtered = filtered.filter((user) => {
        const userAge = currentYear - new Date(user.dateOfBirth).getFullYear();
        return userAge === age;
      });
    }

    setFilteredData(filtered);
  };

  const handleLogout = () => {
    logoutAdmin();
    messageApi.success("Çıkış yapıldı");
    router.push("/admin/login");
  };

  const handleEdit = (user: UserWithParentDTO) => {
    setCurrentUser(user);
    setCurrentParent(user.parent || null);
    setIsViewMode(false);

    // Check if the relationship type is one of the standard options
    const standardTypes = ["Anne", "Baba"];
    const relationshipType = user.parent?.relationshipType || "";
    const isCustomType =
      relationshipType && !standardTypes.includes(relationshipType);

    form.setFieldsValue({
      fullName: user.fullName,
      dateOfBirth: dayjs(user.dateOfBirth),
      height: user.height,
      weight: user.weight,
      email: user.email || "",
      phoneNumber: user.phoneNumber,
      province: user.province || "",
      district: user.district || "",
      armSpan: user.armSpan ?? "",
      shoeSize: user.shoeSize ?? "",
      palmSize: user.palmSize ?? "",
      jumpHeight: user.jumpHeight ?? "",
      jumpRecordDate: user.jumpRecordDate ? dayjs(user.jumpRecordDate) : null,
      hasPlayedForClub: user.hasPlayedForClub ?? false,
      clubName: user.clubName || "",
      hasHealthIssues: user.hasHealthIssues ?? false,
      healthIssues: user.healthIssues || "",
      status: user.status ?? 0,
      parentFullName: user.parent?.fullName || "",
      parentPhone: user.parent?.mobilePhone || "",
      parentRelationshipType: isCustomType ? "Diğer" : relationshipType,
      customRelationshipType: isCustomType ? relationshipType : "",
      motherHeight: user.parent?.motherHeight ?? "",
      fatherHeight: user.parent?.fatherHeight ?? "",
      approvalGiven: user.parent?.approvalGiven ?? false,
    });
    setIsModalVisible(true);
  };

  const handleView = (user: UserWithParentDTO) => {
    setCurrentUser(user);
    setCurrentParent(user.parent || null);
    setIsViewMode(true);

    const standardTypes = ["Anne", "Baba"];
    const relationshipType = user.parent?.relationshipType || "";
    const isCustomType =
      relationshipType && !standardTypes.includes(relationshipType);

    form.setFieldsValue({
      fullName: user.fullName,
      dateOfBirth: dayjs(user.dateOfBirth),
      height: user.height,
      weight: user.weight,
      email: user.email || "",
      phoneNumber: user.phoneNumber,
      province: user.province || "",
      district: user.district || "",
      armSpan: user.armSpan ?? "",
      shoeSize: user.shoeSize ?? "",
      palmSize: user.palmSize ?? "",
      jumpHeight: user.jumpHeight ?? "",
      jumpRecordDate: user.jumpRecordDate ? dayjs(user.jumpRecordDate) : null,
      hasPlayedForClub: user.hasPlayedForClub ?? false,
      clubName: user.clubName || "",
      hasHealthIssues: user.hasHealthIssues ?? false,
      healthIssues: user.healthIssues || "",
      parentFullName: user.parent?.fullName || "",
      parentPhone: user.parent?.mobilePhone || "",
      parentRelationshipType: relationshipType,
      customRelationshipType: isCustomType ? relationshipType : "",
      motherHeight: user.parent?.motherHeight ?? "",
      fatherHeight: user.parent?.fatherHeight ?? "",
      approvalGiven: user.parent?.approvalGiven ?? false,
    });
    setIsModalVisible(true);
  };

  const handleAddNew = () => {
    form.resetFields();
    setCurrentUser(null);
    setCurrentParent(null);
    setIsViewMode(false);
    setIsAddModalVisible(true);
  };

  const handleUpdate = async (values: any) => {
    if (!currentUser || !currentParent) return;

    setLoading(true);
    setBackendErrors([]);
    try {
      const finalRelationshipType =
        values.parentRelationshipType === "Diğer"
          ? values.customRelationshipType
          : values.parentRelationshipType;

      const updatedUser = {
        id: currentUser.id,
        fullName: values.fullName,
        dateOfBirth: values.dateOfBirth.toISOString(),
        height: Number(values.height),
        weight: Number(values.weight),
        phoneNumber: values.phoneNumber,
        email: values.email,
        province: values.province || null,
        district: values.district || null,
        armSpan: values.armSpan ? Number(values.armSpan) : null,
        shoeSize: Number(values.shoeSize),
        palmSize: values.palmSize ? Number(values.palmSize) : null,
        jumpHeight: values.jumpHeight ? Number(values.jumpHeight) : null,
        jumpRecordDate: values.jumpRecordDate
          ? values.jumpRecordDate.toISOString()
          : null,
        hasPlayedForClub: values.hasPlayedForClub,
        clubName: values.clubName || "",
        hasHealthIssues: values.hasHealthIssues,
        healthIssues: values.healthIssues || "",
        status: values.status,
        parentId: currentParent.id,
        parent: {
          id: currentParent.id,
          fullName: values.parentFullName,
          mobilePhone: values.parentPhone,
          relationshipType: finalRelationshipType,
          motherHeight: Number(values.motherHeight),
          fatherHeight: Number(values.fatherHeight),
          approvalGiven: values.approvalGiven,
        },
      };

      const response = await fetchApi(
        `${apiEndpoints.updateStatus}/${currentUser.id}`,
        {
          method: "PUT",
          body: JSON.stringify(updatedUser),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessages: string[] = [];

        // Handle unauthorized access
        if (response.status === 401) {
          logoutAdmin();
          router.push("/admin/login");
          return;
        }

        // Handle RFC 7807/problem+json error format
        if (errorData.errors && typeof errorData.errors === "object") {
          Object.keys(errorData.errors).forEach((key) => {
            const messages = errorData.errors[key];
            if (Array.isArray(messages)) {
              messages.forEach((msg) => errorMessages.push(`${key}: ${msg}`));
            } else {
              errorMessages.push(`${key}: ${messages}`);
            }
          });
          setBackendErrors(errorMessages);
          throw new Error("Validation failed");
        }
        // Handle other error formats
        else if (Array.isArray(errorData.errors)) {
          setBackendErrors(errorData.errors);
          throw new Error("Validation failed");
        } else if (errorData.message) {
          setBackendErrors([errorData.message]);
          throw new Error(errorData.message);
        } else {
          setBackendErrors(["Kullanıcı güncellenirken bir hata oluştu"]);
          throw new Error("Kullanıcı güncellenirken bir hata oluştu");
        }
      }

      const parentResponse = await fetchApi(
        `${API_BASE_URL}/Parents/${currentParent.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            id: currentParent.id,
            fullName: values.parentFullName,
            mobilePhone: values.parentPhone,
            relationshipType: finalRelationshipType,
            motherHeight: Number(values.motherHeight),
            fatherHeight: Number(values.fatherHeight),
            approvalGiven: values.approvalGiven,
          }),
        }
      );

      if (!parentResponse.ok) {
        throw new Error("Veli bilgileri güncellenirken bir hata oluştu");
      }

      messageApi.success("Başvuru başarıyla güncellendi");
      setIsModalVisible(false);
      fetchApplications();
    } catch (error: any) {
      console.error("Error updating application:", error);

      // backendErrors already set above if validation failed
      if (!backendErrors.length) {
        if (error.message) {
          messageApi.error(error.message);
        } else {
          messageApi.error("Güncelleme sırasında bir hata oluştu");
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const handleZodCreate = async (values: FormValues) => {
    try {
      setBackendErrors([]);
      setLoading(true);

      // Handle relationship type
      const finalRelationshipType =
        values.parentRelationshipType === "Diğer"
          ? values.customRelationshipType
          : values.parentRelationshipType;

      const formData = {
        user: {
          fullName: values.fullName,
          dateOfBirth: values.birthDate,
          height: Number(values.height),
          weight: Number(values.weight),
          phoneNumber: values.phoneNumber,
          email: values.email,
          province: values.province || null,
          district: values.district || null,
          armSpan: values.armSpan ? Number(values.armSpan) : null,
          shoeSize: Number(values.shoeSize),
          palmSize: values.palmSize ? Number(values.palmSize) : null,
          jumpHeight: values.jumpHeight ? Number(values.jumpHeight) : null,
          jumpRecordDate: values.jumpRecordDate,
          hasPlayedForClub: values.hasPlayedForClub,
          clubName: values.clubName || "",
          hasHealthIssues: values.hasHealthIssues,
          healthIssues: values.healthIssues || "",
          status: values.status ?? 0,
        },
        parent: {
          fullName: values.parentFullName,
          mobilePhone: values.parentPhone,
          relationshipType: finalRelationshipType,
          motherHeight: Number(values.motherHeight),
          fatherHeight: Number(values.fatherHeight),
          approvalGiven: values.approvalGiven,
          approvalDate: new Date().toISOString(),
        },
      };

      const response = await fetchApi(apiEndpoints.submitForm, {
        method: "POST",
        body: JSON.stringify(formData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        // Handle unauthorized access
        if (response.status === 401) {
          logoutAdmin();
          router.push("/admin/login");
          return;
        }
        if (responseData.errors) {
          // Handle validation errors array format
          if (Array.isArray(responseData.errors)) {
            setBackendErrors(responseData.errors);
          }
          // Handle object with property names as keys format
          else {
            const errorMessages: string[] = [];
            Object.keys(responseData.errors).forEach((key) => {
              const messages = responseData.errors[key];
              if (Array.isArray(messages)) {
                messages.forEach((msg) => errorMessages.push(`${key}: ${msg}`));
              } else {
                errorMessages.push(`${key}: ${messages}`);
              }
            });
            setBackendErrors(errorMessages);
          }
        } else if (responseData.message) {
          setBackendErrors([responseData.message]);
        } else {
          setBackendErrors(["Başvuru eklenirken bir hata oluştu"]);
        }
        throw new Error("Validation failed");
      }

      messageApi.success("Başvuru başarıyla eklendi");
      setIsAddModalVisible(false);
      zodForm.reset({
        fullName: "",
        birthDate: undefined,
        height: "",
        weight: "",
        email: "",
        phoneNumber: "",
        province: "",
        district: "",
        armSpan: "",
        shoeSize: "",
        palmSize: "",
        jumpHeight: "",
        jumpRecordDate: undefined,
        hasPlayedForClub: false,
        clubName: "",
        parentFullName: "",
        parentPhone: "",
        parentRelationshipType: "",
        customRelationshipType: "",
        motherHeight: "",
        fatherHeight: "",
        hasHealthIssues: false,
        healthIssues: "",
        approvalGiven: false,
        status: -1,
      });
      fetchApplications();
    } catch (error: any) {
      console.error("Error creating applicationasd:", error);

      if (!backendErrors.length) {
        messageApi.error("Başvuru eklenirken bir hata oluştu");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      const response = await fetchApi(
        `${apiEndpoints.updateStatus}/${currentUser.id}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          logoutAdmin();
          router.push("/admin/login");
          return;
        }
        throw new Error("Başvuru silinirken bir hata oluştu");
      }

      messageApi.success("Başvuru başarıyla silindi");
      setIsDeleteModalVisible(false);
      fetchApplications();
    } catch (error) {
      console.error("Error deleting application:", error);
      messageApi.error("Silme işlemi sırasında bir hata oluştu");
    } finally {
      setLoading(false);
    }
  };

  // Moderator management functions
  const fetchModeratorsData = async () => {
    setModeratorLoading(true);
    try {
      const moderatorList = await fetchModerators();
      setModerators(moderatorList);
    } catch (error) {
      console.error("Error fetching moderators:", error);
      messageApi.error("Moderatorler alınırken hata oluştu");
    } finally {
      setModeratorLoading(false);
    }
  };

  const handleCreateModerator = async (values: any) => {
    setModeratorLoading(true);
    try {
      await createModerator({
        fullName: values.fullName,
        email: values.email,
        password: values.password,
      });

      messageApi.success("Moderator başarıyla oluşturuldu");
      setIsModeratorModalVisible(false);
      moderatorForm.resetFields();
      await fetchModeratorsData();
    } catch (error: any) {
      console.error("Error creating moderator:", error);
      messageApi.error(error.message || "Moderator oluşturulurken hata oluştu");
    } finally {
      setModeratorLoading(false);
    }
  };

  const handleDeleteModerator = async () => {
    if (!currentModerator) return;

    setModeratorLoading(true);
    try {
      await deleteModerator(currentModerator.id);
      messageApi.success("Moderator başarıyla silindi");
      setIsDeleteModeratorModalVisible(false);
      setCurrentModerator(null);
      await fetchModeratorsData();
    } catch (error: any) {
      console.error("Error deleting moderator:", error);
      messageApi.error(error.message || "Moderator silinirken hata oluştu");
    } finally {
      setModeratorLoading(false);
    }
  };

  const showDeleteModeratorModal = (moderator: ModeratorDTO) => {
    setCurrentModerator(moderator);
    setIsDeleteModeratorModalVisible(true);
  };

  // Add edit moderator states
  const [isEditModeratorModalVisible, setIsEditModeratorModalVisible] =
    useState(false);
  const [editModeratorForm] = Form.useForm();

  // Add edit moderator function
  const handleEditModerator = async (values: any) => {
    if (!currentModerator) return;

    setModeratorLoading(true);
    try {
      const updateData: any = {
        fullName: values.fullName,
        email: values.email,
      };

      // Only include password if it's provided
      if (values.password && values.password.trim() !== "") {
        updateData.password = values.password;
      }

      await updateModerator(currentModerator.id, updateData);

      messageApi.success("Moderator başarıyla güncellendi");
      setIsEditModeratorModalVisible(false);
      editModeratorForm.resetFields();
      setCurrentModerator(null);
      await fetchModeratorsData();
    } catch (error: any) {
      console.error("Error updating moderator:", error);
      messageApi.error(error.message || "Moderator güncellenirken hata oluştu");
    } finally {
      setModeratorLoading(false);
    }
  };

  const showEditModeratorModal = (moderator: ModeratorDTO) => {
    setCurrentModerator(moderator);
    editModeratorForm.setFieldsValue({
      fullName: moderator.fullName,
      email: moderator.email,
      password: "", // Always start with empty password
    });
    setIsEditModeratorModalVisible(true);
  };

  // Excel export function
  const handleExportToExcel = async () => {
    try {
      const response = await fetchApi(`${API_BASE_URL}/form/export/excel`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("fbAdminToken")}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        message.error(errorData.message || "Excel dosyası indirilemedi");
        return;
      }

      // Create blob from response
      const blob = await response.blob();

      // Extract filename from Content-Disposition header or use default
      const contentDisposition = response.headers.get("Content-Disposition");
      let filename = `Basvuru_Listesi_${new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[:-]/g, "")}.xlsx`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch) {
          filename = filenameMatch[1].replace(/"/g, "");
        }
      }

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();

      // Cleanup
      link.remove();
      window.URL.revokeObjectURL(url);

      message.success("Excel dosyası başarıyla indirildi");
    } catch (error) {
      console.error("Excel export error:", error);
      message.error("Excel dosyası indirilemedi. Lütfen tekrar deneyin.");
    }
  };

  // Define moderator columns here, after the handler functions
  const moderatorColumns = [
    {
      title: "Ad Soyad",
      dataIndex: "fullName",
      key: "fullName",
    },
    {
      title: "E-posta",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Kullanıcı Adı",
      dataIndex: "userName",
      key: "userName",
    },
    {
      title: "İşlemler",
      key: "action",
      render: (_: any, record: ModeratorDTO) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditModeratorModal(record)}
            title="Düzenle"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => showDeleteModeratorModal(record)}
            title="Sil"
          />
        </Space>
      ),
    },
  ];

  // Load moderators data when tab changes to moderators
  useEffect(() => {
    if (activeTab === "moderators" && isAdmin()) {
      fetchModeratorsData();
    }
  }, [activeTab]);

  // Load age statistics data when tab changes to age statistics
  useEffect(() => {
    if (activeTab === "age-statistics") {
      fetchAgeStatisticsData();
    }
  }, [activeTab]);

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Spin size="large" />
          <p className="mt-4 text-gray-600">
            Yetkilendirme kontrol ediliyor...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {contextHolder}
      <header className="bg-gradient-to-r from-[#021F4A] to-[#0A4392] text-white shadow-md">
        <div className="max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8 py-4 ">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3 ">
              <Image
                src="/logo.png"
                alt="Fenerbahçe Logo"
                width={40}
                height={40}
              />
              <h1 className="text-xl font-bold !-mb-1 ">
                Fenerbahçe Basketbol Seçmeleri - Yönetici Paneli
              </h1>
            </div>
            <Button onClick={handleLogout} type="link" className="!text-white">
              Çıkış Yap
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg p-6">
          {/* Tab Navigation */}
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: "applications",
                label: (
                  <span>
                    <TeamOutlined />
                    Başvurular
                  </span>
                ),
                children: (
                  <>
                    <div className="flex justify-between items-center mb-6">
                      <h2 className="text-xl font-semibold text-gray-800">
                        Başvuru Listesi
                      </h2>
                      <div className="flex gap-4">
                        <Input
                          placeholder="Ara..."
                          prefix={<SearchOutlined />}
                          value={searchText}
                          onChange={(e) => handleSearch(e.target.value)}
                          style={{ width: 250 }}
                        />

                        {/* Add status filter buttons */}
                        <div className="flex flex-col gap-3">
                          <div className="flex gap-2">
                            <span className="text-sm text-gray-600 self-center min-w-[60px]">
                              Durum:
                            </span>
                            <Button
                              type={
                                statusFilter === null ? "primary" : "default"
                              }
                              onClick={() => handleStatusFilter(null)}
                              className={
                                statusFilter === null
                                  ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                  : ""
                              }
                              size="small"
                            >
                              Tümü
                            </Button>
                            <Button
                              type={statusFilter === -1 ? "primary" : "default"}
                              onClick={() => handleStatusFilter(-1)}
                              className={
                                statusFilter === -1
                                  ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                  : ""
                              }
                              size="small"
                            >
                              Seçim Yapılmadı
                            </Button>
                            <Button
                              type={statusFilter === 0 ? "primary" : "default"}
                              onClick={() => handleStatusFilter(0)}
                              className={
                                statusFilter === 0
                                  ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                  : ""
                              }
                              size="small"
                            >
                              Seçmeye Katılmadı
                            </Button>
                            <Button
                              type={statusFilter === 1 ? "primary" : "default"}
                              onClick={() => handleStatusFilter(1)}
                              className={
                                statusFilter === 1
                                  ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                  : ""
                              }
                              size="small"
                            >
                              Takıma Seçildi
                            </Button>
                            <Button
                              type={statusFilter === 2 ? "primary" : "default"}
                              onClick={() => handleStatusFilter(2)}
                              className={
                                statusFilter === 2
                                  ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                  : ""
                              }
                              size="small"
                            >
                              Takıma Seçilmedi
                            </Button>
                          </div>

                          {/* Add age filter dropdown */}
                          <div className="flex gap-2 items-center">
                            <span className="text-sm text-gray-600 min-w-[60px]">
                              Yaş:
                            </span>
                            <Select
                              placeholder="Yaş seçin"
                              allowClear
                              style={{ width: 150 }}
                              value={ageFilter}
                              onChange={(value) => handleAgeFilter(value)}
                              options={
                                ageStatistics?.ageGroups?.map((group: any) => ({
                                  value: group.age,
                                  label: `${group.age} yaş`,
                                })) || []
                              }
                            />
                          </div>
                        </div>

                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={handleAddNew}
                          className="bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                        >
                          Yeni Ekle
                        </Button>
                      </div>
                    </div>

                    {loading ? (
                      <div className="flex justify-center py-8">
                        <Spin size="large" />
                      </div>
                    ) : (
                      <Table
                        dataSource={filteredData}
                        columns={columns}
                        rowKey="id"
                        pagination={{ pageSize: 10 }}
                        bordered
                        scroll={{ x: true }}
                      />
                    )}
                  </>
                ),
              },
              // Admin tab - Tüm puanları görüntüleme
              ...(isAdmin()
                ? [
                    {
                      key: "ratings",
                      label: (
                        <span>
                          <TrophyOutlined />
                          Tüm Puanlar
                        </span>
                      ),
                      children: (
                        <div>
                          <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-semibold text-gray-800">
                              Tüm Kullanıcı Puanları
                            </h2>
                            <div className="flex gap-3 items-center">
                              <span className="text-sm text-gray-600">
                                Sıralama:
                              </span>
                              <Button.Group>
                                <Button
                                  type={
                                    ratingsSortBy === "combined"
                                      ? "primary"
                                      : "default"
                                  }
                                  onClick={() => setRatingsSortBy("combined")}
                                  size="middle"
                                  className={
                                    ratingsSortBy === "combined"
                                      ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                      : ""
                                  }
                                >
                                  Genel Ortalama
                                </Button>
                                <Button
                                  type={
                                    ratingsSortBy === "topla"
                                      ? "primary"
                                      : "default"
                                  }
                                  onClick={() => setRatingsSortBy("topla")}
                                  size="middle"
                                  className={
                                    ratingsSortBy === "topla"
                                      ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                      : ""
                                  }
                                >
                                  Topla Beceri
                                </Button>
                                <Button
                                  type={
                                    ratingsSortBy === "fiziksel"
                                      ? "primary"
                                      : "default"
                                  }
                                  onClick={() => setRatingsSortBy("fiziksel")}
                                  size="middle"
                                  className={
                                    ratingsSortBy === "fiziksel"
                                      ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                      : ""
                                  }
                                >
                                  Fiziksel Özellik
                                </Button>
                                <Button
                                  type={
                                    ratingsSortBy === "count"
                                      ? "primary"
                                      : "default"
                                  }
                                  onClick={() => setRatingsSortBy("count")}
                                  size="middle"
                                  className={
                                    ratingsSortBy === "count"
                                      ? "bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                                      : ""
                                  }
                                >
                                  Puan Sayısı
                                </Button>
                              </Button.Group>
                              <Button
                                type="text"
                                icon={ratingsSortOrder === "desc" ? "↓" : "↑"}
                                onClick={() =>
                                  setRatingsSortOrder(
                                    ratingsSortOrder === "desc" ? "asc" : "desc"
                                  )
                                }
                                size="middle"
                                title={
                                  ratingsSortOrder === "desc"
                                    ? "Azalan sıralama"
                                    : "Artan sıralama"
                                }
                              >
                                {ratingsSortOrder === "desc"
                                  ? "Yüksekten Düşüğe"
                                  : "Düşükten Yükseğe"}
                              </Button>
                            </div>
                          </div>

                          {/* Add summary statistics */}
                          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg mb-6 border">
                            <Row gutter={16}>
                              <Col span={6}>
                                <Statistic
                                  title="Toplam Kullanıcı"
                                  value={allRatings.length}
                                  prefix={<UserOutlined />}
                                  valueStyle={{ color: "#1890ff" }}
                                />
                              </Col>
                              <Col span={6}>
                                <Statistic
                                  title="Puanlanan Kullanıcı"
                                  value={
                                    allRatings.filter((r) => r.ratingCount > 0)
                                      .length
                                  }
                                  prefix={<StarOutlined />}
                                  valueStyle={{ color: "#52c41a" }}
                                />
                              </Col>
                              <Col span={6}>
                                <Statistic
                                  title="Ortalama Genel Puan"
                                  value={
                                    allRatings.length > 0
                                      ? (
                                          allRatings.reduce(
                                            (sum, r) =>
                                              sum + getCombinedAverage(r),
                                            0
                                          ) / allRatings.length
                                        ).toFixed(1)
                                      : 0
                                  }
                                  suffix="/100"
                                  valueStyle={{ color: "#fa8c16" }}
                                />
                              </Col>
                              <Col span={6}>
                                <Statistic
                                  title="Sayfa"
                                  value={`${ratingsCurrentPage} / ${Math.ceil(
                                    allRatings.length / ratingsPageSize
                                  )}`}
                                  prefix={<span>📄</span>}
                                  valueStyle={{ color: "#722ed1" }}
                                />
                              </Col>
                            </Row>
                          </div>

                          {ratingsLoading ? (
                            <div className="flex justify-center py-12">
                              <Spin size="large" />
                              <span className="ml-3 text-gray-600">
                                Puanlar yükleniyor...
                              </span>
                            </div>
                          ) : allRatings.length > 0 ? (
                            <div className="space-y-4">
                              {getPaginatedRatings().map(
                                (userRating, index) => {
                                  const actualIndex =
                                    (ratingsCurrentPage - 1) * ratingsPageSize +
                                    index;
                                  const combinedAverage =
                                    getCombinedAverage(userRating);
                                  return (
                                    <Card
                                      key={userRating.userId}
                                      className="shadow-sm hover:shadow-md transition-shadow !mt-5"
                                      title={
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center gap-3">
                                            <div
                                              className="flex items-center justify-center w-8 h-8 rounded-full text-white font-bold text-sm"
                                              style={{
                                                backgroundColor:
                                                  getRankBadgeColor(
                                                    actualIndex
                                                  ),
                                              }}
                                            >
                                              {actualIndex < 3 ? (
                                                <TrophyOutlined />
                                              ) : (
                                                <span className="font-bold">
                                                  #{actualIndex + 1}
                                                </span>
                                              )}
                                            </div>
                                            <span className="text-lg font-semibold">
                                              {userRating.userName}
                                            </span>
                                          </div>
                                          <div className="flex items-center gap-2">
                                            <Tag
                                              color={
                                                combinedAverage >= 80
                                                  ? "green"
                                                  : combinedAverage >= 60
                                                  ? "orange"
                                                  : "red"
                                              }
                                              className="text-sm font-medium px-3 py-1"
                                            >
                                              Genel Ortalama:{" "}
                                              {combinedAverage.toFixed(1)}/100
                                            </Tag>
                                          </div>
                                        </div>
                                      }
                                    >
                                      <Row gutter={16} className="mb-4">
                                        <Col span={6}>
                                          <Statistic
                                            title="Topla Beceri"
                                            value={
                                              userRating.averageToplaBeceri?.toFixed(
                                                1
                                              ) || "0"
                                            }
                                            suffix="/100"
                                            valueStyle={{
                                              color:
                                                userRating.averageToplaBeceri >=
                                                80
                                                  ? "#52c41a"
                                                  : userRating.averageToplaBeceri >=
                                                    60
                                                  ? "#faad14"
                                                  : "#ff4d4f",
                                            }}
                                          />
                                        </Col>
                                        <Col span={6}>
                                          <Statistic
                                            title="Fiziksel Özellik"
                                            value={
                                              userRating.averageFizikselOzellik?.toFixed(
                                                1
                                              ) || "0"
                                            }
                                            suffix="/100"
                                            valueStyle={{
                                              color:
                                                userRating.averageFizikselOzellik >=
                                                80
                                                  ? "#52c41a"
                                                  : userRating.averageFizikselOzellik >=
                                                    60
                                                  ? "#faad14"
                                                  : "#ff4d4f",
                                            }}
                                          />
                                        </Col>
                                        <Col span={6}>
                                          <Statistic
                                            title="Genel Ortalama"
                                            value={combinedAverage.toFixed(1)}
                                            suffix="/100"
                                            valueStyle={{
                                              color:
                                                combinedAverage >= 80
                                                  ? "#52c41a"
                                                  : combinedAverage >= 60
                                                  ? "#faad14"
                                                  : "#ff4d4f",
                                              fontWeight: "bold",
                                            }}
                                          />
                                        </Col>
                                        <Col span={6}>
                                          <Statistic
                                            title="Toplam Puan Sayısı"
                                            value={userRating.ratingCount}
                                            prefix={<UserOutlined />}
                                          />
                                        </Col>
                                      </Row>

                                      <div className="bg-gray-50 p-4 rounded-lg">
                                        <h4 className="font-medium mb-3 text-gray-700">
                                          Moderatör Puanları:
                                        </h4>
                                        <Table
                                          dataSource={userRating.ratings}
                                          rowKey="id"
                                          pagination={false}
                                          size="small"
                                          className="rating-table"
                                          columns={[
                                            {
                                              title: "Moderatör",
                                              dataIndex: "moderatorName",
                                              key: "moderatorName",
                                              width: 120,
                                              render: (name: string) => (
                                                <Tag
                                                  icon={<UserOutlined />}
                                                  color="blue"
                                                >
                                                  {name}
                                                </Tag>
                                              ),
                                            },
                                            {
                                              title: "Topla Beceri",
                                              dataIndex: "toplaBeceri",
                                              key: "toplaBeceri",
                                              width: 100,
                                              align: "center",
                                              render: (val: number) =>
                                                val ? (
                                                  <Tag
                                                    color={
                                                      val >= 80
                                                        ? "green"
                                                        : val >= 60
                                                        ? "orange"
                                                        : "red"
                                                    }
                                                  >
                                                    {val}/100
                                                  </Tag>
                                                ) : (
                                                  <Tag color="default">-</Tag>
                                                ),
                                            },
                                            {
                                              title: "Fiziksel Özellik",
                                              dataIndex: "fizikselOzellik",
                                              key: "fizikselOzellik",
                                              width: 120,
                                              align: "center",
                                              render: (val: number) =>
                                                val ? (
                                                  <Tag
                                                    color={
                                                      val >= 80
                                                        ? "green"
                                                        : val >= 60
                                                        ? "orange"
                                                        : "red"
                                                    }
                                                  >
                                                    {val}/100
                                                  </Tag>
                                                ) : (
                                                  <Tag color="default">-</Tag>
                                                ),
                                            },
                                            {
                                              title: "Notlar",
                                              dataIndex: "notes",
                                              key: "notes",
                                              render: (val: string) => (
                                                <div className="max-w-xs">
                                                  {val ? (
                                                    <Tooltip
                                                      title={val}
                                                      placement="topLeft"
                                                    >
                                                      <span className="text-gray-600 text-sm">
                                                        {val.length > 50
                                                          ? `${val.substring(
                                                              0,
                                                              50
                                                            )}...`
                                                          : val}
                                                      </span>
                                                    </Tooltip>
                                                  ) : (
                                                    <span className="text-gray-400 text-sm">
                                                      Yorum yok
                                                    </span>
                                                  )}
                                                </div>
                                              ),
                                            },
                                            {
                                              title: "Tarih",
                                              dataIndex: "createdAt",
                                              key: "createdAt",
                                              width: 120,
                                              render: (val: string) => (
                                                <span className="text-xs text-gray-500">
                                                  {dayjs(val).format(
                                                    "DD/MM/YYYY HH:mm"
                                                  )}
                                                </span>
                                              ),
                                            },
                                          ]}
                                        />
                                      </div>
                                    </Card>
                                  );
                                }
                              )}

                              {/* Pagination Controls */}
                              <div className="flex justify-between items-center mt-8 pt-6 border-t">
                                <div className="flex items-center gap-4">
                                  <span className="text-sm text-gray-600">
                                    Sayfa başına göster:
                                  </span>
                                  <select
                                    value={ratingsPageSize}
                                    onChange={(e) =>
                                      handlePageSizeChange(
                                        Number(e.target.value)
                                      )
                                    }
                                    className="border rounded px-2 py-1 text-sm"
                                  >
                                    <option value={5}>5</option>
                                    <option value={10}>10</option>
                                    <option value={20}>20</option>
                                    <option value={50}>50</option>
                                  </select>
                                  <span className="text-sm text-gray-600">
                                    Toplam {allRatings.length} kullanıcıdan{" "}
                                    {Math.min(
                                      (ratingsCurrentPage - 1) *
                                        ratingsPageSize +
                                        1,
                                      allRatings.length
                                    )}
                                    -
                                    {Math.min(
                                      ratingsCurrentPage * ratingsPageSize,
                                      allRatings.length
                                    )}{" "}
                                    arası gösteriliyor
                                  </span>
                                </div>

                                <div className="flex items-center gap-2">
                                  <Button
                                    disabled={ratingsCurrentPage === 1}
                                    onClick={() => setRatingsCurrentPage(1)}
                                    size="small"
                                  >
                                    İlk
                                  </Button>
                                  <Button
                                    disabled={ratingsCurrentPage === 1}
                                    onClick={() =>
                                      setRatingsCurrentPage(
                                        ratingsCurrentPage - 1
                                      )
                                    }
                                    size="small"
                                  >
                                    Önceki
                                  </Button>

                                  <span className="mx-3 text-sm">
                                    Sayfa {ratingsCurrentPage} /{" "}
                                    {Math.ceil(
                                      allRatings.length / ratingsPageSize
                                    )}
                                  </span>

                                  <Button
                                    disabled={
                                      ratingsCurrentPage >=
                                      Math.ceil(
                                        allRatings.length / ratingsPageSize
                                      )
                                    }
                                    onClick={() =>
                                      setRatingsCurrentPage(
                                        ratingsCurrentPage + 1
                                      )
                                    }
                                    size="small"
                                  >
                                    Sonraki
                                  </Button>
                                  <Button
                                    disabled={
                                      ratingsCurrentPage >=
                                      Math.ceil(
                                        allRatings.length / ratingsPageSize
                                      )
                                    }
                                    onClick={() =>
                                      setRatingsCurrentPage(
                                        Math.ceil(
                                          allRatings.length / ratingsPageSize
                                        )
                                      )
                                    }
                                    size="small"
                                  >
                                    Son
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="text-center py-12">
                              <TrophyOutlined className="text-4xl text-gray-300 mb-4" />
                              <p className="text-gray-500 text-lg">
                                Henüz hiç puanlama yapılmamış.
                              </p>
                            </div>
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "age-statistics",
                      label: (
                        <span>
                          <BarChartOutlined />
                          Yaş İstatistikleri
                        </span>
                      ),
                      children: (
                        <div>
                          <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-semibold text-gray-800">
                              Yaş Aralığı İstatistikleri
                            </h2>
                            <div className="flex gap-2 items-center">
                              <span className="text-sm text-gray-600 min-w-[60px]">
                                Yaş Filtresi:
                              </span>
                              <Select
                                placeholder="Yaş seçin"
                                allowClear
                                style={{ width: 150 }}
                                value={ageFilter}
                                onChange={(value) => handleAgeFilter(value)}
                                options={
                                  ageStatistics?.ageGroups?.map((group: any) => ({
                                    value: group.age,
                                    label: `${group.age} yaş`,
                                  })) || []
                                }
                              />
                            </div>
                          </div>

                          {/* Age Statistics Overview */}
                          {ageStatistics && (
                            <>
                              {/* Main Statistics Cards */}
                              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                                <Card className="text-center border-l-4 border-l-[#021F4A] shadow-lg hover:shadow-xl transition-all duration-300">
                                  <div className="p-4">
                                    <div className="text-3xl text-[#021F4A] mb-2">
                                      <UserOutlined />
                                    </div>
                                    <Statistic
                                      title="Toplam Başvuru"
                                      value={
                                        getFilteredAgeStatistics()
                                          ?.totalApplications ||
                                        ageStatistics.totalApplications
                                      }
                                      valueStyle={{
                                        color: "#021F4A",
                                        fontSize: "2rem",
                                        fontWeight: "bold",
                                      }}
                                    />
                                  </div>
                                </Card>

                                {(
                                  getFilteredAgeStatistics()?.ageRanges ||
                                  ageStatistics.ageRanges
                                ).map((range: any, index: number) => (
                                  <Card
                                    key={index}
                                    className={`text-center border-l-4 shadow-lg hover:shadow-xl transition-all duration-300 ${
                                      index === 0
                                        ? "border-l-green-500"
                                        : index === 1
                                        ? "border-l-blue-500"
                                        : "border-l-orange-500"
                                    }`}
                                  >
                                    <div className="p-4">
                                      <div
                                        className={`text-3xl mb-2 ${
                                          index === 0
                                            ? "text-green-500"
                                            : index === 1
                                            ? "text-blue-500"
                                            : "text-orange-500"
                                        }`}
                                      >
                                        <TeamOutlined />
                                      </div>
                                      <Statistic
                                        title={range.range}
                                        value={range.count}
                                        valueStyle={{
                                          color:
                                            index === 0
                                              ? "#22c55e"
                                              : index === 1
                                              ? "#3b82f6"
                                              : "#f97316",
                                          fontSize: "2rem",
                                          fontWeight: "bold",
                                        }}
                                      />
                                    </div>
                                  </Card>
                                ))}
                              </div>

                              {/* Detailed Age Group Breakdown */}
                              <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                                  Detaylı Yaş Grubu Analizi
                                </h3>

                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                  {(
                                    getFilteredAgeStatistics()?.ageGroups ||
                                    ageStatistics.ageGroups
                                  ).map((group: any, index: number) => (
                                    <div
                                      key={index}
                                      className="bg-gradient-to-br from-gray-50 to-white p-4 rounded-lg border"
                                    >
                                      <div className="flex items-center justify-between mb-3">
                                        <h4 className="text-md font-medium text-gray-700">
                                          {group.age} yaş ({group.birthYear}{" "}
                                          doğumlu)
                                        </h4>
                                        <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2 py-1 rounded">
                                          {group.count} kişi
                                        </span>
                                      </div>

                                      <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                          <span className="text-gray-600">
                                            Seçim Yapılmadı:
                                          </span>
                                          <span className="font-medium text-gray-800">
                                            {group.statusBreakdown.notDecided}
                                          </span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                          <span className="text-orange-600">
                                            Katılmadı:
                                          </span>
                                          <span className="font-medium text-orange-800">
                                            {
                                              group.statusBreakdown
                                                .notParticipated
                                            }
                                          </span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                          <span className="text-green-600">
                                            Seçildi:
                                          </span>
                                          <span className="font-medium text-green-800">
                                            {group.statusBreakdown.selected}
                                          </span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                          <span className="text-red-600">
                                            Seçilmedi:
                                          </span>
                                          <span className="font-medium text-red-800">
                                            {group.statusBreakdown.notSelected}
                                          </span>
                                        </div>
                                      </div>

                                      {/* Progress bar for selection rate */}
                                      {group.count > 0 && (
                                        <div className="mt-3">
                                          <div className="flex justify-between text-xs mb-1">
                                            <span>Seçilme Oranı</span>
                                            <span>
                                              {(
                                                (group.statusBreakdown
                                                  .selected /
                                                  group.count) *
                                                100
                                              ).toFixed(1)}
                                              %
                                            </span>
                                          </div>
                                          <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div
                                              className="bg-green-500 h-2 rounded-full"
                                              style={{
                                                width: `${
                                                  (group.statusBreakdown
                                                    .selected /
                                                    group.count) *
                                                  100
                                                }%`,
                                              }}
                                            ></div>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>

                              {/* Summary insights */}
                              <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                <h3 className="text-lg font-semibold text-blue-800 mb-2 flex items-center gap-2">
                                  <InfoCircleOutlined />
                                  Özet Bilgiler
                                </h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                  <div>
                                    <p className="text-blue-700">
                                      • En fazla başvuru:{" "}
                                      {
                                        (
                                          getFilteredAgeStatistics()
                                            ?.ageGroups ||
                                          ageStatistics.ageGroups
                                        ).reduce(
                                          (max: any, group: any) =>
                                            group.count > max.count
                                              ? group
                                              : max,
                                          { count: 0, age: 0 }
                                        ).age
                                      }{" "}
                                      yaş grubundan (
                                      {
                                        (
                                          getFilteredAgeStatistics()
                                            ?.ageGroups ||
                                          ageStatistics.ageGroups
                                        ).reduce(
                                          (max: any, group: any) =>
                                            group.count > max.count
                                              ? group
                                              : max,
                                          { count: 0 }
                                        ).count
                                      }{" "}
                                      başvuru)
                                    </p>
                                    <p className="text-blue-700">
                                      • En az başvuru:{" "}
                                      {
                                        (
                                          getFilteredAgeStatistics()
                                            ?.ageGroups ||
                                          ageStatistics.ageGroups
                                        ).reduce(
                                          (min: any, group: any) =>
                                            group.count < min.count
                                              ? group
                                              : min,
                                          { count: Infinity, age: 0 }
                                        ).age
                                      }{" "}
                                      yaş grubundan (
                                      {
                                        (
                                          getFilteredAgeStatistics()
                                            ?.ageGroups ||
                                          ageStatistics.ageGroups
                                        ).reduce(
                                          (min: any, group: any) =>
                                            group.count < min.count
                                              ? group
                                              : min,
                                          { count: Infinity }
                                        ).count
                                      }{" "}
                                      başvuru)
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-blue-700">
                                      • Toplam seçilen:{" "}
                                      {(
                                        getFilteredAgeStatistics()?.ageGroups ||
                                        ageStatistics.ageGroups
                                      ).reduce(
                                        (sum: number, group: any) =>
                                          sum + group.statusBreakdown.selected,
                                        0
                                      )}{" "}
                                      kişi
                                    </p>
                                    <p className="text-blue-700">
                                      • Genel seçilme oranı:{" "}
                                      {(
                                        ((
                                          getFilteredAgeStatistics()
                                            ?.ageGroups ||
                                          ageStatistics.ageGroups
                                        ).reduce(
                                          (sum: number, group: any) =>
                                            sum +
                                            group.statusBreakdown.selected,
                                          0
                                        ) /
                                          (getFilteredAgeStatistics()
                                            ?.totalApplications ||
                                            ageStatistics.totalApplications)) *
                                        100
                                      ).toFixed(1)}
                                      %
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </>
                          )}

                          {/* Loading state */}
                          {!ageStatistics && (
                            <div className="flex justify-center py-12">
                              <div className="text-center">
                                <Spin size="large" />
                                <p className="mt-4 text-gray-600">
                                  Yaş istatistikleri yükleniyor...
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "moderators",
                      label: (
                        <span>
                          <TeamOutlined />
                          Moderator Yönetimi
                        </span>
                      ),
                      children: (
                        <div>
                          <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-semibold text-gray-800">
                              Moderator Listesi
                            </h2>
                            <Button
                              type="primary"
                              icon={<PlusOutlined />}
                              onClick={() => setIsModeratorModalVisible(true)}
                              className="bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                            >
                              Yeni Moderator Ekle
                            </Button>
                          </div>

                          {moderatorLoading ? (
                            <div className="flex justify-center py-8">
                              <Spin size="large" />
                            </div>
                          ) : (
                            <Table
                              dataSource={moderators}
                              columns={moderatorColumns}
                              rowKey="id"
                              pagination={{ pageSize: 10 }}
                              bordered
                            />
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "export",
                      label: (
                        <span>
                          <DownloadOutlined />
                          Excel İndir
                        </span>
                      ),
                      children: (
                        <div className="min-h-[600px] bg-gradient-to-br from-blue-50 via-white to-yellow-50">
                          {/* Header Section */}

                          {/* Main Content */}
                          <div className="p-8">
                            <div className="max-w-4xl mx-auto">
                              {/* Stats Cards */}
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                                <Card className="text-center border-l-4 border-l-[#021F4A] shadow-lg hover:shadow-xl transition-all duration-300">
                                  <div className="p-4">
                                    <div className="text-3xl text-[#021F4A] mb-2">
                                      <TeamOutlined />
                                    </div>
                                    <Statistic
                                      title="Toplam Başvuru"
                                      value={applications.length}
                                      valueStyle={{
                                        color: "#021F4A",
                                        fontSize: "2rem",
                                        fontWeight: "bold",
                                      }}
                                    />
                                  </div>
                                </Card>

                                <Card className="text-center border-l-4 border-l-[#FFE500] shadow-lg hover:shadow-xl transition-all duration-300">
                                  <div className="p-4">
                                    <div className="text-3xl text-[#FFE500] mb-2">
                                      <TrophyOutlined />
                                    </div>
                                    <Statistic
                                      title="Takıma Seçilen"
                                      value={
                                        applications.filter(
                                          (app) => app.status === 1
                                        ).length
                                      }
                                      valueStyle={{
                                        color: "#FFE500",
                                        fontSize: "2rem",
                                        fontWeight: "bold",
                                      }}
                                    />
                                  </div>
                                </Card>

                                <Card className="text-center border-l-4 border-l-green-500 shadow-lg hover:shadow-xl transition-all duration-300">
                                  <div className="p-4">
                                    <div className="text-3xl text-green-500 mb-2">
                                      <StarOutlined />
                                    </div>
                                    <Statistic
                                      title="Puanlanan"
                                      value={
                                        allRatings.filter(
                                          (r) => r.ratingCount > 0
                                        ).length
                                      }
                                      valueStyle={{
                                        color: "#22c55e",
                                        fontSize: "2rem",
                                        fontWeight: "bold",
                                      }}
                                    />
                                  </div>
                                </Card>
                              </div>

                              {/* Main Export Section */}
                              <div className="bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100">
                                <div className="bg-gradient-to-r from-[#021F4A] to-[#0A4392] p-6">
                                  <div className="flex items-center justify-center gap-3 text-white ">
                                    <DownloadOutlined className="text-2xl" />
                                    <h2 className="text-xl font-bold !mt-1">
                                      Excel Raporu İndir
                                    </h2>
                                  </div>
                                </div>

                                <div className="p-8">
                                  {/* Download Button */}
                                  <div className="text-center">
                                    <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 rounded-xl mb-6 border border-gray-200">
                                      <Button
                                        type="primary"
                                        size="large"
                                        icon={<DownloadOutlined />}
                                        onClick={handleExportToExcel}
                                        className="bg-gradient-to-r from-[#021F4A] via-[#0A4392] to-[#021F4A] border-0 !h-16 !px-16 !py-5 text-lg font-bold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
                                        style={{
                                          borderRadius: "12px",
                                          background:
                                            "linear-gradient(135deg, #021F4A 0%, #0A4392 50%, #021F4A 100%)",
                                        }}
                                      >
                                        Excel Dosyasını İndir
                                      </Button>

                                      <div className="mt-4 flex items-center justify-center gap-6 text-sm text-gray-500">
                                        <div className="flex items-center gap-1">
                                          <svg
                                            className="w-4 h-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                          >
                                            <path
                                              fillRule="evenodd"
                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                              clipRule="evenodd"
                                            />
                                          </svg>
                                          <span>Güvenli İndirme</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                          <svg
                                            className="w-4 h-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                          >
                                            <path
                                              fillRule="evenodd"
                                              d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 111.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z"
                                              clipRule="evenodd"
                                            />
                                          </svg>
                                          <span>Excel Format</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                          <svg
                                            className="w-4 h-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                          >
                                            <path
                                              fillRule="evenodd"
                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                              clipRule="evenodd"
                                            />
                                          </svg>
                                          <span>Anlık Veri</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ),
                    },
                  ]
                : []),
              // Moderator tab - Kendi puanlarını görüntüleme
              ...(isModerator()
                ? [
                    {
                      key: "my-ratings",
                      label: (
                        <span>
                          <UserOutlined />
                          Benim Puanlarım
                        </span>
                      ),
                      children: (
                        <div>
                          <h2 className="text-xl font-semibold text-gray-800 mb-6">
                            Verdiğim Puanlar
                          </h2>
                          {myRatings.length > 0 ? (
                            <Table
                              dataSource={myRatings}
                              rowKey="id"
                              pagination={{ pageSize: 10 }}
                              columns={[
                                {
                                  title: "Kullanıcı ID",
                                  dataIndex: "fullName",
                                  key: "fullName",
                                },
                                // {
                                //   title: "Kullanıcı ID",
                                //   dataIndex: "userId",
                                //   key: "userId",
                                // },
                                {
                                  title: "Topla Beceri",
                                  dataIndex: "toplaBeceri",
                                  key: "toplaBeceri",
                                  render: (val: number) =>
                                    val ? `${val}/100` : "-",
                                },
                                {
                                  title: "Fiziksel Özellik",
                                  dataIndex: "fizikselOzellik",
                                  key: "fizikselOzellik",
                                  render: (val: number) =>
                                    val ? `${val}/100` : "-",
                                },
                                {
                                  title: "Notlar",
                                  dataIndex: "notes",
                                  key: "notes",
                                  render: (val: string) => val || "-",
                                },
                                {
                                  title: "Tarih",
                                  dataIndex: "createdAt",
                                  key: "createdAt",
                                  render: (val: string) =>
                                    dayjs(val).format("DD/MM/YYYY HH:mm"),
                                },
                              ]}
                            />
                          ) : (
                            <div className="text-center py-8 text-gray-500">
                              Henüz hiç puanlama yapmadınız.
                            </div>
                          )}
                        </div>
                      ),
                    },
                  ]
                : []),
            ]}
          />
        </div>
      </main>

      {/* Edit/View Modal */}
      <Modal
        title={
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-[#021F4A] to-[#0A4392] text-white p-2 rounded-full">
              <UserOutlined className="text-lg" />
            </div>
            <div>
              <h3 className="text-lg font-semibold m-0">
                {isViewMode ? "Başvuru Detayları" : "Başvuru Düzenle"}
              </h3>
              {isViewMode && currentUser && (
                <p className="text-sm text-gray-500 m-0">
                  Takip Kodu: {currentUser.trackingCode}
                </p>
              )}
            </div>
          </div>
        }
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={
          isViewMode
            ? [
                <Button key="back" onClick={() => setIsModalVisible(false)}>
                  Kapat
                </Button>,
              ]
            : [
                <Button key="back" onClick={() => setIsModalVisible(false)}>
                  İptal
                </Button>,
                <Button
                  key="submit"
                  type="primary"
                  onClick={() => form.submit()}
                  loading={loading}
                  className="bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
                >
                  Güncelle
                </Button>,
              ]
        }
        width={900}
        style={{ maxHeight: "80vh", overflow: "auto" }}
      >
        {!isViewMode && backendErrors.length > 0 && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <h4 className="text-red-700 font-medium mb-2">
              Aşağıdaki hatalar oluştu:
            </h4>
            <ul className="list-disc pl-5">
              {backendErrors.map((error, index) => (
                <li key={index} className="text-red-600 text-sm">
                  {error}
                </li>
              ))}
            </ul>
          </div>
        )}

        {isViewMode ? (
          // View Mode - Card-based elegant design
          <div className="space-y-6">
            {/* Status Badge */}
            {currentUser && (
              <div className="flex justify-center mb-6">
                <Tag
                  color={getStatusColor(currentUser.status)}
                  style={{
                    fontSize: "14px",
                    padding: "6px 16px",
                    borderRadius: "20px",
                  }}
                >
                  {getStatusText(currentUser.status)}
                </Tag>
              </div>
            )}

            {/* Personal Information Card */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <UserOutlined className="text-blue-600" />
                  <span>Kişisel Bilgiler</span>
                </div>
              }
              className="shadow-sm"
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Ad Soyad
                    </label>
                    <p className="text-base font-medium">
                      {currentUser?.fullName}
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Doğum Tarihi
                    </label>
                    <p className="text-base">
                      {currentUser?.dateOfBirth
                        ? dayjs(currentUser.dateOfBirth).format("DD/MM/YYYY")
                        : "-"}
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      E-posta
                    </label>
                    <p className="text-base">{currentUser?.email || "-"}</p>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Telefon
                    </label>
                    <p className="text-base">{currentUser?.phoneNumber}</p>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      İl
                    </label>
                    <p className="text-base">{currentUser?.province || "-"}</p>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      İlçe
                    </label>
                    <p className="text-base">{currentUser?.district || "-"}</p>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* Physical Measurements Card */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <TeamOutlined className="text-green-600" />
                  <span>Fiziksel Ölçüler</span>
                </div>
              }
              className="shadow-sm !mt-5"
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Boy
                    </label>
                    <p className="text-base font-medium">
                      {currentUser?.height} cm
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Kilo
                    </label>
                    <p className="text-base font-medium">
                      {currentUser?.weight} kg
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Kulaç Boyu
                    </label>
                    <p className="text-base">
                      {currentUser?.armSpan ? `${currentUser.armSpan} cm` : "-"}
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Karış Ölçüsü
                    </label>
                    <p className="text-base">
                      {currentUser?.palmSize
                        ? `${currentUser.palmSize} cm`
                        : "-"}
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Ayak Numarası
                    </label>
                    <p className="text-base">{currentUser?.shoeSize}</p>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Sıçrama Yüksekliği
                    </label>
                    <p className="text-base">
                      {currentUser?.jumpHeight
                        ? `${currentUser.jumpHeight} cm`
                        : "-"}
                    </p>
                  </div>
                </Col>
                {currentUser?.jumpRecordDate && (
                  <Col xs={24} sm={12}>
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-500">
                        Son Sıçrama Tarihi
                      </label>
                      <p className="text-base">
                        {dayjs(currentUser.jumpRecordDate).format("DD/MM/YYYY")}
                      </p>
                    </div>
                  </Col>
                )}
              </Row>
            </Card>

            {/* Sports History Card */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <TrophyOutlined className="text-orange-600" />
                  <span>Spor Geçmişi</span>
                </div>
              }
              className="shadow-sm !mt-5"
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Kulüp Tecrübesi
                    </label>
                    <p className="text-base">
                      <Tag
                        color={
                          currentUser?.hasPlayedForClub ? "green" : "orange"
                        }
                      >
                        {currentUser?.hasPlayedForClub ? "Var" : "Yok"}
                      </Tag>
                    </p>
                  </div>
                </Col>
                {currentUser?.hasPlayedForClub && (
                  <Col xs={24} sm={12}>
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-500">
                        Kulüp Adı
                      </label>
                      <p className="text-base">
                        {currentUser?.clubName || "-"}
                      </p>
                    </div>
                  </Col>
                )}
              </Row>
            </Card>

            {/* Parent Information Card */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <UserOutlined className="text-purple-600" />
                  <span>Veli Bilgileri</span>
                </div>
              }
              className="shadow-sm !mt-5"
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Veli Adı Soyadı
                    </label>
                    <p className="text-base font-medium">
                      {currentParent?.fullName}
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Veli Telefon
                    </label>
                    <p className="text-base">{currentParent?.mobilePhone}</p>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Yakınlık Derecesi
                    </label>
                    <p className="text-base">
                      {currentParent?.relationshipType}
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Anne Boyu
                    </label>
                    <p className="text-base">
                      {currentParent?.motherHeight
                        ? `${currentParent.motherHeight} cm`
                        : "-"}
                    </p>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Baba Boyu
                    </label>
                    <p className="text-base">
                      {currentParent?.fatherHeight
                        ? `${currentParent.fatherHeight} cm`
                        : "-"}
                    </p>
                  </div>
                </Col>
                <Col xs={24}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Veli Onayı
                    </label>
                    <p className="text-base">
                      <Tag
                        color={currentParent?.approvalGiven ? "green" : "red"}
                      >
                        {currentParent?.approvalGiven ? "Verildi" : "Verilmedi"}
                      </Tag>
                    </p>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* Health Information Card */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <InfoCircleOutlined className="text-red-600" />
                  <span>Sağlık Bilgileri</span>
                </div>
              }
              className="shadow-sm !mt-5"
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-500">
                      Sağlık Sorunu
                    </label>
                    <p className="text-base">
                      <Tag
                        color={currentUser?.hasHealthIssues ? "red" : "green"}
                      >
                        {currentUser?.hasHealthIssues ? "Var" : "Yok"}
                      </Tag>
                    </p>
                  </div>
                </Col>
                {currentUser?.hasHealthIssues && (
                  <Col xs={24}>
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-500">
                        Sağlık Sorunu Detayları
                      </label>
                      <div className="bg-gray-50 p-3 rounded-md">
                        <p className="text-base">
                          {currentUser?.healthIssues || "-"}
                        </p>
                      </div>
                    </div>
                  </Col>
                )}
              </Row>
            </Card>
          </div>
        ) : (
          // Edit Mode - Keep existing form layout
          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdate}
            disabled={isViewMode}
          >
            <Divider orientation="left">Kişisel Bilgiler</Divider>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Ad Soyad */}
              <Form.Item
                name="fullName"
                label="Adı Soyadı"
                rules={[{ required: true, message: "Lütfen adı soyadı girin" }]}
              >
                <Input
                  placeholder="Adınız ve soyadınız"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Doğum Tarihi */}
              <Form.Item
                name="dateOfBirth"
                label="Doğum Tarihi"
                rules={[
                  { required: true, message: "Lütfen doğum tarihi seçin" },
                ]}
              >
                <DatePicker
                  style={
                    isViewMode
                      ? { width: "100%", color: "#000" }
                      : { width: "100%" }
                  }
                  format="DD/MM/YYYY"
                />
              </Form.Item>
              {/* E-posta */}
              <Form.Item
                name="email"
                label="E-posta"
                rules={[
                  { required: true, message: "Lütfen e-posta adresi girin" },
                ]}
              >
                <Input
                  type="email"
                  placeholder="E-posta adresiniz"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Telefon */}
              <Form.Item
                name="phoneNumber"
                label="Telefon"
                rules={[
                  { required: true, message: "Lütfen telefon numarası girin" },
                ]}
              >
                <Input
                  placeholder="Telefon numaranız"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* İl */}
              <Form.Item name="province" label="İl">
                <Input
                  placeholder="İl"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* İlçe */}
              <Form.Item name="district" label="İlçe">
                <Input
                  placeholder="İlçe"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
            </div>

            <Divider orientation="left">Fiziksel Ölçüler</Divider>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Boy */}
              <Form.Item name="height" label="Boy (cm)">
                <Input
                  type="number"
                  placeholder="Boy (cm)"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Kilo */}
              <Form.Item name="weight" label="Kilo (kg)">
                <Input
                  type="number"
                  placeholder="Kilo (kg)"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Kulaç Boyu */}
              <Form.Item name="armSpan" label="Kulaç Boyu (cm)">
                <Input
                  type="number"
                  placeholder="Kulaç boyu (cm)"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Avuç İçi Ölçüsü */}
              <Form.Item name="palmSize" label="Karış Ölçüsü (cm)">
                <Input
                  type="number"
                  placeholder="Karış ölçüsü (cm)"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Ayak Numarası */}
              <Form.Item name="shoeSize" label="Ayak Numarası" rules={[]}>
                <Input
                  type="number"
                  placeholder="Ayak numarası"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Sıçrama Yüksekliği */}
              <Form.Item name="jumpHeight" label="Sıçrama Yüksekliği (cm)">
                <Input
                  type="number"
                  placeholder="Sıçrama yüksekliği (cm)"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Sıçrama Tarihi */}
              <Form.Item name="jumpRecordDate" label="Son Sıçrama Tarihi">
                <DatePicker
                  style={
                    isViewMode
                      ? { width: "100%", color: "#000" }
                      : { width: "100%" }
                  }
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </div>

            <Divider orientation="left">Spor Geçmişi</Divider>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Kulüp Adı */}
              <Form.Item
                name="hasPlayedForClub"
                label="Kulüp Tecrübesi"
                valuePropName="checked"
              >
                <Checkbox>Var</Checkbox>
              </Form.Item>
              <Form.Item name="clubName" label="Kulüp Adı">
                <Input
                  placeholder="Kulüp adı"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
            </div>

            <Divider orientation="left">Veli Bilgileri</Divider>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Veli Adı Soyadı */}
              <Form.Item
                name="parentFullName"
                label="Veli Adı Soyadı"
                rules={[
                  { required: true, message: "Lütfen veli adı soyadı girin" },
                ]}
              >
                <Input
                  placeholder="Veli adı soyadı"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Veli Telefon */}
              <Form.Item
                name="parentPhone"
                label="Veli Telefon"
                rules={[
                  {
                    required: true,
                    message: "Lütfen veli telefon numarası girin",
                  },
                ]}
              >
                <Input
                  placeholder="Veli telefon numarası"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Anne Boyu */}
              <Form.Item
                name="motherHeight"
                label="Anne Boyu (cm)"
                rules={[{ required: true, message: "Lütfen anne boyu girin" }]}
                className="md:col-span-1"
              >
                <Input
                  type="number"
                  placeholder="Anne boyu (cm)"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Baba Boyu */}
              <Form.Item
                name="fatherHeight"
                label="Baba Boyu (cm)"
                rules={[{ required: true, message: "Lütfen baba boyu girin" }]}
                className="md:col-span-1"
              >
                <Input
                  type="number"
                  placeholder="Baba boyu (cm)"
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
              {/* Veli Yakınlık Derecesi */}
              <Form.Item
                name="parentRelationshipType"
                label="Veli Yakınlık Derecesi"
                rules={[
                  {
                    required: true,
                    message: "Lütfen veli yakınlık derecesi seçin",
                  },
                ]}
              >
                {isViewMode ? (
                  <Input
                    value={currentParent?.relationshipType || ""}
                    readOnly
                    style={{ color: "#000" }}
                  />
                ) : (
                  <select
                    className="w-full border rounded-md h-10 px-3 py-2"
                    onChange={handleRelationshipTypeChange}
                  >
                    <option value="">Seçiniz</option>
                    <option value="Anne">Anne</option>
                    <option value="Baba">Baba</option>
                    <option value="Diğer">Diğer</option>
                  </select>
                )}
              </Form.Item>

              {/* Custom Relationship Type - Only show in edit mode */}

              <Form.Item
                name="customRelationshipType"
                label="Diğer Yakınlık Derecesi"
                className="custom-relationship-field"
                style={{
                  display:
                    form.getFieldValue("parentRelationshipType") === "Diğer"
                      ? "block"
                      : "none",
                }}
              >
                <Input placeholder="Yakınlık derecesini belirtin" />
              </Form.Item>

              {/* Veli Onayı */}
              <Form.Item
                name="approvalGiven"
                label="Veli Onayı"
                valuePropName="checked"
                className="md:col-span-2"
              >
                <Checkbox>Onay verildi</Checkbox>
              </Form.Item>
            </div>

            <Divider orientation="left">Sağlık Bilgileri</Divider>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Sağlık Sorunu */}
              <Form.Item
                name="hasHealthIssues"
                label="Sağlık Sorunu"
                valuePropName="checked"
              >
                <Checkbox>Var</Checkbox>
              </Form.Item>
              {/* Sağlık Sorunu Detayları */}
              <Form.Item name="healthIssues" label="Sağlık Sorunu Detayları">
                <Input.TextArea
                  rows={2}
                  placeholder=""
                  style={isViewMode ? { color: "#000" } : {}}
                />
              </Form.Item>
            </div>

            <Divider orientation="left">Başvuru Durumu</Divider>
            <div className="grid grid-cols-1 gap-4">
              <Form.Item name="status" label="Durum">
                <select
                  className="w-full border rounded-md h-10 px-3 py-2"
                  disabled={isViewMode}
                >
                  <option value="-1">Seçim Yapılmadı</option>
                  <option value="0">Seçmeye Katılmadı</option>
                  <option value="1">Takıma Seçildi</option>
                  <option value="2">Takıma Seçilmedi</option>
                </select>
              </Form.Item>
            </div>
          </Form>
        )}
      </Modal>

      {/* QR Code Modal */}
      <Modal
        open={isQRCodeModalVisible && currentUser !== null}
        onCancel={() => setIsQRCodeModalVisible(false)}
        footer={[]}
        centered
        width={400}
      >
        {currentUser && (
          <div className="py-4 flex flex-col items-center">
            <div className="bg-gradient-to-b from-[#021F4A] to-[#0A4392] p-4 rounded-lg shadow-md mb-4">
              <div className="bg-white p-3 rounded-md flex justify-center items-center">
                <QRCodeSVG
                  value={currentUser.id?.toString() || ""}
                  size={200}
                  bgColor={"#FFFFFF"}
                  fgColor={"#021F4A"}
                  level={"H"}
                  includeMargin={false}
                />
              </div>
              <div className="text-center mt-3 pb-1">
                <p className="text-white text-xs">
                  Kullanıcı ID: {currentUser.id}
                </p>
                <p className="text-white text-sm font-semibold mt-1">
                  {currentUser.fullName}
                </p>
                <p className="text-white text-xs">{currentUser.trackingCode}</p>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        title="Başvuruyu Sil"
        open={isDeleteModalVisible}
        onCancel={() => setIsDeleteModalVisible(false)}
        footer={[
          <Button key="back" onClick={() => setIsDeleteModalVisible(false)}>
            İptal
          </Button>,
          <Button
            key="submit"
            type="primary"
            danger
            onClick={handleDelete}
            loading={loading}
          >
            Sil
          </Button>,
        ]}
      >
        <p>Bu başvuruyu silmek istediğinizden emin misiniz?</p>
        {currentUser && (
          <p className="font-semibold mt-2">
            {currentUser.fullName} - {currentUser.trackingCode}
          </p>
        )}
      </Modal>

      {/* Add New Modal */}
      <Modal
        title="Yeni Başvuru Ekle"
        open={isAddModalVisible}
        onCancel={() => {
          setIsAddModalVisible(false);
          zodForm.reset();
        }}
        footer={[
          <Button
            key="back"
            onClick={() => {
              setIsAddModalVisible(false);
              zodForm.reset();
            }}
          >
            İptal
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => zodForm.handleSubmit(handleZodCreate)()}
            loading={loading}
            // disabled={!zodForm.formState.isValid || loading}
            className="bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
          >
            Ekle
          </Button>,
        ]}
        width={800}
      >
        <div>
          {/* Show backend errors */}
          {backendErrors.length > 0 && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <h4 className="text-red-700 font-medium mb-2">
                Aşağıdaki hatalar oluştu:
              </h4>
              <ul className="list-disc pl-5">
                {backendErrors.map((error, index) => (
                  <li key={index} className="text-red-600 text-sm">
                    {error}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* KİŞİSEL BİLGİLER */}
          <div className="space-y-4 pt-2 pb-2">
            <h3 className="text-lg font-semibold text-[#021F4A]">
              KİŞİSEL BİLGİLER
            </h3>
            <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Adı Soyadı
              </label>
              <input
                type="text"
                placeholder="Adınız ve soyadınız"
                className={`w-full border ${
                  zodForm.formState.errors.fullName
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("fullName")}
              />
              {zodForm.formState.errors.fullName && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.fullName.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Doğum Tarihi
              </label>
              <DatePicker
                style={{ width: "100%" }}
                format="DD/MM/YYYY"
                className={`border ${
                  zodForm.formState.errors.birthDate
                    ? "border-red-500"
                    : "border-gray-300"
                }`}
                placeholder="Doğum tarihi seçin"
                onChange={(date) => {
                  if (date) {
                    zodForm.setValue("birthDate", date.toDate(), {
                      shouldValidate: true,
                    });
                  }
                }}
                // Set default date to today if needed to avoid validation error
                defaultValue={dayjs(new Date())}
              />
              {zodForm.formState.errors.birthDate && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.birthDate.message as string}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                E-posta
              </label>
              <input
                type="email"
                placeholder="E-posta adresiniz"
                className={`w-full border ${
                  zodForm.formState.errors.email
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("email")}
              />
              {zodForm.formState.errors.email && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.email.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Telefon
              </label>
              <input
                type="text"
                placeholder="Telefon numaranız"
                className={`w-full border ${
                  zodForm.formState.errors.phoneNumber
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("phoneNumber")}
              />
              {zodForm.formState.errors.phoneNumber && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.phoneNumber.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                İl
              </label>
              <input
                type="text"
                placeholder="İl"
                className={`w-full border ${
                  zodForm.formState.errors.province
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("province")}
              />
              {zodForm.formState.errors.province && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.province.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                İlçe
              </label>
              <input
                type="text"
                placeholder="İlçe"
                className={`w-full border ${
                  zodForm.formState.errors.district
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("district")}
              />
              {zodForm.formState.errors.district && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.district.message}
                </p>
              )}
            </div>
          </div>

          {/* FİZİKSEL ÖLÇÜLER */}
          <div className="space-y-4 pt-2 pb-2">
            <h3 className="text-lg font-semibold text-[#021F4A]">
              FİZİKSEL ÖLÇÜLER
            </h3>
            <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Boy (cm)
              </label>
              <input
                type="number"
                placeholder="Boy (cm)"
                className={`w-full border ${
                  zodForm.formState.errors.height
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("height")}
              />
              {zodForm.formState.errors.height && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.height.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Kilo (kg)
              </label>
              <input
                type="number"
                placeholder="Kilo (kg)"
                className={`w-full border ${
                  zodForm.formState.errors.weight
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("weight")}
              />
              {zodForm.formState.errors.weight && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.weight.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                Kulaç Boyu (cm)
                <Tooltip
                  title={
                    <div>
                      <Image
                        src="/kulac.png"
                        alt="Kulaç Açıklama"
                        width={400}
                        height={200}
                      />
                    </div>
                  }
                  placement="right"
                  overlayStyle={{ maxWidth: 420 }}
                >
                  <InfoCircleOutlined className="text-blue-500 cursor-pointer" />
                </Tooltip>
              </label>
              <input
                type="number"
                placeholder="Kulaç boyu (cm)"
                className={`w-full border ${
                  zodForm.formState.errors.armSpan
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("armSpan")}
              />
              {zodForm.formState.errors.armSpan && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.armSpan.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                Karış Ölçüsü (cm)
                {/* <Tooltip
                  title={
                    <div>
                      <Image
                        src="/avucici.png"
                        alt="Kulaç Açıklama"
                        width={400}
                        height={200}
                      />
                    </div>
                  }
                  placement="right"
                  overlayStyle={{ maxWidth: 420 }}
                >
                  <InfoCircleOutlined className="text-blue-500 cursor-pointer" />
                </Tooltip> */}
              </label>
              <input
                type="number"
                placeholder="Karış ölçüsü (cm)"
                className={`w-full border ${
                  zodForm.formState.errors.palmSize
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("palmSize")}
              />
              {zodForm.formState.errors.palmSize && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.palmSize.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ayak Numarası
              </label>
              <input
                type="number"
                placeholder="Ayak numarası"
                className={`w-full border ${
                  zodForm.formState.errors.shoeSize
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("shoeSize")}
              />
              {zodForm.formState.errors.shoeSize && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.shoeSize.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sıçrama Yüksekliği (cm)
              </label>
              <input
                type="number"
                placeholder="Sıçrama yüksekliği (cm)"
                className={`w-full border ${
                  zodForm.formState.errors.jumpHeight
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("jumpHeight")}
              />
              {zodForm.formState.errors.jumpHeight && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.jumpHeight.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Son Sıçrama Tarihi
              </label>
              <DatePicker
                style={{ width: "100%" }}
                format="DD/MM/YYYY"
                onChange={(date) =>
                  zodForm.setValue(
                    "jumpRecordDate",
                    date?.toDate() || undefined
                  )
                }
              />
              {zodForm.formState.errors.jumpRecordDate && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.jumpRecordDate.message}
                </p>
              )}
            </div>
          </div>

          {/* SPOR GEÇMİŞİ */}
          <div className="space-y-4 pt-2 pb-2">
            <h3 className="text-lg font-semibold text-[#021F4A]">
              SPOR GEÇMİŞİ
            </h3>
            <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
          </div>

          <div className="mb-6">
            <div className="flex items-center h-10  mb-3">
              <input
                type="checkbox"
                id="hasPlayedForClub"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                {...zodForm.register("hasPlayedForClub")}
              />
              <label
                htmlFor="hasPlayedForClub"
                className="ml-2 block text-sm text-gray-700"
              >
                Kulüp tecrübesi var
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Kulüp Adı
              </label>
              <input
                type="text"
                placeholder="Kulüp adı"
                disabled={!zodForm.watch("hasPlayedForClub")}
                className={`w-full border ${
                  zodForm.formState.errors.clubName
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  !zodForm.watch("hasPlayedForClub")
                    ? "bg-gray-100 text-gray-500"
                    : ""
                }`}
                {...zodForm.register("clubName")}
              />
              {zodForm.formState.errors.clubName && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.clubName.message}
                </p>
              )}
            </div>
          </div>

          {/* VELİ BİLGİLERİ */}
          <div className="space-y-4 pt-2 pb-2">
            <h3 className="text-lg font-semibold text-[#021F4A]">
              VELİ BİLGİLERİ
            </h3>
            <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Veli Adı Soyadı
              </label>
              <input
                type="text"
                placeholder="Veli adı soyadı"
                className={`w-full border ${
                  zodForm.formState.errors.parentFullName
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("parentFullName")}
              />
              {zodForm.formState.errors.parentFullName && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.parentFullName.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Veli Telefon
              </label>
              <input
                type="text"
                placeholder="Veli telefon numarası"
                className={`w-full border ${
                  zodForm.formState.errors.parentPhone
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("parentPhone")}
              />
              {zodForm.formState.errors.parentPhone && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.parentPhone.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Anne Boyu (cm)
              </label>
              <input
                type="number"
                placeholder="Anne boyu (cm)"
                className={`w-full border ${
                  zodForm.formState.errors.motherHeight
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("motherHeight")}
              />
              {zodForm.formState.errors.motherHeight && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.motherHeight.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Baba Boyu (cm)
              </label>
              <input
                type="number"
                placeholder="Baba boyu (cm)"
                className={`w-full border ${
                  zodForm.formState.errors.fatherHeight
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("fatherHeight")}
              />
              {zodForm.formState.errors.fatherHeight && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.fatherHeight.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Veli Yakınlık Derecesi
              </label>
              <select
                className={`w-full border ${
                  zodForm.formState.errors.parentRelationshipType
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                {...zodForm.register("parentRelationshipType")}
              >
                <option value="">Seçiniz</option>
                <option value="Anne">Anne</option>
                <option value="Baba">Baba</option>
                <option value="Diğer">Diğer</option>
              </select>
              {zodForm.formState.errors.parentRelationshipType && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.parentRelationshipType.message}
                </p>
              )}
            </div>

            <div
              className={
                zodForm.watch("parentRelationshipType") === "Diğer"
                  ? ""
                  : "invisible"
              }
            >
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Diğer Yakınlık Derecesi
              </label>
              <input
                type="text"
                placeholder="Yakınlık derecesini belirtin"
                className={`w-full border ${
                  zodForm.formState.errors.customRelationshipType
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                disabled={zodForm.watch("parentRelationshipType") !== "Diğer"}
                {...zodForm.register("customRelationshipType")}
              />
              {zodForm.formState.errors.customRelationshipType &&
                zodForm.watch("parentRelationshipType") === "Diğer" && (
                  <p className="mt-1 text-sm text-red-600">
                    {zodForm.formState.errors.customRelationshipType.message}
                  </p>
                )}
            </div>

            <div className="md:col-span-2">
              <div className="flex items-center h-10">
                <input
                  type="checkbox"
                  id="approvalGiven"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  {...zodForm.register("approvalGiven")}
                />
                <label
                  htmlFor="approvalGiven"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Veli onayı verildi
                </label>
              </div>
              {zodForm.formState.errors.approvalGiven && (
                <p className="mt-1 text-sm text-red-600">
                  {zodForm.formState.errors.approvalGiven.message}
                </p>
              )}
            </div>
          </div>

          {/* SAĞLIK BİLGİLERİ */}
          <div className="space-y-4 pt-2 pb-2">
            <h3 className="text-lg font-semibold text-[#021F4A]">
              SAĞLIK BİLGİLERİ
            </h3>
            <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
          </div>

          <div className="mb-4">
            <div className="flex items-center h-10">
              <input
                type="checkbox"
                id="hasHealthIssues"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                {...zodForm.register("hasHealthIssues")}
              />
              <label
                htmlFor="hasHealthIssues"
                className="ml-2 block text-sm text-gray-700"
              >
                Sağlık sorunu var
              </label>
            </div>

            {zodForm.watch("hasHealthIssues") && (
              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sağlık Sorunu Detayları
                </label>
                <textarea
                  placeholder="Sağlık sorunu detayları"
                  rows={2}
                  className={`w-full border ${
                    zodForm.formState.errors.healthIssues
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  {...zodForm.register("healthIssues")}
                ></textarea>
                {zodForm.formState.errors.healthIssues && (
                  <p className="mt-1 text-sm text-red-600">
                    {zodForm.formState.errors.healthIssues.message}
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </Modal>

      {/* Rating Modal */}
      <Modal
        title={`${currentRatingUser?.fullName} - Puanlama`}
        open={isRatingModalVisible}
        onCancel={() => setIsRatingModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsRatingModalVisible(false)}>
            İptal
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleSubmitRating}
            className="bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
          >
            Puanı Kaydet
          </Button>,
        ]}
        width={600}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Topla Beceri (0-100)
            </label>
            <InputNumber
              min={0}
              max={100}
              value={ratingForm.toplaBeceri}
              onChange={(value) =>
                setRatingForm((prev) => ({
                  ...prev,
                  toplaBeceri: value || undefined,
                }))
              }
              style={{ width: "100%" }}
              placeholder="Topla beceri puanı"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fiziksel Özellik (0-100)
            </label>
            <InputNumber
              min={0}
              max={100}
              value={ratingForm.fizikselOzellik}
              onChange={(value) =>
                setRatingForm((prev) => ({
                  ...prev,
                  fizikselOzellik: value || undefined,
                }))
              }
              style={{ width: "100%" }}
              placeholder="Fiziksel özellik puanı"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notlar
            </label>
            <Input.TextArea
              rows={4}
              value={ratingForm.notes}
              onChange={(e) =>
                setRatingForm((prev) => ({ ...prev, notes: e.target.value }))
              }
              placeholder="Bu oyuncu hakkında notlarınız..."
            />
          </div>
        </div>
      </Modal>

      {/* Moderator Create Modal */}
      <Modal
        title="Yeni Moderator Ekle"
        open={isModeratorModalVisible}
        onCancel={() => {
          setIsModeratorModalVisible(false);
          moderatorForm.resetFields();
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setIsModeratorModalVisible(false);
              moderatorForm.resetFields();
            }}
          >
            İptal
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => moderatorForm.submit()}
            loading={moderatorLoading}
            className="bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
          >
            Oluştur
          </Button>,
        ]}
      >
        <Form
          form={moderatorForm}
          layout="vertical"
          onFinish={handleCreateModerator}
        >
          <Form.Item
            name="fullName"
            label="Ad Soyad"
            rules={[
              { required: true, message: "Ad Soyad gereklidir" },
              { min: 2, message: "Ad Soyad en az 2 karakter olmalıdır" },
            ]}
          >
            <Input placeholder="Moderator adı soyadı" />
          </Form.Item>

          <Form.Item
            name="email"
            label="E-posta"
            rules={[
              { required: true, message: "E-posta gereklidir" },
              { type: "email", message: "Geçerli bir e-posta adresi giriniz" },
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>

          <Form.Item
            name="password"
            label="Şifre"
            rules={[
              { required: true, message: "Şifre gereklidir" },
              { min: 6, message: "Şifre en az 6 karakter olmalıdır" },
            ]}
          >
            <Input.Password placeholder="Güvenli bir şifre" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Moderator Delete Modal */}
      <Modal
        title="Moderator Sil"
        open={isDeleteModeratorModalVisible}
        onCancel={() => {
          setIsDeleteModeratorModalVisible(false);
          setCurrentModerator(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setIsDeleteModeratorModalVisible(false);
              setCurrentModerator(null);
            }}
          >
            İptal
          </Button>,
          <Button
            key="delete"
            type="primary"
            danger
            onClick={handleDeleteModerator}
            loading={moderatorLoading}
          >
            Sil
          </Button>,
        ]}
      >
        <p>
          <strong>{currentModerator?.fullName}</strong> adlı moderatorü silmek
          istediğinizden emin misiniz? Bu işlem geri alınamaz.
        </p>
      </Modal>

      {/* Moderator Edit Modal */}
      <Modal
        title="Moderator Düzenle"
        open={isEditModeratorModalVisible}
        onCancel={() => {
          setIsEditModeratorModalVisible(false);
          editModeratorForm.resetFields();
          setCurrentModerator(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setIsEditModeratorModalVisible(false);
              editModeratorForm.resetFields();
              setCurrentModerator(null);
            }}
          >
            İptal
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => editModeratorForm.submit()}
            loading={moderatorLoading}
            className="bg-gradient-to-r from-[#021F4A] to-[#0A4392]"
          >
            Güncelle
          </Button>,
        ]}
      >
        <Form
          form={editModeratorForm}
          layout="vertical"
          onFinish={handleEditModerator}
        >
          <Form.Item
            name="fullName"
            label="Ad Soyad"
            rules={[
              { required: true, message: "Ad Soyad gereklidir" },
              { min: 2, message: "Ad Soyad en az 2 karakter olmalıdır" },
            ]}
          >
            <Input placeholder="Moderator adı soyadı" />
          </Form.Item>

          <Form.Item
            name="email"
            label="E-posta"
            rules={[
              { required: true, message: "E-posta gereklidir" },
              { type: "email", message: "Geçerli bir e-posta adresi giriniz" },
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>

          <Form.Item
            name="password"
            label="Yeni Şifre (İsteğe bağlı)"
            help="Şifreyi değiştirmek istemiyorsanız boş bırakın"
            rules={[
              {
                validator: (_, value) => {
                  if (value && value.length < 6) {
                    return Promise.reject(
                      new Error("Şifre en az 6 karakter olmalıdır")
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input.Password placeholder="Yeni şifre (boş bırakılabilir)" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Admin;
