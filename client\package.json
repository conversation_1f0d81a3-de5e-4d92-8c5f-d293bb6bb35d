{"name": "fenerbahce-form", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3030", "build": "next build", "start": "next start -p 3030", "lint": "next lint"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^6.0.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "antd": "^5.24.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "lightningcss": "^1.29.3", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.56.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "xlsx": "^0.18.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}