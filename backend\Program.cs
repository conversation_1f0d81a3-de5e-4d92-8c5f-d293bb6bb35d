using System.Reflection;
using System.Text;
using backend.Data;
using backend.Models;
using backend.Models.DTOs;
using backend.Repositories;
using backend.Repositories.Interfaces;
using backend.Service;
using FluentValidation;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using OfficeOpenXml;

// Set EPPlus license context
ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection"))
);

builder.Services.AddControllers(options =>
{
    options.Filters.Add(new AuthorizeFilter());
});

builder.Services.Configure<MailSettings>(builder.Configuration.GetSection("MailSettings"));

builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IParentsRepository, ParentsRepository>();
builder.Services.AddScoped<IRatingRepository, RatingRepository>();
builder.Services.AddScoped<IMailService, MailService>();

builder.Services.AddControllers().AddFluentValidation();

builder.Services.AddValidatorsFromAssemblyContaining<Program>();

builder
    .Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"])
            ),
        };
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
});

builder
    .Services.AddIdentityCore<User>()
    .AddRoles<IdentityRole<Guid>>()
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddDefaultTokenProviders();

builder.Services.AddCors(options =>
{
    options.AddPolicy(
        "AllowFrontend",
        policy =>
            policy
                .WithOrigins(
                    "http://localhost:3020",
                    "http://frontend:3020",
                    "http://localhost",
                    "http://localhost:3023",
                    "http://nginx"
                )
                .AllowAnyHeader()
                .AllowAnyMethod()
                .AllowCredentials()
                .SetIsOriginAllowed(origin => true)
                .WithExposedHeaders("Authorization")
    );
});

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc(
        "v1",
        new OpenApiInfo
        {
            Title = "Fenerbahçe Form API",
            Version = "v1",
            Description = "API for managing Fenerbahçe registration forms",
            Contact = new OpenApiContact { Name = "Fenerbahçe Form Team" },
        }
    );

    c.AddSecurityDefinition(
        "Bearer",
        new OpenApiSecurityScheme
        {
            Description =
                "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer",
        }
    );

    c.AddSecurityRequirement(
        new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer",
                    },
                },
                new string[] { }
            },
        }
    );

    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// if (app.Environment.IsDevelopment())
// {

app.UseAuthentication();
app.UseAuthorization();

app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Fenerbahçe Form API V1");
    c.RoutePrefix = "swagger";
    c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.List);
    c.DefaultModelsExpandDepth(1);
});

// }

app.UseHttpsRedirection();

app.UseCors("AllowFrontend");

app.UseAuthorization();

app.MapControllers();

using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<ApplicationDbContext>();
        context.Database.Migrate();

        await SeedDefaultUser(services);
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while migrating or initializing the database.");
    }
}

async Task SeedDefaultUser(IServiceProvider services)
{
    var userManager = services.GetRequiredService<UserManager<User>>();
    var roleManager = services.GetRequiredService<RoleManager<IdentityRole<Guid>>>();

    // Create Admin role if it doesn't exist
    if (!await roleManager.RoleExistsAsync("Admin"))
    {
        await roleManager.CreateAsync(new IdentityRole<Guid>("Admin"));
    }

    // Create Moderator role if it doesn't exist
    if (!await roleManager.RoleExistsAsync("Moderator"))
    {
        await roleManager.CreateAsync(new IdentityRole<Guid>("Moderator"));
    }

    // Check if any users exist
    if (!userManager.Users.Any())
    {
        var adminUser = new User
        {
            UserName = "<EMAIL>",
            FullName = "Admin User",
            ClubName = "Fenerbahçe",
            DateOfBirth = new DateTime(1990, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            Height = 180,
            Weight = 75,
            ParentId = null, // Assuming admin user does not have a parent
            Email = "<EMAIL>",
            EmailConfirmed = true,
            HasHealthIssues = false,
            HealthIssues = "",
        };

        var result = await userManager.CreateAsync(adminUser, "Fenerbahce2025!");

        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(adminUser, "Admin");
            var logger = services.GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Default admin user created successfully.");
        }

        // Create 5 Moderator users
        var moderators = new[]
        {
            new
            {
                Username = "<EMAIL>",
                FullName = "Moderatör 1",
                Password = "Fenerbahce2025!",
            },
            new
            {
                Username = "<EMAIL>",
                FullName = "Moderatör 2",
                Password = "Fenerbahce2025!",
            },
            new
            {
                Username = "<EMAIL>",
                FullName = "Moderatör 3",
                Password = "Fenerbahce2025!",
            },
            new
            {
                Username = "<EMAIL>",
                FullName = "Moderatör 4",
                Password = "Fenerbahce2025!",
            },
            new
            {
                Username = "<EMAIL>",
                FullName = "Moderatör 5",
                Password = "Fenerbahce2025!",
            },
        };

        foreach (var mod in moderators)
        {
            var moderatorUser = new User
            {
                UserName = mod.Username,
                FullName = mod.FullName,
                ClubName = "Fenerbahçe",
                DateOfBirth = new DateTime(1990, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                Height = 180,
                Weight = 75,
                ParentId = null,
                Email = mod.Username,
                EmailConfirmed = true,
                HasHealthIssues = false,
                HealthIssues = "",
            };

            var modResult = await userManager.CreateAsync(moderatorUser, mod.Password);

            if (modResult.Succeeded)
            {
                await userManager.AddToRoleAsync(moderatorUser, "Moderator");
                var logger = services.GetRequiredService<ILogger<Program>>();
                logger.LogInformation($"Moderator user {mod.FullName} created successfully.");
            }
        }
    }
}

app.Run();
