using System.Drawing;
using System.Drawing.Imaging;
using backend.Models;
using backend.Models.DTOs;
using backend.Repositories.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using QRCoder;

namespace backend.Controllers;

/// <summary>
/// Controller for managing form submissions
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FormController(
    IUserRepository userRepository,
    IParentsRepository parentsRepository,
    Service.IMailService emailSender
) : ControllerBase
{
    private readonly IUserRepository _userRepository = userRepository;
    private readonly IParentsRepository _parentsRepository = parentsRepository;

    private readonly Service.IMailService _emailSender = emailSender;

    /// <summary>
    /// Gets form data by tracking code
    /// </summary>
    /// <param name="code">The tracking code to look up</param>
    /// <returns>The user associated with the tracking code</returns>
    /// <response code="200">Returns the user associated with the tracking code</response>
    /// <response code="404">If no user is found with the specified tracking code</response>
    [HttpGet("tracking/{code}")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<UserWithParentDTO>> GetFormByTrackingCode(string code)
    {
        var user = await _userRepository.GetUserByTrackingCodeAsync(code);

        if (user == null)
        {
            return NotFound();
        }

        return Mapping.ToUserWithParentDTO(user);
    }

    /// <summary>
    /// Submits a new form with user and parent information
    /// </summary>
    /// <param name="submission">The form submission data containing user and parent information</param>
    /// <returns>A result containing tracking code and other important information</returns>
    /// <response code="200">If the form was submitted successfully</response>
    /// <response code="400">If the form data is invalid</response>
    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<object>> SubmitForm([FromBody] FormSubmissionDTO submission)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new
                {
                    errors = ModelState
                        .Values.SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList(),
                }
            );
        }

        try
        {
            // Check if email already exists
            if (await _userRepository.EmailExistsAsync(submission.User.Email))
            {
                return BadRequest(
                    new { errors = new[] { "Bu e-posta adresi ile daha önce kayıt yapılmıştır." } }
                );
            }

            // Create parent entity using mapping approach
            var parent = new Parents
            {
                FullName = submission.Parent.FullName,
                MobilePhone = submission.Parent.MobilePhone,
                RelationshipType = submission.Parent.RelationshipType,
                ApprovalGiven = false,
                ApprovalDate = DateTime.UtcNow,
            };

            // Use mapping to update other parent properties from DTO
            Mapping.UpdateParentFromDTO(parent, submission.Parent);

            await _parentsRepository.AddAsync(parent);
            await _parentsRepository.SaveAsync();

            // Create user entity using mapping approach
            var user = new User { ParentId = parent.Id };

            // Use mapping to update user properties from DTO
            Mapping.UpdateUserFromDTO(user, submission.User);

            // Calculate and set the TrackingSequence based on birth year
            // Each birth year starts from 1 and increments for each user born in that year
            var birthYear = user.DateOfBirth.Year;
            user.TrackingSequence = await _userRepository.GetNextTrackingSequenceForBirthYearAsync(
                birthYear
            );

            await _userRepository.AddAsync(user);
            await _userRepository.SaveAsync();

            string htmlBody =
                $@"
                                <html>
                                    <body>
                                        <h2 style='color:blue;'>tracking code {user.TrackingCode}</h2>
                                    </body>
                                </html>";

            await _emailSender.SendEmailAsync(
                user.Email,
                "Fenerbahçe Spor Kulübü Basketbol Altyapı Seçmeleri - Başvuru Onayı",
                GetEmailTemplate(user)
            );

            return Ok(
                new
                {
                    trackingCode = user.TrackingCode,
                    userId = user.Id,
                    parentId = parent.Id,
                    message = "Form submitted successfully",
                }
            );
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Verifies if a form submission has been approved by the parent
    /// </summary>
    /// <param name="userId">The ID of the user to verify</param>
    /// <returns>True if the parent has given approval, otherwise false</returns>
    /// <response code="200">Returns the approval status</response>
    /// <response code="404">If the user is not found</response>
    /// <response code="400">If parent information is missing</response>
    [HttpGet("{userId}/verify")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<bool>> VerifyFormSubmission(Guid userId)
    {
        var user = await _userRepository.GetUserWithParentAsync(userId);
        if (user == null)
        {
            return NotFound();
        }

        if (user.Parent == null)
        {
            return BadRequest("Parent information is missing");
        }

        return Ok(user.Parent.ApprovalGiven);
    }

    /// <summary>
    /// Gets age-based statistics for applications
    /// </summary>
    /// <returns>Age range statistics including application counts</returns>
    /// <response code="200">Returns age-based statistics</response>
    /// <response code="401">If user is not authenticated</response>
    /// <response code="403">If user is not authorized</response>
    [HttpGet("age-statistics")]
    [Authorize(Policy = "AdminOnly")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<object>> GetAgeStatistics()
    {
        try
        {
            // Get only real applicants (excluding admin and moderator users)
            var users = await _userRepository.GetApplicantUsersAsync();

            var currentYear = DateTime.Now.Year;
            var ageStatistics = users
                .GroupBy(u => currentYear - u.DateOfBirth.Year)
                .Select(g => new
                {
                    Age = g.Key,
                    BirthYear = currentYear - g.Key,
                    Count = g.Count(),
                    StatusBreakdown = new
                    {
                        NotDecided = g.Count(u => u.Status == null || u.Status == -1),
                        NotParticipated = g.Count(u => u.Status == 0),
                        Selected = g.Count(u => u.Status == 1),
                        NotSelected = g.Count(u => u.Status == 2),
                    },
                })
                .OrderBy(x => x.Age)
                .ToList();

            var totalApplications = users.Count();
            var summary = new
            {
                TotalApplications = totalApplications,
                AgeGroups = ageStatistics,
                AgeRanges = new[]
                {
                    new
                    {
                        Range = "8-9 yaş (2015-2016)",
                        Count = ageStatistics
                            .Where(x => x.Age >= 8 && x.Age <= 9)
                            .Sum(x => x.Count),
                    },
                    new
                    {
                        Range = "10-11 yaş (2013-2014)",
                        Count = ageStatistics
                            .Where(x => x.Age >= 10 && x.Age <= 11)
                            .Sum(x => x.Count),
                    },
                    new
                    {
                        Range = "12-13 yaş (2011-2012)",
                        Count = ageStatistics
                            .Where(x => x.Age >= 12 && x.Age <= 13)
                            .Sum(x => x.Count),
                    },
                },
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Exports all form submissions to Excel file
    /// </summary>
    /// <returns>Excel file containing all applicant data</returns>
    /// <response code="200">Returns the Excel file</response>
    /// <response code="401">If user is not authenticated</response>
    /// <response code="403">If user is not authorized</response>
    [HttpGet("export/excel")]
    [Authorize(Policy = "AdminOnly")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> ExportToExcel()
    {
        try
        {
            // Get only real applicants (excluding admin and moderator users)
            var users = await _userRepository.GetApplicantUsersAsync();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Başvuru Listesi");

            // Set headers
            var headers = new[]
            {
                "Takip Kodu",
                "Ad Soyad",
                "Doğum Tarihi",
                "Boy (cm)",
                "Kilo (kg)",
                "Telefon",
                "E-posta",
                "İl",
                "İlçe",
                "Kol Açıklığı (cm)",
                "Ayak Numarası",
                "Avuç İçi (cm)",
                "Sıçrama Yüksekliği (cm)",
                "Sıçrama Tarihi",
                "Kulüp Deneyimi",
                "Kulüp Adı",
                "Sağlık Sorunu",
                "Sağlık Detayları",
                "Durum",
                "Veli Adı",
                "Veli Telefonu",
                "Yakınlık Derecesi",
                "Anne Boyu (cm)",
                "Baba Boyu (cm)",
            };

            // Write headers
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet
                    .Cells[1, i + 1]
                    .Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            // Write data
            int row = 2;
            foreach (var user in users)
            {
                worksheet.Cells[row, 1].Value = user.TrackingCode;
                worksheet.Cells[row, 2].Value = user.FullName;
                worksheet.Cells[row, 3].Value = user.DateOfBirth.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 4].Value = user.Height;
                worksheet.Cells[row, 5].Value = user.Weight;
                worksheet.Cells[row, 6].Value = user.PhoneNumber;
                worksheet.Cells[row, 7].Value = user.Email ?? "";
                worksheet.Cells[row, 8].Value = user.Province ?? "";
                worksheet.Cells[row, 9].Value = user.District ?? "";
                worksheet.Cells[row, 10].Value = user.ArmSpan ?? 0;
                worksheet.Cells[row, 11].Value = user.ShoeSize;
                worksheet.Cells[row, 12].Value = user.PalmSize ?? 0;
                worksheet.Cells[row, 13].Value = user.JumpHeight ?? 0;
                worksheet.Cells[row, 14].Value = user.JumpRecordDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cells[row, 15].Value = user.HasPlayedForClub ? "Evet" : "Hayır";
                worksheet.Cells[row, 16].Value = user.ClubName ?? "";
                worksheet.Cells[row, 17].Value = user.HasHealthIssues ? "Evet" : "Hayır";
                worksheet.Cells[row, 18].Value = user.HealthIssues ?? "";
                worksheet.Cells[row, 19].Value = GetStatusTextTurkish(user.Status);
                worksheet.Cells[row, 20].Value = user.Parent?.FullName ?? "";
                worksheet.Cells[row, 21].Value = user.Parent?.MobilePhone ?? "";
                worksheet.Cells[row, 22].Value = user.Parent?.RelationshipType ?? "";
                worksheet.Cells[row, 23].Value = user.Parent?.MotherHeight ?? 0;
                worksheet.Cells[row, 24].Value = user.Parent?.FatherHeight ?? 0;

                row++;
            }

            // Auto-fit columns
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            // Generate filename with current date
            var fileName = $"Basvuru_Listesi_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";

            var stream = new MemoryStream();
            package.SaveAs(stream);
            stream.Position = 0;

            return File(
                stream.ToArray(),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                fileName
            );
        }
        catch (Exception ex)
        {
            return BadRequest(
                new { message = $"Excel dosyası oluşturulurken hata oluştu: {ex.Message}" }
            );
        }
    }

    /// <summary>
    /// Gets status text in Turkish for Excel export
    /// </summary>
    /// <param name="status">Status value</param>
    /// <returns>Turkish status text</returns>
    private string GetStatusTextTurkish(int? status)
    {
        return status switch
        {
            null or -1 => "Seçim Yapılmadı",
            0 => "Seçmeye Katılmadı",
            1 => "Takıma Seçildi",
            2 => "Takıma Seçilmedi",
            _ => "Bilinmiyor",
        };
    }

    /// <summary>
    /// Generates QR code as base64 string
    /// </summary>
    /// <param name="text">Text to encode in QR code</param>
    /// <returns>Base64 encoded QR code image</returns>
    private string GenerateQRCode(string text)
    {
        using var qrGenerator = new QRCodeGenerator();
        using var qrCodeData = qrGenerator.CreateQrCode(text, QRCodeGenerator.ECCLevel.Q);
        using var qrCode = new PngByteQRCode(qrCodeData);
        var qrCodeBytes = qrCode.GetGraphic(20);

        return Convert.ToBase64String(qrCodeBytes);
    }

    /// <summary>
    /// Gets training time based on birth year
    /// </summary>
    /// <param name="birthDate">User's birth date</param>
    /// <returns>Training time slot as string</returns>
    private string GetTrainingTime(DateTime birthDate)
    {
        int birthYear = birthDate.Year;
        return birthYear switch
        {
            2016 => "10.00 - 11.00",
            2015 => "10.00 - 11.00",
            2014 => "11.00 - 12.00",
            2013 => "12.00 - 13.00",
            2012 => "13.00 - 14.00",
            2011 => "14.00 - 15.00",
            _ =>
                "Bu yaş aralığı için başvuru kabul edilmemektedir (2011-2016 arası doğumlular başvuru yapabilir)",
        };
    }

    /// <summary>
    /// Generates HTML email template with user-specific information
    /// </summary>
    /// <param name="user">User entity containing form submission data</param>
    /// <returns>HTML email template as string</returns>
    private string GetEmailTemplate(User user)
    {
        var trainingTime = GetTrainingTime(user.DateOfBirth);

        return $@"
<!DOCTYPE html>
<html lang=""tr"">
  <head>
    <meta charset=""UTF-8"" />
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"" />
    <title>Fenerbahçe Spor Kulübü Basketbol Altyapı Seçmeleri</title>
  </head>
  <body style=""margin: 0; font-family: Arial, Helvetica, sans-serif; background-color: #002b5c; color: white; padding: 20px;"">
    <table width=""100%"" cellpadding=""0"" cellspacing=""0"" style=""max-width: 600px; margin: 0 auto; background-color: #002b5c;"">
      <tr>
        <td style=""text-align: center; padding: 20px;"">
          <!-- Logo Section -->
          <img
            src=""https://upload.wikimedia.org/wikipedia/tr/archive/8/86/20210127193236%21Fenerbah%C3%A7e_SK.png""
            alt=""Fenerbahçe Logo""
            style=""width: 80px; margin-bottom: 10px;""
          />
          <div style=""height: 6px; background-color: #ffcc00; margin: 10px 0; width: 100%;""></div>
          
          <!-- Content Section -->
          <h1 style=""font-size: 20px; margin: 20px 0; font-weight: normal; color: white;"">Sn. {user.FullName}</h1>
          
          <p style=""font-size: 16px; line-height: 1.5; color: white; margin-bottom: 20px;"">
            Fenerbahçe Spor Kulübü Erkek Basketbol Altyapı seçmelerine kaydınız başarıyla gerçekleşmiştir. 
            Başvurunuza ait detayları, antrenman yeri ve saatinizi aşağıda sizinle paylaşmaktayız. 
            Antrenman saatinden en az 15 dk. önce Metro Enerji Spor Salonu girişinde antrenmana hazır 
            şekilde bulunmanızı ve Kulübümüzün resmi renklerine uygun kıyafet tercih etmenizi rica ederiz.
          </p>
          
          <!-- Info Box -->
          <table width=""100%"" cellpadding=""15"" cellspacing=""0"" style=""background-color: #1a4a7a; border-radius: 5px; border-left: 4px solid #ffcc00; margin: 20px 0;"">
            <tr>
              <td style=""text-align: center;"">
                <div style=""background-color: #ffcc00; color: #002b5c; padding: 20px; border-radius: 5px; margin: 20px 0;"">
                  <h2 style=""margin: 0; font-size: 24px; font-weight: bold;"">BAŞVURU NO</h2>
                  <div style=""font-size: 32px; font-weight: bold; margin: 10px 0; letter-spacing: 2px;"">{user.TrackingCode}</div>
                </div>
                
                <p style=""color: white; font-size: 16px; margin: 15px 0; line-height: 1.5;"">
                  <strong>ANTRENMAN YER ve SAATİ:</strong><br>
                  Metro Enerji Spor Salonu<br>
                  22 Haziran 2025 Pazar günü - Saat: {trainingTime}
                </p>
              </td>
            </tr>
          </table>
          
          <p style=""color: #ffcc00; font-weight: bold; margin: 20px 0; font-size: 16px;"">
            <strong>Not:</strong> Antrenmanlarımız veli ve seyirci girişine kapalı olacaktır.
          </p>
          
          <p style=""color: white; font-size: 16px; margin-bottom: 40px;"">Katılım sağlayan tüm sporculara başarılar dileriz.</p>
          
          <div style=""margin-top: 40px; font-weight: bold; font-size: 16px; color: white;"">
            Saygılarımızla,<br />
            Fenerbahçe Spor Kulübü
          </div>
          
          <div style=""height: 6px; background-color: #ffcc00; margin: 20px 0; width: 100%;""></div>
        </td>
      </tr>
    </table>
  </body>
</html>";
    }
}

/// <summary>
/// Data model for form submission containing both user and parent information
/// </summary>
public class FormSubmissionDTO
{
    /// <summary>
    /// The user's information
    /// </summary>
    public required UserWithParentDTO User { get; set; }

    /// <summary>
    /// The parent's information
    /// </summary>
    public required ParentDTO Parent { get; set; }
}
