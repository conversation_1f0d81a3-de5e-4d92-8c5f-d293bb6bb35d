version: "3.8"
services:
  postgres:
    image: postgres:15-alpine
    container_name: fenerbahce-postgres
    environment:
      - POSTGRES_DB=fb
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5454:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped

  backend:
    image: buraxtaa/fenerbahce-back:latest
    ports:
      - "3031:3031"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:3031
      - ASPNETCORE_CORS_HEADERS=*,Authorization
      - ASPNETCORE_CORS_METHODS=*
    networks:
      - app-network
    depends_on:
      - postgres
    restart: unless-stopped

  frontend:
    image: buraxtaa/fenerbahce-front:latest
    ports:
      - "3030:3030"
    environment:
      - NODE_ENV=production
    networks:
      - app-network
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
