"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-tabs";
exports.ids = ["vendor-chunks/rc-tabs"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-tabs/es/TabContext.js":
/*!***********************************************!*\
  !*** ./node_modules/rc-tabs/es/TabContext.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzQztBQUN0Qyw4RUFBNEIsb0RBQWEsTUFBTSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcZmVuZXJiYWhjZS1mb3JtXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtdGFic1xcZXNcXFRhYkNvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9jcmVhdGVDb250ZXh0KG51bGwpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/AddButton.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/AddButton.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar AddButton = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    editable = props.editable,\n    locale = props.locale,\n    style = props.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AddButton);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJOYXZMaXN0L0FkZEJ1dHRvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0I7QUFDL0IsNkJBQTZCLDZDQUFnQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixnREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0QsaUVBQWUsU0FBUyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcZmVuZXJiYWhjZS1mb3JtXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtdGFic1xcZXNcXFRhYk5hdkxpc3RcXEFkZEJ1dHRvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG52YXIgQWRkQnV0dG9uID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoZnVuY3Rpb24gKHByb3BzLCByZWYpIHtcbiAgdmFyIHByZWZpeENscyA9IHByb3BzLnByZWZpeENscyxcbiAgICBlZGl0YWJsZSA9IHByb3BzLmVkaXRhYmxlLFxuICAgIGxvY2FsZSA9IHByb3BzLmxvY2FsZSxcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlO1xuICBpZiAoIWVkaXRhYmxlIHx8IGVkaXRhYmxlLnNob3dBZGQgPT09IGZhbHNlKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiYnV0dG9uXCIsIHtcbiAgICByZWY6IHJlZixcbiAgICB0eXBlOiBcImJ1dHRvblwiLFxuICAgIGNsYXNzTmFtZTogXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1uYXYtYWRkXCIpLFxuICAgIHN0eWxlOiBzdHlsZSxcbiAgICBcImFyaWEtbGFiZWxcIjogKGxvY2FsZSA9PT0gbnVsbCB8fCBsb2NhbGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGxvY2FsZS5hZGRBcmlhTGFiZWwpIHx8ICdBZGQgdGFiJyxcbiAgICBvbkNsaWNrOiBmdW5jdGlvbiBvbkNsaWNrKGV2ZW50KSB7XG4gICAgICBlZGl0YWJsZS5vbkVkaXQoJ2FkZCcsIHtcbiAgICAgICAgZXZlbnQ6IGV2ZW50XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIGVkaXRhYmxlLmFkZEljb24gfHwgJysnKTtcbn0pO1xuZXhwb3J0IGRlZmF1bHQgQWRkQnV0dG9uOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/AddButton.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/ExtraContent.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/ExtraContent.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar ExtraContent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {\n  var position = props.position,\n    prefixCls = props.prefixCls,\n    extra = props.extra;\n  if (!extra) {\n    return null;\n  }\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(extra) === 'object' && ! /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (true) {\n  ExtraContent.displayName = 'ExtraContent';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExtraContent);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJOYXZMaXN0L0V4dHJhQ29udGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdEO0FBQ3pCO0FBQy9CLGdDQUFnQyw2Q0FBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU0sNkVBQU8sdUNBQXVDLGlEQUFvQjtBQUN4RTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLGdEQUFtQjtBQUNuRDtBQUNBO0FBQ0EsR0FBRztBQUNILENBQUM7QUFDRCxJQUFJLElBQXFDO0FBQ3pDO0FBQ0E7QUFDQSxpRUFBZSxZQUFZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxmZW5lcmJhaGNlLWZvcm1cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxyYy10YWJzXFxlc1xcVGFiTmF2TGlzdFxcRXh0cmFDb250ZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfdHlwZW9mIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90eXBlb2ZcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbnZhciBFeHRyYUNvbnRlbnQgPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihmdW5jdGlvbiAocHJvcHMsIHJlZikge1xuICB2YXIgcG9zaXRpb24gPSBwcm9wcy5wb3NpdGlvbixcbiAgICBwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgZXh0cmEgPSBwcm9wcy5leHRyYTtcbiAgaWYgKCFleHRyYSkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG4gIHZhciBjb250ZW50O1xuXG4gIC8vIFBhcnNlIGV4dHJhXG4gIHZhciBhc3NlcnRFeHRyYSA9IHt9O1xuICBpZiAoX3R5cGVvZihleHRyYSkgPT09ICdvYmplY3QnICYmICEgLyojX19QVVJFX18qL1JlYWN0LmlzVmFsaWRFbGVtZW50KGV4dHJhKSkge1xuICAgIGFzc2VydEV4dHJhID0gZXh0cmE7XG4gIH0gZWxzZSB7XG4gICAgYXNzZXJ0RXh0cmEucmlnaHQgPSBleHRyYTtcbiAgfVxuICBpZiAocG9zaXRpb24gPT09ICdyaWdodCcpIHtcbiAgICBjb250ZW50ID0gYXNzZXJ0RXh0cmEucmlnaHQ7XG4gIH1cbiAgaWYgKHBvc2l0aW9uID09PSAnbGVmdCcpIHtcbiAgICBjb250ZW50ID0gYXNzZXJ0RXh0cmEubGVmdDtcbiAgfVxuICByZXR1cm4gY29udGVudCA/IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItZXh0cmEtY29udGVudFwiKSxcbiAgICByZWY6IHJlZlxuICB9LCBjb250ZW50KSA6IG51bGw7XG59KTtcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIEV4dHJhQ29udGVudC5kaXNwbGF5TmFtZSA9ICdFeHRyYUNvbnRlbnQnO1xufVxuZXhwb3J0IGRlZmF1bHQgRXh0cmFDb250ZW50OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/ExtraContent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/OperationNode.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/OperationNode.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var rc_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-dropdown */ \"(ssr)/./node_modules/rc-dropdown/es/index.js\");\n/* harmony import */ var rc_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-menu */ \"(ssr)/./node_modules/rc-menu/es/index.js\");\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/rc-tabs/es/util.js\");\n/* harmony import */ var _AddButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./AddButton */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/AddButton.js\");\n\n\n\n\n\n\n\n\n\n\n\nvar OperationNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    tabs = props.tabs,\n    locale = props.locale,\n    mobile = props.mobile,\n    _props$more = props.more,\n    moreProps = _props$more === void 0 ? {} : _props$more,\n    style = props.style,\n    className = props.className,\n    editable = props.editable,\n    tabBarGutter = props.tabBarGutter,\n    rtl = props.rtl,\n    removeAriaLabel = props.removeAriaLabel,\n    onTabClick = props.onTabClick,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName;\n  // ======================== Dropdown ========================\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var _moreProps$icon = moreProps.icon,\n    moreIcon = _moreProps$icon === void 0 ? 'More' : _moreProps$icon;\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(rc_menu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n    onClick: function onClick(_ref) {\n      var key = _ref.key,\n        domEvent = _ref.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var closable = tab.closable,\n      disabled = tab.disabled,\n      closeIcon = tab.closeIcon,\n      key = tab.key,\n      label = tab.label;\n    var removable = (0,_util__WEBPACK_IMPORTED_MODULE_8__.getRemovable)(closable, closeIcon, editable, disabled);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(rc_menu__WEBPACK_IMPORTED_MODULE_5__.MenuItem, {\n      key: key,\n      id: \"\".concat(popupId, \"-\").concat(key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n      disabled: disabled\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"span\", null, label), removable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, key);\n      }\n    }, closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].DOWN, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].SPACE, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].ESC:\n        setOpen(false);\n        break;\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].SPACE:\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].ENTER:\n        if (selectedKey !== null) {\n          onTabClick(selectedKey, e);\n        }\n        break;\n    }\n  }\n\n  // ========================= Effect =========================\n  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]);\n\n  // ========================= Render =========================\n  var moreStyle = (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classnames__WEBPACK_IMPORTED_MODULE_3___default()((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(rc_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    visible: tabs.length ? open : false,\n    onVisibleChange: setOpen,\n    overlayClassName: classnames__WEBPACK_IMPORTED_MODULE_3___default()(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, moreProps), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_AddButton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.memo(OperationNode, function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJOYXZMaXN0L09wZXJhdGlvbk5vZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUNjO0FBQ0Y7QUFDbEM7QUFDRDtBQUNNO0FBQ0E7QUFDVjtBQUNhO0FBQ0w7QUFDSDtBQUNwQyxpQ0FBaUMsNkNBQWdCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwrQ0FBUTtBQUMxQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBLG1CQUFtQiwrQ0FBUTtBQUMzQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLDBCQUEwQixnREFBbUIsQ0FBQywrQ0FBSTtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsbURBQVk7QUFDaEMsd0JBQXdCLGdEQUFtQixDQUFDLDZDQUFRO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLGVBQWUsZ0RBQW1CLGlEQUFpRCxnREFBbUI7QUFDM0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxvQkFBb0IsU0FBUztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywwREFBTyxPQUFPLDBEQUFPLFFBQVEsMERBQU87QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywwREFBTztBQUNsQjtBQUNBO0FBQ0E7QUFDQSxXQUFXLDBEQUFPO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMERBQU87QUFDbEI7QUFDQTtBQUNBLFdBQVcsMERBQU87QUFDbEIsV0FBVywwREFBTztBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLGtCQUFrQixxRkFBZSxHQUFHO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGlEQUFVLENBQUMscUZBQWUsR0FBRztBQUN0RCw4Q0FBOEMsZ0RBQW1CLENBQUMsbURBQVEsRUFBRSw4RUFBUTtBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixpREFBVTtBQUNoQztBQUNBO0FBQ0E7QUFDQSxHQUFHLDJCQUEyQixnREFBbUI7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxzQkFBc0IsZ0RBQW1CO0FBQ3pDLGVBQWUsaURBQVU7QUFDekI7QUFDQTtBQUNBLEdBQUcseUJBQXlCLGdEQUFtQixDQUFDLGtEQUFTO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0QsOEVBQTRCLHVDQUFVO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGZlbmVyYmFoY2UtZm9ybVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLXRhYnNcXGVzXFxUYWJOYXZMaXN0XFxPcGVyYXRpb25Ob2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZGVmaW5lUHJvcGVydHlcIjtcbmltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0IGNsYXNzTmFtZXMgZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgRHJvcGRvd24gZnJvbSAncmMtZHJvcGRvd24nO1xuaW1wb3J0IE1lbnUsIHsgTWVudUl0ZW0gfSBmcm9tICdyYy1tZW51JztcbmltcG9ydCBLZXlDb2RlIGZyb20gXCJyYy11dGlsL2VzL0tleUNvZGVcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBnZXRSZW1vdmFibGUgfSBmcm9tIFwiLi4vdXRpbFwiO1xuaW1wb3J0IEFkZEJ1dHRvbiBmcm9tIFwiLi9BZGRCdXR0b25cIjtcbnZhciBPcGVyYXRpb25Ob2RlID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoZnVuY3Rpb24gKHByb3BzLCByZWYpIHtcbiAgdmFyIHByZWZpeENscyA9IHByb3BzLnByZWZpeENscyxcbiAgICBpZCA9IHByb3BzLmlkLFxuICAgIHRhYnMgPSBwcm9wcy50YWJzLFxuICAgIGxvY2FsZSA9IHByb3BzLmxvY2FsZSxcbiAgICBtb2JpbGUgPSBwcm9wcy5tb2JpbGUsXG4gICAgX3Byb3BzJG1vcmUgPSBwcm9wcy5tb3JlLFxuICAgIG1vcmVQcm9wcyA9IF9wcm9wcyRtb3JlID09PSB2b2lkIDAgPyB7fSA6IF9wcm9wcyRtb3JlLFxuICAgIHN0eWxlID0gcHJvcHMuc3R5bGUsXG4gICAgY2xhc3NOYW1lID0gcHJvcHMuY2xhc3NOYW1lLFxuICAgIGVkaXRhYmxlID0gcHJvcHMuZWRpdGFibGUsXG4gICAgdGFiQmFyR3V0dGVyID0gcHJvcHMudGFiQmFyR3V0dGVyLFxuICAgIHJ0bCA9IHByb3BzLnJ0bCxcbiAgICByZW1vdmVBcmlhTGFiZWwgPSBwcm9wcy5yZW1vdmVBcmlhTGFiZWwsXG4gICAgb25UYWJDbGljayA9IHByb3BzLm9uVGFiQ2xpY2ssXG4gICAgZ2V0UG9wdXBDb250YWluZXIgPSBwcm9wcy5nZXRQb3B1cENvbnRhaW5lcixcbiAgICBwb3B1cENsYXNzTmFtZSA9IHByb3BzLnBvcHVwQ2xhc3NOYW1lO1xuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT0gRHJvcGRvd24gPT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBfdXNlU3RhdGUgPSB1c2VTdGF0ZShmYWxzZSksXG4gICAgX3VzZVN0YXRlMiA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0ZSwgMiksXG4gICAgb3BlbiA9IF91c2VTdGF0ZTJbMF0sXG4gICAgc2V0T3BlbiA9IF91c2VTdGF0ZTJbMV07XG4gIHZhciBfdXNlU3RhdGUzID0gdXNlU3RhdGUobnVsbCksXG4gICAgX3VzZVN0YXRlNCA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0ZTMsIDIpLFxuICAgIHNlbGVjdGVkS2V5ID0gX3VzZVN0YXRlNFswXSxcbiAgICBzZXRTZWxlY3RlZEtleSA9IF91c2VTdGF0ZTRbMV07XG4gIHZhciBfbW9yZVByb3BzJGljb24gPSBtb3JlUHJvcHMuaWNvbixcbiAgICBtb3JlSWNvbiA9IF9tb3JlUHJvcHMkaWNvbiA9PT0gdm9pZCAwID8gJ01vcmUnIDogX21vcmVQcm9wcyRpY29uO1xuICB2YXIgcG9wdXBJZCA9IFwiXCIuY29uY2F0KGlkLCBcIi1tb3JlLXBvcHVwXCIpO1xuICB2YXIgZHJvcGRvd25QcmVmaXggPSBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWRyb3Bkb3duXCIpO1xuICB2YXIgc2VsZWN0ZWRJdGVtSWQgPSBzZWxlY3RlZEtleSAhPT0gbnVsbCA/IFwiXCIuY29uY2F0KHBvcHVwSWQsIFwiLVwiKS5jb25jYXQoc2VsZWN0ZWRLZXkpIDogbnVsbDtcbiAgdmFyIGRyb3Bkb3duQXJpYUxhYmVsID0gbG9jYWxlID09PSBudWxsIHx8IGxvY2FsZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogbG9jYWxlLmRyb3Bkb3duQXJpYUxhYmVsO1xuICBmdW5jdGlvbiBvblJlbW92ZVRhYihldmVudCwga2V5KSB7XG4gICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICBlZGl0YWJsZS5vbkVkaXQoJ3JlbW92ZScsIHtcbiAgICAgIGtleToga2V5LFxuICAgICAgZXZlbnQ6IGV2ZW50XG4gICAgfSk7XG4gIH1cbiAgdmFyIG1lbnUgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChNZW51LCB7XG4gICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhfcmVmKSB7XG4gICAgICB2YXIga2V5ID0gX3JlZi5rZXksXG4gICAgICAgIGRvbUV2ZW50ID0gX3JlZi5kb21FdmVudDtcbiAgICAgIG9uVGFiQ2xpY2soa2V5LCBkb21FdmVudCk7XG4gICAgICBzZXRPcGVuKGZhbHNlKTtcbiAgICB9LFxuICAgIHByZWZpeENsczogXCJcIi5jb25jYXQoZHJvcGRvd25QcmVmaXgsIFwiLW1lbnVcIiksXG4gICAgaWQ6IHBvcHVwSWQsXG4gICAgdGFiSW5kZXg6IC0xLFxuICAgIHJvbGU6IFwibGlzdGJveFwiLFxuICAgIFwiYXJpYS1hY3RpdmVkZXNjZW5kYW50XCI6IHNlbGVjdGVkSXRlbUlkLFxuICAgIHNlbGVjdGVkS2V5czogW3NlbGVjdGVkS2V5XSxcbiAgICBcImFyaWEtbGFiZWxcIjogZHJvcGRvd25BcmlhTGFiZWwgIT09IHVuZGVmaW5lZCA/IGRyb3Bkb3duQXJpYUxhYmVsIDogJ2V4cGFuZGVkIGRyb3Bkb3duJ1xuICB9LCB0YWJzLm1hcChmdW5jdGlvbiAodGFiKSB7XG4gICAgdmFyIGNsb3NhYmxlID0gdGFiLmNsb3NhYmxlLFxuICAgICAgZGlzYWJsZWQgPSB0YWIuZGlzYWJsZWQsXG4gICAgICBjbG9zZUljb24gPSB0YWIuY2xvc2VJY29uLFxuICAgICAga2V5ID0gdGFiLmtleSxcbiAgICAgIGxhYmVsID0gdGFiLmxhYmVsO1xuICAgIHZhciByZW1vdmFibGUgPSBnZXRSZW1vdmFibGUoY2xvc2FibGUsIGNsb3NlSWNvbiwgZWRpdGFibGUsIGRpc2FibGVkKTtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoTWVudUl0ZW0sIHtcbiAgICAgIGtleToga2V5LFxuICAgICAgaWQ6IFwiXCIuY29uY2F0KHBvcHVwSWQsIFwiLVwiKS5jb25jYXQoa2V5KSxcbiAgICAgIHJvbGU6IFwib3B0aW9uXCIsXG4gICAgICBcImFyaWEtY29udHJvbHNcIjogaWQgJiYgXCJcIi5jb25jYXQoaWQsIFwiLXBhbmVsLVwiKS5jb25jYXQoa2V5KSxcbiAgICAgIGRpc2FibGVkOiBkaXNhYmxlZFxuICAgIH0sIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwic3BhblwiLCBudWxsLCBsYWJlbCksIHJlbW92YWJsZSAmJiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImJ1dHRvblwiLCB7XG4gICAgICB0eXBlOiBcImJ1dHRvblwiLFxuICAgICAgXCJhcmlhLWxhYmVsXCI6IHJlbW92ZUFyaWFMYWJlbCB8fCAncmVtb3ZlJyxcbiAgICAgIHRhYkluZGV4OiAwLFxuICAgICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChkcm9wZG93blByZWZpeCwgXCItbWVudS1pdGVtLXJlbW92ZVwiKSxcbiAgICAgIG9uQ2xpY2s6IGZ1bmN0aW9uIG9uQ2xpY2soZSkge1xuICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICBvblJlbW92ZVRhYihlLCBrZXkpO1xuICAgICAgfVxuICAgIH0sIGNsb3NlSWNvbiB8fCBlZGl0YWJsZS5yZW1vdmVJY29uIHx8ICfDlycpKTtcbiAgfSkpO1xuICBmdW5jdGlvbiBzZWxlY3RPZmZzZXQob2Zmc2V0KSB7XG4gICAgdmFyIGVuYWJsZWRUYWJzID0gdGFicy5maWx0ZXIoZnVuY3Rpb24gKHRhYikge1xuICAgICAgcmV0dXJuICF0YWIuZGlzYWJsZWQ7XG4gICAgfSk7XG4gICAgdmFyIHNlbGVjdGVkSW5kZXggPSBlbmFibGVkVGFicy5maW5kSW5kZXgoZnVuY3Rpb24gKHRhYikge1xuICAgICAgcmV0dXJuIHRhYi5rZXkgPT09IHNlbGVjdGVkS2V5O1xuICAgIH0pIHx8IDA7XG4gICAgdmFyIGxlbiA9IGVuYWJsZWRUYWJzLmxlbmd0aDtcbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbjsgaSArPSAxKSB7XG4gICAgICBzZWxlY3RlZEluZGV4ID0gKHNlbGVjdGVkSW5kZXggKyBvZmZzZXQgKyBsZW4pICUgbGVuO1xuICAgICAgdmFyIHRhYiA9IGVuYWJsZWRUYWJzW3NlbGVjdGVkSW5kZXhdO1xuICAgICAgaWYgKCF0YWIuZGlzYWJsZWQpIHtcbiAgICAgICAgc2V0U2VsZWN0ZWRLZXkodGFiLmtleSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gb25LZXlEb3duKGUpIHtcbiAgICB2YXIgd2hpY2ggPSBlLndoaWNoO1xuICAgIGlmICghb3Blbikge1xuICAgICAgaWYgKFtLZXlDb2RlLkRPV04sIEtleUNvZGUuU1BBQ0UsIEtleUNvZGUuRU5URVJdLmluY2x1ZGVzKHdoaWNoKSkge1xuICAgICAgICBzZXRPcGVuKHRydWUpO1xuICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICB9XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHN3aXRjaCAod2hpY2gpIHtcbiAgICAgIGNhc2UgS2V5Q29kZS5VUDpcbiAgICAgICAgc2VsZWN0T2Zmc2V0KC0xKTtcbiAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgS2V5Q29kZS5ET1dOOlxuICAgICAgICBzZWxlY3RPZmZzZXQoMSk7XG4gICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIEtleUNvZGUuRVNDOlxuICAgICAgICBzZXRPcGVuKGZhbHNlKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIEtleUNvZGUuU1BBQ0U6XG4gICAgICBjYXNlIEtleUNvZGUuRU5URVI6XG4gICAgICAgIGlmIChzZWxlY3RlZEtleSAhPT0gbnVsbCkge1xuICAgICAgICAgIG9uVGFiQ2xpY2soc2VsZWN0ZWRLZXksIGUpO1xuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuICAgIH1cbiAgfVxuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gRWZmZWN0ID09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICAvLyBXZSB1c2UgcXVlcnkgZWxlbWVudCBoZXJlIHRvIGF2b2lkIFJlYWN0IHN0cmljdCB3YXJuaW5nXG4gICAgdmFyIGVsZSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHNlbGVjdGVkSXRlbUlkKTtcbiAgICBpZiAoZWxlICYmIGVsZS5zY3JvbGxJbnRvVmlldykge1xuICAgICAgZWxlLnNjcm9sbEludG9WaWV3KGZhbHNlKTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZEtleV0pO1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICghb3Blbikge1xuICAgICAgc2V0U2VsZWN0ZWRLZXkobnVsbCk7XG4gICAgfVxuICB9LCBbb3Blbl0pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gUmVuZGVyID09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIG1vcmVTdHlsZSA9IF9kZWZpbmVQcm9wZXJ0eSh7fSwgcnRsID8gJ21hcmdpblJpZ2h0JyA6ICdtYXJnaW5MZWZ0JywgdGFiQmFyR3V0dGVyKTtcbiAgaWYgKCF0YWJzLmxlbmd0aCkge1xuICAgIG1vcmVTdHlsZS52aXNpYmlsaXR5ID0gJ2hpZGRlbic7XG4gICAgbW9yZVN0eWxlLm9yZGVyID0gMTtcbiAgfVxuICB2YXIgb3ZlcmxheUNsYXNzTmFtZSA9IGNsYXNzTmFtZXMoX2RlZmluZVByb3BlcnR5KHt9LCBcIlwiLmNvbmNhdChkcm9wZG93blByZWZpeCwgXCItcnRsXCIpLCBydGwpKTtcbiAgdmFyIG1vcmVOb2RlID0gbW9iaWxlID8gbnVsbCA6IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KERyb3Bkb3duLCBfZXh0ZW5kcyh7XG4gICAgcHJlZml4Q2xzOiBkcm9wZG93blByZWZpeCxcbiAgICBvdmVybGF5OiBtZW51LFxuICAgIHZpc2libGU6IHRhYnMubGVuZ3RoID8gb3BlbiA6IGZhbHNlLFxuICAgIG9uVmlzaWJsZUNoYW5nZTogc2V0T3BlbixcbiAgICBvdmVybGF5Q2xhc3NOYW1lOiBjbGFzc05hbWVzKG92ZXJsYXlDbGFzc05hbWUsIHBvcHVwQ2xhc3NOYW1lKSxcbiAgICBtb3VzZUVudGVyRGVsYXk6IDAuMSxcbiAgICBtb3VzZUxlYXZlRGVsYXk6IDAuMSxcbiAgICBnZXRQb3B1cENvbnRhaW5lcjogZ2V0UG9wdXBDb250YWluZXJcbiAgfSwgbW9yZVByb3BzKSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJidXR0b25cIiwge1xuICAgIHR5cGU6IFwiYnV0dG9uXCIsXG4gICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLW5hdi1tb3JlXCIpLFxuICAgIHN0eWxlOiBtb3JlU3R5bGUsXG4gICAgXCJhcmlhLWhhc3BvcHVwXCI6IFwibGlzdGJveFwiLFxuICAgIFwiYXJpYS1jb250cm9sc1wiOiBwb3B1cElkLFxuICAgIGlkOiBcIlwiLmNvbmNhdChpZCwgXCItbW9yZVwiKSxcbiAgICBcImFyaWEtZXhwYW5kZWRcIjogb3BlbixcbiAgICBvbktleURvd246IG9uS2V5RG93blxuICB9LCBtb3JlSWNvbikpO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lcyhcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLW5hdi1vcGVyYXRpb25zXCIpLCBjbGFzc05hbWUpLFxuICAgIHN0eWxlOiBzdHlsZSxcbiAgICByZWY6IHJlZlxuICB9LCBtb3JlTm9kZSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQWRkQnV0dG9uLCB7XG4gICAgcHJlZml4Q2xzOiBwcmVmaXhDbHMsXG4gICAgbG9jYWxlOiBsb2NhbGUsXG4gICAgZWRpdGFibGU6IGVkaXRhYmxlXG4gIH0pKTtcbn0pO1xuZXhwb3J0IGRlZmF1bHQgLyojX19QVVJFX18qL1JlYWN0Lm1lbW8oT3BlcmF0aW9uTm9kZSwgZnVuY3Rpb24gKF8sIG5leHQpIHtcbiAgcmV0dXJuIChcbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vYW50LWRlc2lnbi9hbnQtZGVzaWduL2lzc3Vlcy8zMjU0NFxuICAgIC8vIFdlJ2QgYmV0dGVyIHJlbW92ZSBzeW50YWN0aWMgc3VnYXIgaW4gYHJjLW1lbnVgIHNpbmNlIHRoaXMgaGFzIHBlcmYgaXNzdWVcbiAgICBuZXh0LnRhYk1vdmluZ1xuICApO1xufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/OperationNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/TabNode.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/TabNode.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/rc-tabs/es/util.js\");\n\n\n\n\nvar TabNode = function TabNode(props) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    active = props.active,\n    focus = props.focus,\n    _props$tab = props.tab,\n    key = _props$tab.key,\n    label = _props$tab.label,\n    disabled = _props$tab.disabled,\n    closeIcon = _props$tab.closeIcon,\n    icon = _props$tab.icon,\n    closable = props.closable,\n    renderWrapper = props.renderWrapper,\n    removeAriaLabel = props.removeAriaLabel,\n    editable = props.editable,\n    onClick = props.onClick,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    style = props.style,\n    tabCount = props.tabCount,\n    currentPosition = props.currentPosition;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  var removable = (0,_util__WEBPACK_IMPORTED_MODULE_3__.getRemovable)(closable, closeIcon, editable, disabled);\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var labelNode = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(function () {\n    return icon && typeof label === 'string' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"span\", null, label) : label;\n  }, [label, icon]);\n  var btnRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {\n    if (focus && btnRef.current) {\n      btnRef.current.focus();\n    }\n  }, [focus]);\n  var node = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", {\n    key: key,\n    \"data-node-key\": (0,_util__WEBPACK_IMPORTED_MODULE_3__.genDataNodeKey)(key),\n    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(tabPrefix, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, \"\".concat(tabPrefix, \"-with-remove\"), removable), \"\".concat(tabPrefix, \"-active\"), active), \"\".concat(tabPrefix, \"-disabled\"), disabled), \"\".concat(tabPrefix, \"-focus\"), focus)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", {\n    ref: btnRef,\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : active ? 0 : -1,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: onKeyDown,\n    onMouseDown: onMouseDown,\n    onMouseUp: onMouseUp,\n    onFocus: onFocus,\n    onBlur: onBlur\n  }, focus && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", {\n    \"aria-live\": \"polite\",\n    style: {\n      width: 0,\n      height: 0,\n      position: 'absolute',\n      overflow: 'hidden',\n      opacity: 0\n    }\n  }, \"Tab \".concat(currentPosition, \" of \").concat(tabCount)), icon && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"span\", {\n    className: \"\".concat(tabPrefix, \"-icon\")\n  }, icon), label && labelNode), removable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"button\", {\n    type: \"button\",\n    role: \"tab\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: active ? 0 : -1,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabNode);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/TabNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/Wrapper.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/Wrapper.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! . */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/index.js\");\n/* harmony import */ var _TabContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../TabContext */ \"(ssr)/./node_modules/rc-tabs/es/TabContext.js\");\n/* harmony import */ var _TabPanelList_TabPane__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../TabPanelList/TabPane */ \"(ssr)/./node_modules/rc-tabs/es/TabPanelList/TabPane.js\");\n\n\n\nvar _excluded = [\"renderTabBar\"],\n  _excluded2 = [\"label\", \"key\"];\n// zombieJ: To compatible with `renderTabBar` usage.\n\n\n\n\n\n// We have to create a TabNavList components.\nvar TabNavListWrapper = function TabNavListWrapper(_ref) {\n  var renderTabBar = _ref.renderTabBar,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_ref, _excluded);\n  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_3__.useContext(_TabContext__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n    tabs = _React$useContext.tabs;\n  if (renderTabBar) {\n    var tabNavBarProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, restProps), {}, {\n      // Legacy support. We do not use this actually\n      panes: tabs.map(function (_ref2) {\n        var label = _ref2.label,\n          key = _ref2.key,\n          restTabProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_ref2, _excluded2);\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_TabPanelList_TabPane__WEBPACK_IMPORTED_MODULE_6__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n          tab: label,\n          key: key,\n          tabKey: key\n        }, restTabProps));\n      })\n    });\n    return renderTabBar(tabNavBarProps, ___WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(___WEBPACK_IMPORTED_MODULE_4__[\"default\"], restProps);\n};\nif (true) {\n  TabNavListWrapper.displayName = 'TabNavListWrapper';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabNavListWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/Wrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _TabContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../TabContext */ \"(ssr)/./node_modules/rc-tabs/es/TabContext.js\");\n/* harmony import */ var _hooks_useIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/useIndicator */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useIndicator.js\");\n/* harmony import */ var _hooks_useOffsets__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../hooks/useOffsets */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useOffsets.js\");\n/* harmony import */ var _hooks_useSyncState__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../hooks/useSyncState */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useSyncState.js\");\n/* harmony import */ var _hooks_useTouchMove__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../hooks/useTouchMove */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useTouchMove.js\");\n/* harmony import */ var _hooks_useUpdate__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../hooks/useUpdate */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useUpdate.js\");\n/* harmony import */ var _hooks_useVisibleRange__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../hooks/useVisibleRange */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useVisibleRange.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/rc-tabs/es/util.js\");\n/* harmony import */ var _AddButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./AddButton */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/AddButton.js\");\n/* harmony import */ var _ExtraContent__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./ExtraContent */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/ExtraContent.js\");\n/* harmony import */ var _OperationNode__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./OperationNode */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/OperationNode.js\");\n/* harmony import */ var _TabNode__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./TabNode */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/TabNode.js\");\n\n\n\n\n\n/* eslint-disable react-hooks/exhaustive-deps */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar getTabSize = function getTabSize(tab, containerRect) {\n  // tabListRef\n  var offsetWidth = tab.offsetWidth,\n    offsetHeight = tab.offsetHeight,\n    offsetTop = tab.offsetTop,\n    offsetLeft = tab.offsetLeft;\n  var _tab$getBoundingClien = tab.getBoundingClientRect(),\n    width = _tab$getBoundingClien.width,\n    height = _tab$getBoundingClien.height,\n    left = _tab$getBoundingClien.left,\n    top = _tab$getBoundingClien.top;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (Math.abs(width - offsetWidth) < 1) {\n    return [width, height, left - containerRect.left, top - containerRect.top];\n  }\n  return [offsetWidth, offsetHeight, offsetLeft, offsetTop];\n};\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (refObj.current) {\n    var _refObj$current$getBo = refObj.current.getBoundingClientRect(),\n      width = _refObj$current$getBo.width,\n      height = _refObj$current$getBo.height;\n    if (Math.abs(width - offsetWidth) < 1) {\n      return [width, height];\n    }\n  }\n  return [offsetWidth, offsetHeight];\n};\n\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nvar TabNavList = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.forwardRef(function (props, ref) {\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    indicator = props.indicator;\n  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_9__.useContext(_TabContext__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var extraLeftRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var extraRightRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var tabsWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var tabListRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var operationsRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var innerAddButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = (0,_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = (0,_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([0, 0]),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([0, 0]),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([0, 0]),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([0, 0]),\n    _useState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useUpdateState = (0,_hooks_useUpdate__WEBPACK_IMPORTED_MODULE_15__.useUpdateState)(new Map()),\n    _useUpdateState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useUpdateState, 2),\n    tabSizes = _useUpdateState2[0],\n    setTabSizes = _useUpdateState2[1];\n  var tabOffsets = (0,_hooks_useOffsets__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(tabs, tabSizes, tabContentSize[0]);\n\n  // ========================== Unit =========================\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = Math.floor(containerExcludeExtraSizeValue) < Math.floor(tabContentSizeValue + addSizeValue);\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue;\n\n  // ========================== Util =========================\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  }\n\n  // ========================= Mobile ========================\n  var touchMovingRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(),\n    _useState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    if (touchMovingRef.current) {\n      clearTimeout(touchMovingRef.current);\n    }\n  }\n  (0,_hooks_useTouchMove__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    // Skip scroll if place is enough\n    if (!needScroll) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]);\n\n  // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n  var _useVisibleRange = (0,_hooks_useVisibleRange__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n\n  // ========================= Scroll ========================\n  var scrollToTab = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft;\n\n      // RTL\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      }\n      // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  });\n\n  // ========================= Focus =========================\n  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(),\n    _useState12 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState11, 2),\n    focusKey = _useState12[0],\n    setFocusKey = _useState12[1];\n  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false),\n    _useState14 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState13, 2),\n    isMouse = _useState14[0],\n    setIsMouse = _useState14[1];\n  var enabledTabs = tabs.filter(function (tab) {\n    return !tab.disabled;\n  }).map(function (tab) {\n    return tab.key;\n  });\n  var onOffset = function onOffset(offset) {\n    var currentIndex = enabledTabs.indexOf(focusKey || activeKey);\n    var len = enabledTabs.length;\n    var nextIndex = (currentIndex + offset + len) % len;\n    var newKey = enabledTabs[nextIndex];\n    setFocusKey(newKey);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    var code = e.code;\n    var isRTL = rtl && tabPositionTopOrBottom;\n    var firstEnabledTab = enabledTabs[0];\n    var lastEnabledTab = enabledTabs[enabledTabs.length - 1];\n    switch (code) {\n      // LEFT\n      case 'ArrowLeft':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? 1 : -1);\n          }\n          break;\n        }\n\n      // RIGHT\n      case 'ArrowRight':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? -1 : 1);\n          }\n          break;\n        }\n\n      // UP\n      case 'ArrowUp':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(-1);\n          }\n          break;\n        }\n\n      // DOWN\n      case 'ArrowDown':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(1);\n          }\n          break;\n        }\n\n      // HOME\n      case 'Home':\n        {\n          e.preventDefault();\n          setFocusKey(firstEnabledTab);\n          break;\n        }\n\n      // END\n      case 'End':\n        {\n          e.preventDefault();\n          setFocusKey(lastEnabledTab);\n          break;\n        }\n\n      // Enter & Space\n      case 'Enter':\n      case 'Space':\n        {\n          e.preventDefault();\n          onTabClick(focusKey !== null && focusKey !== void 0 ? focusKey : activeKey, e);\n          break;\n        }\n      // Backspace\n      case 'Backspace':\n      case 'Delete':\n        {\n          var removeIndex = enabledTabs.indexOf(focusKey);\n          var removeTab = tabs.find(function (tab) {\n            return tab.key === focusKey;\n          });\n          var removable = (0,_util__WEBPACK_IMPORTED_MODULE_17__.getRemovable)(removeTab === null || removeTab === void 0 ? void 0 : removeTab.closable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.closeIcon, editable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.disabled);\n          if (removable) {\n            e.preventDefault();\n            e.stopPropagation();\n            editable.onEdit('remove', {\n              key: focusKey,\n              event: e\n            });\n            // when remove last tab, focus previous tab\n            if (removeIndex === enabledTabs.length - 1) {\n              onOffset(-1);\n            } else {\n              onOffset(1);\n            }\n          }\n          break;\n        }\n    }\n  };\n\n  // ========================== Tab ==========================\n  var tabNodeStyle = {};\n  if (tabPositionTopOrBottom) {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_TabNode__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      focus: key === focusKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      tabCount: enabledTabs.length,\n      currentPosition: i + 1,\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onKeyDown: handleKeyDown,\n      onFocus: function onFocus() {\n        if (!isMouse) {\n          setFocusKey(key);\n        }\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        }\n        // Focus element will make scrollLeft change which we should reset back\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      },\n      onBlur: function onBlur() {\n        setFocusKey(undefined);\n      },\n      onMouseDown: function onMouseDown() {\n        setIsMouse(true);\n      },\n      onMouseUp: function onMouseUp() {\n        setIsMouse(false);\n      }\n    });\n  });\n\n  // Update buttons records\n  var updateTabSizes = function updateTabSizes() {\n    return setTabSizes(function () {\n      var _tabListRef$current;\n      var newSizes = new Map();\n      var listRect = (_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.getBoundingClientRect();\n      tabs.forEach(function (_ref2) {\n        var _tabListRef$current2;\n        var key = _ref2.key;\n        var btnNode = (_tabListRef$current2 = tabListRef.current) === null || _tabListRef$current2 === void 0 ? void 0 : _tabListRef$current2.querySelector(\"[data-node-key=\\\"\".concat((0,_util__WEBPACK_IMPORTED_MODULE_17__.genDataNodeKey)(key), \"\\\"]\"));\n        if (btnNode) {\n          var _getTabSize = getTabSize(btnNode, listRect),\n            _getTabSize2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_getTabSize, 4),\n            width = _getTabSize2[0],\n            height = _getTabSize2[1],\n            left = _getTabSize2[2],\n            top = _getTabSize2[3];\n          newSizes.set(key, {\n            width: width,\n            height: height,\n            left: left,\n            top: top\n          });\n        }\n      });\n      return newSizes;\n    });\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    updateTabSizes();\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]);\n  var onListHolderResize = (0,_hooks_useUpdate__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize);\n\n    // Which includes add button size\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]);\n\n    // Update buttons records\n    updateTabSizes();\n  });\n\n  // ======================== Dropdown =======================\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(startHiddenTabs), (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(endHiddenTabs));\n\n  // =================== Link & Operations ===================\n  var activeTabOffset = tabOffsets.get(activeKey);\n  var _useIndicator = (0,_hooks_useIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({\n      activeTabOffset: activeTabOffset,\n      horizontal: tabPositionTopOrBottom,\n      indicator: indicator,\n      rtl: rtl\n    }),\n    indicatorStyle = _useIndicator.style;\n\n  // ========================= Effect ========================\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    scrollToTab();\n  }, [activeKey, transformMin, transformMax, (0,_util__WEBPACK_IMPORTED_MODULE_17__.stringify)(activeTabOffset), (0,_util__WEBPACK_IMPORTED_MODULE_17__.stringify)(tabOffsets), tabPositionTopOrBottom]);\n\n  // Should recalculate when rtl changed\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    onListHolderResize();\n    // eslint-disable-next-line\n  }, [rtl]);\n\n  // ========================= Render ========================\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft !== transformMax;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = transformLeft !== transformMin;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = transformTop !== transformMin;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", {\n    ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_8__.useComposeRef)(ref, containerRef),\n    role: \"tablist\",\n    \"aria-orientation\": tabPositionTopOrBottom ? 'horizontal' : 'vertical',\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ExtraContent__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(wrapPrefix, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_AddButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(\"\".concat(prefixCls, \"-ink-bar\"), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: indicatorStyle\n  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_OperationNode__WEBPACK_IMPORTED_MODULE_20__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ExtraContent__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabNavList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabPanelList/TabPane.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabPanelList/TabPane.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar TabPane = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    id = props.id,\n    active = props.active,\n    tabKey = props.tabKey,\n    children = props.children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classnames__WEBPACK_IMPORTED_MODULE_0___default()(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\nif (true) {\n  TabPane.displayName = 'TabPane';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabPane);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJQYW5lbExpc3QvVGFiUGFuZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFvQztBQUNMO0FBQy9CLDJCQUEyQiw2Q0FBZ0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsZ0RBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsaURBQVU7QUFDekI7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNELElBQUksSUFBcUM7QUFDekM7QUFDQTtBQUNBLGlFQUFlLE9BQU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGZlbmVyYmFoY2UtZm9ybVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLXRhYnNcXGVzXFxUYWJQYW5lbExpc3RcXFRhYlBhbmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNsYXNzTmFtZXMgZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG52YXIgVGFiUGFuZSA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKGZ1bmN0aW9uIChwcm9wcywgcmVmKSB7XG4gIHZhciBwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgY2xhc3NOYW1lID0gcHJvcHMuY2xhc3NOYW1lLFxuICAgIHN0eWxlID0gcHJvcHMuc3R5bGUsXG4gICAgaWQgPSBwcm9wcy5pZCxcbiAgICBhY3RpdmUgPSBwcm9wcy5hY3RpdmUsXG4gICAgdGFiS2V5ID0gcHJvcHMudGFiS2V5LFxuICAgIGNoaWxkcmVuID0gcHJvcHMuY2hpbGRyZW47XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgaWQ6IGlkICYmIFwiXCIuY29uY2F0KGlkLCBcIi1wYW5lbC1cIikuY29uY2F0KHRhYktleSksXG4gICAgcm9sZTogXCJ0YWJwYW5lbFwiLFxuICAgIHRhYkluZGV4OiBhY3RpdmUgPyAwIDogLTEsXG4gICAgXCJhcmlhLWxhYmVsbGVkYnlcIjogaWQgJiYgXCJcIi5jb25jYXQoaWQsIFwiLXRhYi1cIikuY29uY2F0KHRhYktleSksXG4gICAgXCJhcmlhLWhpZGRlblwiOiAhYWN0aXZlLFxuICAgIHN0eWxlOiBzdHlsZSxcbiAgICBjbGFzc05hbWU6IGNsYXNzTmFtZXMocHJlZml4Q2xzLCBhY3RpdmUgJiYgXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1hY3RpdmVcIiksIGNsYXNzTmFtZSksXG4gICAgcmVmOiByZWZcbiAgfSwgY2hpbGRyZW4pO1xufSk7XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBUYWJQYW5lLmRpc3BsYXlOYW1lID0gJ1RhYlBhbmUnO1xufVxuZXhwb3J0IGRlZmF1bHQgVGFiUGFuZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabPanelList/TabPane.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabPanelList/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabPanelList/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _TabContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../TabContext */ \"(ssr)/./node_modules/rc-tabs/es/TabContext.js\");\n/* harmony import */ var _TabPane__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TabPane */ \"(ssr)/./node_modules/rc-tabs/es/TabPanelList/TabPane.js\");\n\n\n\n\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\", \"destroyInactiveTabPane\"];\n\n\n\n\n\nvar TabPanelList = function TabPanelList(props) {\n  var id = props.id,\n    activeKey = props.activeKey,\n    animated = props.animated,\n    tabPosition = props.tabPosition,\n    destroyInactiveTabPane = props.destroyInactiveTabPane;\n  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_6__.useContext(_TabContext__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (item) {\n    var key = item.key,\n      forceRender = item.forceRender,\n      paneStyle = item.style,\n      paneClassName = item.className,\n      itemDestroyInactiveTabPane = item.destroyInactiveTabPane,\n      restTabProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(item, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(rc_motion__WEBPACK_IMPORTED_MODULE_5__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!(destroyInactiveTabPane || itemDestroyInactiveTabPane),\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref, ref) {\n      var motionStyle = _ref.style,\n        motionClassName = _ref.className;\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_TabPane__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, paneStyle), motionStyle),\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabPanelList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabPanelList/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/Tabs.js":
/*!*****************************************!*\
  !*** ./node_modules/rc-tabs/es/Tabs.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_isMobile__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/isMobile */ \"(ssr)/./node_modules/rc-util/es/isMobile.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _TabContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./TabContext */ \"(ssr)/./node_modules/rc-tabs/es/TabContext.js\");\n/* harmony import */ var _TabNavList_Wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./TabNavList/Wrapper */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/Wrapper.js\");\n/* harmony import */ var _TabPanelList__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TabPanelList */ \"(ssr)/./node_modules/rc-tabs/es/TabPanelList/index.js\");\n/* harmony import */ var _hooks_useAnimateConfig__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useAnimateConfig */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useAnimateConfig.js\");\n\n\n\n\n\n\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"items\", \"direction\", \"activeKey\", \"defaultActiveKey\", \"editable\", \"animated\", \"tabPosition\", \"tabBarGutter\", \"tabBarStyle\", \"tabBarExtraContent\", \"locale\", \"more\", \"destroyInactiveTabPane\", \"renderTabBar\", \"onChange\", \"onTabClick\", \"onTabScroll\", \"getPopupContainer\", \"popupClassName\", \"indicator\"];\n// Accessibility https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/Tab_Role\n\n\n\n\n\n\n\n\n\n/**\n * Should added antd:\n * - type\n *\n * Removed:\n * - onNextClick\n * - onPrevClick\n * - keyboard\n */\n\n// Used for accessibility\nvar uuid = 0;\nvar Tabs = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tabs' : _props$prefixCls,\n    className = props.className,\n    items = props.items,\n    direction = props.direction,\n    activeKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    editable = props.editable,\n    animated = props.animated,\n    _props$tabPosition = props.tabPosition,\n    tabPosition = _props$tabPosition === void 0 ? 'top' : _props$tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    tabBarStyle = props.tabBarStyle,\n    tabBarExtraContent = props.tabBarExtraContent,\n    locale = props.locale,\n    more = props.more,\n    destroyInactiveTabPane = props.destroyInactiveTabPane,\n    renderTabBar = props.renderTabBar,\n    onChange = props.onChange,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName,\n    indicator = props.indicator,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(props, _excluded);\n  var tabs = react__WEBPACK_IMPORTED_MODULE_9__.useMemo(function () {\n    return (items || []).filter(function (item) {\n      return item && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(item) === 'object' && 'key' in item;\n    });\n  }, [items]);\n  var rtl = direction === 'rtl';\n  var mergedAnimated = (0,_hooks_useAnimateConfig__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(animated);\n\n  // ======================== Mobile ========================\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    // Only update on the client side\n    setMobile((0,rc_util_es_isMobile__WEBPACK_IMPORTED_MODULE_8__[\"default\"])());\n  }, []);\n\n  // ====================== Active Key ======================\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n      var _tabs$;\n      return (_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key;\n    }, {\n      value: activeKey,\n      defaultValue: defaultActiveKey\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_useMergedState, 2),\n    mergedActiveKey = _useMergedState2[0],\n    setMergedActiveKey = _useMergedState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(function () {\n      return tabs.findIndex(function (tab) {\n        return tab.key === mergedActiveKey;\n      });\n    }),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_useState3, 2),\n    activeIndex = _useState4[0],\n    setActiveIndex = _useState4[1];\n\n  // Reset active key if not exist anymore\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    var newActiveIndex = tabs.findIndex(function (tab) {\n      return tab.key === mergedActiveKey;\n    });\n    if (newActiveIndex === -1) {\n      var _tabs$newActiveIndex;\n      newActiveIndex = Math.max(0, Math.min(activeIndex, tabs.length - 1));\n      setMergedActiveKey((_tabs$newActiveIndex = tabs[newActiveIndex]) === null || _tabs$newActiveIndex === void 0 ? void 0 : _tabs$newActiveIndex.key);\n    }\n    setActiveIndex(newActiveIndex);\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), mergedActiveKey, activeIndex]);\n\n  // ===================== Accessibility ====================\n  var _useMergedState3 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(null, {\n      value: id\n    }),\n    _useMergedState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_useMergedState3, 2),\n    mergedId = _useMergedState4[0],\n    setMergedId = _useMergedState4[1];\n\n  // Async generate id to avoid ssr mapping failed\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    if (!id) {\n      setMergedId(\"rc-tabs-\".concat( false ? 0 : uuid));\n      uuid += 1;\n    }\n  }, []);\n\n  // ======================== Events ========================\n  function onInternalTabClick(key, e) {\n    onTabClick === null || onTabClick === void 0 || onTabClick(key, e);\n    var isActiveChanged = key !== mergedActiveKey;\n    setMergedActiveKey(key);\n    if (isActiveChanged) {\n      onChange === null || onChange === void 0 || onChange(key);\n    }\n  }\n\n  // ======================== Render ========================\n  var sharedProps = {\n    id: mergedId,\n    activeKey: mergedActiveKey,\n    animated: mergedAnimated,\n    tabPosition: tabPosition,\n    rtl: rtl,\n    mobile: mobile\n  };\n  var tabNavBarProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, sharedProps), {}, {\n    editable: editable,\n    locale: locale,\n    more: more,\n    tabBarGutter: tabBarGutter,\n    onTabClick: onInternalTabClick,\n    onTabScroll: onTabScroll,\n    extra: tabBarExtraContent,\n    style: tabBarStyle,\n    panes: null,\n    getPopupContainer: getPopupContainer,\n    popupClassName: popupClassName,\n    indicator: indicator\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_TabContext__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Provider, {\n    value: {\n      tabs: tabs,\n      prefixCls: prefixCls\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref,\n    id: id,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixCls, \"\".concat(prefixCls, \"-\").concat(tabPosition), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-mobile\"), mobile), \"\".concat(prefixCls, \"-editable\"), editable), \"\".concat(prefixCls, \"-rtl\"), rtl), className)\n  }, restProps), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_TabNavList_Wrapper__WEBPACK_IMPORTED_MODULE_11__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, tabNavBarProps, {\n    renderTabBar: renderTabBar\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_TabPanelList__WEBPACK_IMPORTED_MODULE_12__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    destroyInactiveTabPane: destroyInactiveTabPane\n  }, sharedProps, {\n    animated: mergedAnimated\n  }))));\n});\nif (true) {\n  Tabs.displayName = 'Tabs';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Tabs);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/Tabs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useAnimateConfig.js":
/*!***********************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useAnimateConfig.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAnimateConfig)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n\n\n\nfunction useAnimateConfig() {\n  var animated = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    inkBar: true,\n    tabPane: false\n  };\n  var mergedAnimated;\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: false\n    };\n  } else {\n    mergedAnimated = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n      inkBar: true\n    }, (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(animated) === 'object' ? animated : {});\n  }\n\n  // Enable tabPane animation if provide motion\n  if (mergedAnimated.tabPaneMotion && mergedAnimated.tabPane === undefined) {\n    mergedAnimated.tabPane = true;\n  }\n  if (!mergedAnimated.tabPaneMotion && mergedAnimated.tabPane) {\n    if (true) {\n      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(false, '`animated.tabPane` is true but `animated.tabPaneMotion` is not provided. Motion will not work.');\n    }\n    mergedAnimated.tabPane = false;\n  }\n  return mergedAnimated;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useAnimateConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useIndicator.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useIndicator.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar useIndicator = function useIndicator(options) {\n  var activeTabOffset = options.activeTabOffset,\n    horizontal = options.horizontal,\n    rtl = options.rtl,\n    _options$indicator = options.indicator,\n    indicator = _options$indicator === void 0 ? {} : _options$indicator;\n  var size = indicator.size,\n    _indicator$align = indicator.align,\n    align = _indicator$align === void 0 ? 'center' : _indicator$align;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    inkStyle = _useState2[0],\n    setInkStyle = _useState2[1];\n  var inkBarRafRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n  var getLength = react__WEBPACK_IMPORTED_MODULE_2___default().useCallback(function (origin) {\n    if (typeof size === 'function') {\n      return size(origin);\n    }\n    if (typeof size === 'number') {\n      return size;\n    }\n    return origin;\n  }, [size]);\n\n  // Delay set ink style to avoid remove tab blink\n  function cleanInkBarRaf() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__[\"default\"].cancel(inkBarRafRef.current);\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (horizontal) {\n        newInkStyle.width = getLength(activeTabOffset.width);\n        var key = rtl ? 'right' : 'left';\n        if (align === 'start') {\n          newInkStyle[key] = activeTabOffset[key];\n        }\n        if (align === 'center') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width / 2;\n          newInkStyle.transform = rtl ? 'translateX(50%)' : 'translateX(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width;\n          newInkStyle.transform = 'translateX(-100%)';\n        }\n      } else {\n        newInkStyle.height = getLength(activeTabOffset.height);\n        if (align === 'start') {\n          newInkStyle.top = activeTabOffset.top;\n        }\n        if (align === 'center') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height / 2;\n          newInkStyle.transform = 'translateY(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height;\n          newInkStyle.transform = 'translateY(-100%)';\n        }\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () {\n      // Avoid jitter caused by tiny numerical differences\n      // fix https://github.com/ant-design/ant-design/issues/53378\n      var isEqual = inkStyle && newInkStyle && Object.keys(newInkStyle).every(function (key) {\n        var newValue = newInkStyle[key];\n        var oldValue = inkStyle[key];\n        return typeof newValue === 'number' && typeof oldValue === 'number' ? Math.round(newValue) === Math.round(oldValue) : newValue === oldValue;\n      });\n      if (!isEqual) {\n        setInkStyle(newInkStyle);\n      }\n    });\n    return cleanInkBarRaf;\n  }, [JSON.stringify(activeTabOffset), horizontal, rtl, align, getLength]);\n  return {\n    style: inkStyle\n  };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIndicator);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useIndicator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useOffsets.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useOffsets.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useOffsets)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0\n};\nfunction useOffsets(tabs, tabSizes, holderScrollWidth) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    var _tabs$;\n    var map = new Map();\n    var lastOffset = tabSizes.get((_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key) || DEFAULT_SIZE;\n    var rightOffset = lastOffset.left + lastOffset.width;\n    for (var i = 0; i < tabs.length; i += 1) {\n      var key = tabs[i].key;\n      var data = tabSizes.get(key);\n\n      // Reuse last one when not exist yet\n      if (!data) {\n        var _tabs;\n        data = tabSizes.get((_tabs = tabs[i - 1]) === null || _tabs === void 0 ? void 0 : _tabs.key) || DEFAULT_SIZE;\n      }\n      var entity = map.get(key) || (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, data);\n\n      // Right\n      entity.right = rightOffset - entity.left - entity.width;\n\n      // Update entity\n      map.set(key, entity);\n    }\n    return map;\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), tabSizes, holderScrollWidth]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useOffsets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useSyncState.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useSyncState.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSyncState)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useSyncState(defaultState, onChange) {\n  var stateRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(defaultState);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1__.useState({}),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  function setState(updater) {\n    var newValue = typeof updater === 'function' ? updater(stateRef.current) : updater;\n    if (newValue !== stateRef.current) {\n      onChange(newValue, stateRef.current);\n    }\n    stateRef.current = newValue;\n    forceUpdate({});\n  }\n  return [stateRef.current, setState];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9ob29rcy91c2VTeW5jU3RhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFzRTtBQUN2QztBQUNoQjtBQUNmLGlCQUFpQix5Q0FBWTtBQUM3Qix3QkFBd0IsMkNBQWMsR0FBRztBQUN6Qyx1QkFBdUIsb0ZBQWM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxmZW5lcmJhaGNlLWZvcm1cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxyYy10YWJzXFxlc1xcaG9va3NcXHVzZVN5bmNTdGF0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVN5bmNTdGF0ZShkZWZhdWx0U3RhdGUsIG9uQ2hhbmdlKSB7XG4gIHZhciBzdGF0ZVJlZiA9IFJlYWN0LnVzZVJlZihkZWZhdWx0U3RhdGUpO1xuICB2YXIgX1JlYWN0JHVzZVN0YXRlID0gUmVhY3QudXNlU3RhdGUoe30pLFxuICAgIF9SZWFjdCR1c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfUmVhY3QkdXNlU3RhdGUsIDIpLFxuICAgIGZvcmNlVXBkYXRlID0gX1JlYWN0JHVzZVN0YXRlMlsxXTtcbiAgZnVuY3Rpb24gc2V0U3RhdGUodXBkYXRlcikge1xuICAgIHZhciBuZXdWYWx1ZSA9IHR5cGVvZiB1cGRhdGVyID09PSAnZnVuY3Rpb24nID8gdXBkYXRlcihzdGF0ZVJlZi5jdXJyZW50KSA6IHVwZGF0ZXI7XG4gICAgaWYgKG5ld1ZhbHVlICE9PSBzdGF0ZVJlZi5jdXJyZW50KSB7XG4gICAgICBvbkNoYW5nZShuZXdWYWx1ZSwgc3RhdGVSZWYuY3VycmVudCk7XG4gICAgfVxuICAgIHN0YXRlUmVmLmN1cnJlbnQgPSBuZXdWYWx1ZTtcbiAgICBmb3JjZVVwZGF0ZSh7fSk7XG4gIH1cbiAgcmV0dXJuIFtzdGF0ZVJlZi5jdXJyZW50LCBzZXRTdGF0ZV07XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useSyncState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useTouchMove.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useTouchMove.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useTouchMove)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nvar MIN_SWIPE_DISTANCE = 0.1;\nvar STOP_SWIPE_DISTANCE = 0.01;\nvar REFRESH_INTERVAL = 20;\nvar SPEED_OFF_MULTIPLE = Math.pow(0.995, REFRESH_INTERVAL);\n\n// ================================= Hook =================================\nfunction useTouchMove(ref, onOffset) {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    touchPosition = _useState2[0],\n    setTouchPosition = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState3, 2),\n    lastTimestamp = _useState4[0],\n    setLastTimestamp = _useState4[1];\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState5, 2),\n    lastTimeDiff = _useState6[0],\n    setLastTimeDiff = _useState6[1];\n  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(),\n    _useState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState7, 2),\n    lastOffset = _useState8[0],\n    setLastOffset = _useState8[1];\n  var motionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n\n  // ========================= Events =========================\n  // >>> Touch events\n  function onTouchStart(e) {\n    var _e$touches$ = e.touches[0],\n      screenX = _e$touches$.screenX,\n      screenY = _e$touches$.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    window.clearInterval(motionRef.current);\n  }\n  function onTouchMove(e) {\n    if (!touchPosition) return;\n\n    // e.preventDefault();\n    var _e$touches$2 = e.touches[0],\n      screenX = _e$touches$2.screenX,\n      screenY = _e$touches$2.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    var offsetX = screenX - touchPosition.x;\n    var offsetY = screenY - touchPosition.y;\n    onOffset(offsetX, offsetY);\n    var now = Date.now();\n    setLastTimestamp(now);\n    setLastTimeDiff(now - lastTimestamp);\n    setLastOffset({\n      x: offsetX,\n      y: offsetY\n    });\n  }\n  function onTouchEnd() {\n    if (!touchPosition) return;\n    setTouchPosition(null);\n    setLastOffset(null);\n\n    // Swipe if needed\n    if (lastOffset) {\n      var distanceX = lastOffset.x / lastTimeDiff;\n      var distanceY = lastOffset.y / lastTimeDiff;\n      var absX = Math.abs(distanceX);\n      var absY = Math.abs(distanceY);\n\n      // Skip swipe if low distance\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) return;\n      var currentX = distanceX;\n      var currentY = distanceY;\n      motionRef.current = window.setInterval(function () {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(motionRef.current);\n          return;\n        }\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL);\n      }, REFRESH_INTERVAL);\n    }\n  }\n\n  // >>> Wheel event\n  var lastWheelDirectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  function onWheel(e) {\n    var deltaX = e.deltaX,\n      deltaY = e.deltaY;\n\n    // Convert both to x & y since wheel only happened on PC\n    var mixed = 0;\n    var absX = Math.abs(deltaX);\n    var absY = Math.abs(deltaY);\n    if (absX === absY) {\n      mixed = lastWheelDirectionRef.current === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      lastWheelDirectionRef.current = 'x';\n    } else {\n      mixed = deltaY;\n      lastWheelDirectionRef.current = 'y';\n    }\n    if (onOffset(-mixed, -mixed)) {\n      e.preventDefault();\n    }\n  }\n\n  // ========================= Effect =========================\n  var touchEventsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  touchEventsRef.current = {\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onWheel: onWheel\n  };\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {\n    function onProxyTouchStart(e) {\n      touchEventsRef.current.onTouchStart(e);\n    }\n    function onProxyTouchMove(e) {\n      touchEventsRef.current.onTouchMove(e);\n    }\n    function onProxyTouchEnd(e) {\n      touchEventsRef.current.onTouchEnd(e);\n    }\n    function onProxyWheel(e) {\n      touchEventsRef.current.onWheel(e);\n    }\n    document.addEventListener('touchmove', onProxyTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', onProxyTouchEnd, {\n      passive: true\n    });\n\n    // No need to clean up since element removed\n    ref.current.addEventListener('touchstart', onProxyTouchStart, {\n      passive: true\n    });\n    ref.current.addEventListener('wheel', onProxyWheel, {\n      passive: false\n    });\n    return function () {\n      document.removeEventListener('touchmove', onProxyTouchMove);\n      document.removeEventListener('touchend', onProxyTouchEnd);\n    };\n  }, []);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9ob29rcy91c2VUb3VjaE1vdmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFzRTtBQUN2QztBQUNVO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ2U7QUFDZixrQkFBa0IsK0NBQVE7QUFDMUIsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0E7QUFDQSxtQkFBbUIsK0NBQVE7QUFDM0IsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0E7QUFDQSxtQkFBbUIsK0NBQVE7QUFDM0IsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0E7QUFDQSxtQkFBbUIsK0NBQVE7QUFDM0IsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0E7QUFDQSxrQkFBa0IsNkNBQU07O0FBRXhCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7O0FBRUE7QUFDQSw4QkFBOEIsNkNBQU07QUFDcEM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsdUJBQXVCLDZDQUFNO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsNENBQWU7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcZmVuZXJiYWhjZS1mb3JtXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtdGFic1xcZXNcXGhvb2tzXFx1c2VUb3VjaE1vdmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xudmFyIE1JTl9TV0lQRV9ESVNUQU5DRSA9IDAuMTtcbnZhciBTVE9QX1NXSVBFX0RJU1RBTkNFID0gMC4wMTtcbnZhciBSRUZSRVNIX0lOVEVSVkFMID0gMjA7XG52YXIgU1BFRURfT0ZGX01VTFRJUExFID0gTWF0aC5wb3coMC45OTUsIFJFRlJFU0hfSU5URVJWQUwpO1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gSG9vayA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVRvdWNoTW92ZShyZWYsIG9uT2Zmc2V0KSB7XG4gIHZhciBfdXNlU3RhdGUgPSB1c2VTdGF0ZSgpLFxuICAgIF91c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUsIDIpLFxuICAgIHRvdWNoUG9zaXRpb24gPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldFRvdWNoUG9zaXRpb24gPSBfdXNlU3RhdGUyWzFdO1xuICB2YXIgX3VzZVN0YXRlMyA9IHVzZVN0YXRlKDApLFxuICAgIF91c2VTdGF0ZTQgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUzLCAyKSxcbiAgICBsYXN0VGltZXN0YW1wID0gX3VzZVN0YXRlNFswXSxcbiAgICBzZXRMYXN0VGltZXN0YW1wID0gX3VzZVN0YXRlNFsxXTtcbiAgdmFyIF91c2VTdGF0ZTUgPSB1c2VTdGF0ZSgwKSxcbiAgICBfdXNlU3RhdGU2ID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlNSwgMiksXG4gICAgbGFzdFRpbWVEaWZmID0gX3VzZVN0YXRlNlswXSxcbiAgICBzZXRMYXN0VGltZURpZmYgPSBfdXNlU3RhdGU2WzFdO1xuICB2YXIgX3VzZVN0YXRlNyA9IHVzZVN0YXRlKCksXG4gICAgX3VzZVN0YXRlOCA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0ZTcsIDIpLFxuICAgIGxhc3RPZmZzZXQgPSBfdXNlU3RhdGU4WzBdLFxuICAgIHNldExhc3RPZmZzZXQgPSBfdXNlU3RhdGU4WzFdO1xuICB2YXIgbW90aW9uUmVmID0gdXNlUmVmKCk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PSBFdmVudHMgPT09PT09PT09PT09PT09PT09PT09PT09PVxuICAvLyA+Pj4gVG91Y2ggZXZlbnRzXG4gIGZ1bmN0aW9uIG9uVG91Y2hTdGFydChlKSB7XG4gICAgdmFyIF9lJHRvdWNoZXMkID0gZS50b3VjaGVzWzBdLFxuICAgICAgc2NyZWVuWCA9IF9lJHRvdWNoZXMkLnNjcmVlblgsXG4gICAgICBzY3JlZW5ZID0gX2UkdG91Y2hlcyQuc2NyZWVuWTtcbiAgICBzZXRUb3VjaFBvc2l0aW9uKHtcbiAgICAgIHg6IHNjcmVlblgsXG4gICAgICB5OiBzY3JlZW5ZXG4gICAgfSk7XG4gICAgd2luZG93LmNsZWFySW50ZXJ2YWwobW90aW9uUmVmLmN1cnJlbnQpO1xuICB9XG4gIGZ1bmN0aW9uIG9uVG91Y2hNb3ZlKGUpIHtcbiAgICBpZiAoIXRvdWNoUG9zaXRpb24pIHJldHVybjtcblxuICAgIC8vIGUucHJldmVudERlZmF1bHQoKTtcbiAgICB2YXIgX2UkdG91Y2hlcyQyID0gZS50b3VjaGVzWzBdLFxuICAgICAgc2NyZWVuWCA9IF9lJHRvdWNoZXMkMi5zY3JlZW5YLFxuICAgICAgc2NyZWVuWSA9IF9lJHRvdWNoZXMkMi5zY3JlZW5ZO1xuICAgIHNldFRvdWNoUG9zaXRpb24oe1xuICAgICAgeDogc2NyZWVuWCxcbiAgICAgIHk6IHNjcmVlbllcbiAgICB9KTtcbiAgICB2YXIgb2Zmc2V0WCA9IHNjcmVlblggLSB0b3VjaFBvc2l0aW9uLng7XG4gICAgdmFyIG9mZnNldFkgPSBzY3JlZW5ZIC0gdG91Y2hQb3NpdGlvbi55O1xuICAgIG9uT2Zmc2V0KG9mZnNldFgsIG9mZnNldFkpO1xuICAgIHZhciBub3cgPSBEYXRlLm5vdygpO1xuICAgIHNldExhc3RUaW1lc3RhbXAobm93KTtcbiAgICBzZXRMYXN0VGltZURpZmYobm93IC0gbGFzdFRpbWVzdGFtcCk7XG4gICAgc2V0TGFzdE9mZnNldCh7XG4gICAgICB4OiBvZmZzZXRYLFxuICAgICAgeTogb2Zmc2V0WVxuICAgIH0pO1xuICB9XG4gIGZ1bmN0aW9uIG9uVG91Y2hFbmQoKSB7XG4gICAgaWYgKCF0b3VjaFBvc2l0aW9uKSByZXR1cm47XG4gICAgc2V0VG91Y2hQb3NpdGlvbihudWxsKTtcbiAgICBzZXRMYXN0T2Zmc2V0KG51bGwpO1xuXG4gICAgLy8gU3dpcGUgaWYgbmVlZGVkXG4gICAgaWYgKGxhc3RPZmZzZXQpIHtcbiAgICAgIHZhciBkaXN0YW5jZVggPSBsYXN0T2Zmc2V0LnggLyBsYXN0VGltZURpZmY7XG4gICAgICB2YXIgZGlzdGFuY2VZID0gbGFzdE9mZnNldC55IC8gbGFzdFRpbWVEaWZmO1xuICAgICAgdmFyIGFic1ggPSBNYXRoLmFicyhkaXN0YW5jZVgpO1xuICAgICAgdmFyIGFic1kgPSBNYXRoLmFicyhkaXN0YW5jZVkpO1xuXG4gICAgICAvLyBTa2lwIHN3aXBlIGlmIGxvdyBkaXN0YW5jZVxuICAgICAgaWYgKE1hdGgubWF4KGFic1gsIGFic1kpIDwgTUlOX1NXSVBFX0RJU1RBTkNFKSByZXR1cm47XG4gICAgICB2YXIgY3VycmVudFggPSBkaXN0YW5jZVg7XG4gICAgICB2YXIgY3VycmVudFkgPSBkaXN0YW5jZVk7XG4gICAgICBtb3Rpb25SZWYuY3VycmVudCA9IHdpbmRvdy5zZXRJbnRlcnZhbChmdW5jdGlvbiAoKSB7XG4gICAgICAgIGlmIChNYXRoLmFicyhjdXJyZW50WCkgPCBTVE9QX1NXSVBFX0RJU1RBTkNFICYmIE1hdGguYWJzKGN1cnJlbnRZKSA8IFNUT1BfU1dJUEVfRElTVEFOQ0UpIHtcbiAgICAgICAgICB3aW5kb3cuY2xlYXJJbnRlcnZhbChtb3Rpb25SZWYuY3VycmVudCk7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGN1cnJlbnRYICo9IFNQRUVEX09GRl9NVUxUSVBMRTtcbiAgICAgICAgY3VycmVudFkgKj0gU1BFRURfT0ZGX01VTFRJUExFO1xuICAgICAgICBvbk9mZnNldChjdXJyZW50WCAqIFJFRlJFU0hfSU5URVJWQUwsIGN1cnJlbnRZICogUkVGUkVTSF9JTlRFUlZBTCk7XG4gICAgICB9LCBSRUZSRVNIX0lOVEVSVkFMKTtcbiAgICB9XG4gIH1cblxuICAvLyA+Pj4gV2hlZWwgZXZlbnRcbiAgdmFyIGxhc3RXaGVlbERpcmVjdGlvblJlZiA9IHVzZVJlZigpO1xuICBmdW5jdGlvbiBvbldoZWVsKGUpIHtcbiAgICB2YXIgZGVsdGFYID0gZS5kZWx0YVgsXG4gICAgICBkZWx0YVkgPSBlLmRlbHRhWTtcblxuICAgIC8vIENvbnZlcnQgYm90aCB0byB4ICYgeSBzaW5jZSB3aGVlbCBvbmx5IGhhcHBlbmVkIG9uIFBDXG4gICAgdmFyIG1peGVkID0gMDtcbiAgICB2YXIgYWJzWCA9IE1hdGguYWJzKGRlbHRhWCk7XG4gICAgdmFyIGFic1kgPSBNYXRoLmFicyhkZWx0YVkpO1xuICAgIGlmIChhYnNYID09PSBhYnNZKSB7XG4gICAgICBtaXhlZCA9IGxhc3RXaGVlbERpcmVjdGlvblJlZi5jdXJyZW50ID09PSAneCcgPyBkZWx0YVggOiBkZWx0YVk7XG4gICAgfSBlbHNlIGlmIChhYnNYID4gYWJzWSkge1xuICAgICAgbWl4ZWQgPSBkZWx0YVg7XG4gICAgICBsYXN0V2hlZWxEaXJlY3Rpb25SZWYuY3VycmVudCA9ICd4JztcbiAgICB9IGVsc2Uge1xuICAgICAgbWl4ZWQgPSBkZWx0YVk7XG4gICAgICBsYXN0V2hlZWxEaXJlY3Rpb25SZWYuY3VycmVudCA9ICd5JztcbiAgICB9XG4gICAgaWYgKG9uT2Zmc2V0KC1taXhlZCwgLW1peGVkKSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIH1cbiAgfVxuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gRWZmZWN0ID09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIHRvdWNoRXZlbnRzUmVmID0gdXNlUmVmKG51bGwpO1xuICB0b3VjaEV2ZW50c1JlZi5jdXJyZW50ID0ge1xuICAgIG9uVG91Y2hTdGFydDogb25Ub3VjaFN0YXJ0LFxuICAgIG9uVG91Y2hNb3ZlOiBvblRvdWNoTW92ZSxcbiAgICBvblRvdWNoRW5kOiBvblRvdWNoRW5kLFxuICAgIG9uV2hlZWw6IG9uV2hlZWxcbiAgfTtcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBmdW5jdGlvbiBvblByb3h5VG91Y2hTdGFydChlKSB7XG4gICAgICB0b3VjaEV2ZW50c1JlZi5jdXJyZW50Lm9uVG91Y2hTdGFydChlKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gb25Qcm94eVRvdWNoTW92ZShlKSB7XG4gICAgICB0b3VjaEV2ZW50c1JlZi5jdXJyZW50Lm9uVG91Y2hNb3ZlKGUpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBvblByb3h5VG91Y2hFbmQoZSkge1xuICAgICAgdG91Y2hFdmVudHNSZWYuY3VycmVudC5vblRvdWNoRW5kKGUpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBvblByb3h5V2hlZWwoZSkge1xuICAgICAgdG91Y2hFdmVudHNSZWYuY3VycmVudC5vbldoZWVsKGUpO1xuICAgIH1cbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCd0b3VjaG1vdmUnLCBvblByb3h5VG91Y2hNb3ZlLCB7XG4gICAgICBwYXNzaXZlOiBmYWxzZVxuICAgIH0pO1xuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNoZW5kJywgb25Qcm94eVRvdWNoRW5kLCB7XG4gICAgICBwYXNzaXZlOiB0cnVlXG4gICAgfSk7XG5cbiAgICAvLyBObyBuZWVkIHRvIGNsZWFuIHVwIHNpbmNlIGVsZW1lbnQgcmVtb3ZlZFxuICAgIHJlZi5jdXJyZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNoc3RhcnQnLCBvblByb3h5VG91Y2hTdGFydCwge1xuICAgICAgcGFzc2l2ZTogdHJ1ZVxuICAgIH0pO1xuICAgIHJlZi5jdXJyZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3doZWVsJywgb25Qcm94eVdoZWVsLCB7XG4gICAgICBwYXNzaXZlOiBmYWxzZVxuICAgIH0pO1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCd0b3VjaG1vdmUnLCBvblByb3h5VG91Y2hNb3ZlKTtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3RvdWNoZW5kJywgb25Qcm94eVRvdWNoRW5kKTtcbiAgICB9O1xuICB9LCBbXSk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useTouchMove.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useUpdate.js":
/*!****************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useUpdate.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useUpdate),\n/* harmony export */   useUpdateState: () => (/* binding */ useUpdateState)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n/**\n * Help to merge callback with `useLayoutEffect`.\n * One time will only trigger once.\n */\nfunction useUpdate(callback) {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var effectRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n  var callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n  callbackRef.current = callback;\n\n  // Trigger on `useLayoutEffect`\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_1__.useLayoutUpdateEffect)(function () {\n    var _callbackRef$current;\n    (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 || _callbackRef$current.call(callbackRef);\n  }, [count]);\n\n  // Trigger to update count\n  return function () {\n    if (effectRef.current !== count) {\n      return;\n    }\n    effectRef.current += 1;\n    setCount(effectRef.current);\n  };\n}\nfunction useUpdateState(defaultState) {\n  var batchRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({}),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState3, 2),\n    forceUpdate = _useState4[1];\n  var state = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useUpdate(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n  return [state.current, updater];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useUpdate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useVisibleRange.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useVisibleRange.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useVisibleRange)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0,\n  right: 0\n};\nfunction useVisibleRange(tabOffsets, visibleTabContentValue, transform, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, _ref) {\n  var tabs = _ref.tabs,\n    tabPosition = _ref.tabPosition,\n    rtl = _ref.rtl;\n  var charUnit;\n  var position;\n  var transformSize;\n  if (['top', 'bottom'].includes(tabPosition)) {\n    charUnit = 'width';\n    position = rtl ? 'right' : 'left';\n    transformSize = Math.abs(transform);\n  } else {\n    charUnit = 'height';\n    position = 'top';\n    transformSize = -transform;\n  }\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    if (!tabs.length) {\n      return [0, 0];\n    }\n    var len = tabs.length;\n    var endIndex = len;\n    for (var i = 0; i < len; i += 1) {\n      var offset = tabOffsets.get(tabs[i].key) || DEFAULT_SIZE;\n      if (Math.floor(offset[position] + offset[charUnit]) > Math.floor(transformSize + visibleTabContentValue)) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n    var startIndex = 0;\n    for (var _i = len - 1; _i >= 0; _i -= 1) {\n      var _offset = tabOffsets.get(tabs[_i].key) || DEFAULT_SIZE;\n      if (_offset[position] < transformSize) {\n        startIndex = _i + 1;\n        break;\n      }\n    }\n    return startIndex >= endIndex ? [0, 0] : [startIndex, endIndex];\n  }, [tabOffsets, visibleTabContentValue, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, transformSize, tabPosition, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), rtl]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9ob29rcy91c2VWaXNpYmxlUmFuZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOENBQU87QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixTQUFTO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLFNBQVM7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGZlbmVyYmFoY2UtZm9ybVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLXRhYnNcXGVzXFxob29rc1xcdXNlVmlzaWJsZVJhbmdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG52YXIgREVGQVVMVF9TSVpFID0ge1xuICB3aWR0aDogMCxcbiAgaGVpZ2h0OiAwLFxuICBsZWZ0OiAwLFxuICB0b3A6IDAsXG4gIHJpZ2h0OiAwXG59O1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlVmlzaWJsZVJhbmdlKHRhYk9mZnNldHMsIHZpc2libGVUYWJDb250ZW50VmFsdWUsIHRyYW5zZm9ybSwgdGFiQ29udGVudFNpemVWYWx1ZSwgYWRkTm9kZVNpemVWYWx1ZSwgb3BlcmF0aW9uTm9kZVNpemVWYWx1ZSwgX3JlZikge1xuICB2YXIgdGFicyA9IF9yZWYudGFicyxcbiAgICB0YWJQb3NpdGlvbiA9IF9yZWYudGFiUG9zaXRpb24sXG4gICAgcnRsID0gX3JlZi5ydGw7XG4gIHZhciBjaGFyVW5pdDtcbiAgdmFyIHBvc2l0aW9uO1xuICB2YXIgdHJhbnNmb3JtU2l6ZTtcbiAgaWYgKFsndG9wJywgJ2JvdHRvbSddLmluY2x1ZGVzKHRhYlBvc2l0aW9uKSkge1xuICAgIGNoYXJVbml0ID0gJ3dpZHRoJztcbiAgICBwb3NpdGlvbiA9IHJ0bCA/ICdyaWdodCcgOiAnbGVmdCc7XG4gICAgdHJhbnNmb3JtU2l6ZSA9IE1hdGguYWJzKHRyYW5zZm9ybSk7XG4gIH0gZWxzZSB7XG4gICAgY2hhclVuaXQgPSAnaGVpZ2h0JztcbiAgICBwb3NpdGlvbiA9ICd0b3AnO1xuICAgIHRyYW5zZm9ybVNpemUgPSAtdHJhbnNmb3JtO1xuICB9XG4gIHJldHVybiB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoIXRhYnMubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gWzAsIDBdO1xuICAgIH1cbiAgICB2YXIgbGVuID0gdGFicy5sZW5ndGg7XG4gICAgdmFyIGVuZEluZGV4ID0gbGVuO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGVuOyBpICs9IDEpIHtcbiAgICAgIHZhciBvZmZzZXQgPSB0YWJPZmZzZXRzLmdldCh0YWJzW2ldLmtleSkgfHwgREVGQVVMVF9TSVpFO1xuICAgICAgaWYgKE1hdGguZmxvb3Iob2Zmc2V0W3Bvc2l0aW9uXSArIG9mZnNldFtjaGFyVW5pdF0pID4gTWF0aC5mbG9vcih0cmFuc2Zvcm1TaXplICsgdmlzaWJsZVRhYkNvbnRlbnRWYWx1ZSkpIHtcbiAgICAgICAgZW5kSW5kZXggPSBpIC0gMTtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuICAgIHZhciBzdGFydEluZGV4ID0gMDtcbiAgICBmb3IgKHZhciBfaSA9IGxlbiAtIDE7IF9pID49IDA7IF9pIC09IDEpIHtcbiAgICAgIHZhciBfb2Zmc2V0ID0gdGFiT2Zmc2V0cy5nZXQodGFic1tfaV0ua2V5KSB8fCBERUZBVUxUX1NJWkU7XG4gICAgICBpZiAoX29mZnNldFtwb3NpdGlvbl0gPCB0cmFuc2Zvcm1TaXplKSB7XG4gICAgICAgIHN0YXJ0SW5kZXggPSBfaSArIDE7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gc3RhcnRJbmRleCA+PSBlbmRJbmRleCA/IFswLCAwXSA6IFtzdGFydEluZGV4LCBlbmRJbmRleF07XG4gIH0sIFt0YWJPZmZzZXRzLCB2aXNpYmxlVGFiQ29udGVudFZhbHVlLCB0YWJDb250ZW50U2l6ZVZhbHVlLCBhZGROb2RlU2l6ZVZhbHVlLCBvcGVyYXRpb25Ob2RlU2l6ZVZhbHVlLCB0cmFuc2Zvcm1TaXplLCB0YWJQb3NpdGlvbiwgdGFicy5tYXAoZnVuY3Rpb24gKHRhYikge1xuICAgIHJldHVybiB0YWIua2V5O1xuICB9KS5qb2luKCdfJyksIHJ0bF0pO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useVisibleRange.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/index.js":
/*!******************************************!*\
  !*** ./node_modules/rc-tabs/es/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Tabs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tabs */ \"(ssr)/./node_modules/rc-tabs/es/Tabs.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Tabs__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUMxQixpRUFBZSw2Q0FBSSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcZmVuZXJiYWhjZS1mb3JtXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtdGFic1xcZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBUYWJzIGZyb20gXCIuL1RhYnNcIjtcbmV4cG9ydCBkZWZhdWx0IFRhYnM7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/util.js":
/*!*****************************************!*\
  !*** ./node_modules/rc-tabs/es/util.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genDataNodeKey: () => (/* binding */ genDataNodeKey),\n/* harmony export */   getRemovable: () => (/* binding */ getRemovable),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/**\n * We trade Map as deps which may change with same value but different ref object.\n * We should make it as hash for deps\n * */\nfunction stringify(obj) {\n  var tgt;\n  if (obj instanceof Map) {\n    tgt = {};\n    obj.forEach(function (v, k) {\n      tgt[k] = v;\n    });\n  } else {\n    tgt = obj;\n  }\n  return JSON.stringify(tgt);\n}\nvar RC_TABS_DOUBLE_QUOTE = 'TABS_DQ';\nfunction genDataNodeKey(key) {\n  return String(key).replace(/\"/g, RC_TABS_DOUBLE_QUOTE);\n}\nfunction getRemovable(closable, closeIcon, editable, disabled) {\n  if (\n  // Only editable tabs can be removed\n  !editable ||\n  // Tabs cannot be removed when disabled\n  disabled ||\n  // closable is false\n  closable === false ||\n  // If closable is undefined, the remove button should be hidden when closeIcon is null or false\n  closable === undefined && (closeIcon === false || closeIcon === null)) {\n    return false;\n  }\n  return true;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy91dGlsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcZmVuZXJiYWhjZS1mb3JtXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtdGFic1xcZXNcXHV0aWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBXZSB0cmFkZSBNYXAgYXMgZGVwcyB3aGljaCBtYXkgY2hhbmdlIHdpdGggc2FtZSB2YWx1ZSBidXQgZGlmZmVyZW50IHJlZiBvYmplY3QuXG4gKiBXZSBzaG91bGQgbWFrZSBpdCBhcyBoYXNoIGZvciBkZXBzXG4gKiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cmluZ2lmeShvYmopIHtcbiAgdmFyIHRndDtcbiAgaWYgKG9iaiBpbnN0YW5jZW9mIE1hcCkge1xuICAgIHRndCA9IHt9O1xuICAgIG9iai5mb3JFYWNoKGZ1bmN0aW9uICh2LCBrKSB7XG4gICAgICB0Z3Rba10gPSB2O1xuICAgIH0pO1xuICB9IGVsc2Uge1xuICAgIHRndCA9IG9iajtcbiAgfVxuICByZXR1cm4gSlNPTi5zdHJpbmdpZnkodGd0KTtcbn1cbnZhciBSQ19UQUJTX0RPVUJMRV9RVU9URSA9ICdUQUJTX0RRJztcbmV4cG9ydCBmdW5jdGlvbiBnZW5EYXRhTm9kZUtleShrZXkpIHtcbiAgcmV0dXJuIFN0cmluZyhrZXkpLnJlcGxhY2UoL1wiL2csIFJDX1RBQlNfRE9VQkxFX1FVT1RFKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBnZXRSZW1vdmFibGUoY2xvc2FibGUsIGNsb3NlSWNvbiwgZWRpdGFibGUsIGRpc2FibGVkKSB7XG4gIGlmIChcbiAgLy8gT25seSBlZGl0YWJsZSB0YWJzIGNhbiBiZSByZW1vdmVkXG4gICFlZGl0YWJsZSB8fFxuICAvLyBUYWJzIGNhbm5vdCBiZSByZW1vdmVkIHdoZW4gZGlzYWJsZWRcbiAgZGlzYWJsZWQgfHxcbiAgLy8gY2xvc2FibGUgaXMgZmFsc2VcbiAgY2xvc2FibGUgPT09IGZhbHNlIHx8XG4gIC8vIElmIGNsb3NhYmxlIGlzIHVuZGVmaW5lZCwgdGhlIHJlbW92ZSBidXR0b24gc2hvdWxkIGJlIGhpZGRlbiB3aGVuIGNsb3NlSWNvbiBpcyBudWxsIG9yIGZhbHNlXG4gIGNsb3NhYmxlID09PSB1bmRlZmluZWQgJiYgKGNsb3NlSWNvbiA9PT0gZmFsc2UgfHwgY2xvc2VJY29uID09PSBudWxsKSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/util.js\n");

/***/ })

};
;