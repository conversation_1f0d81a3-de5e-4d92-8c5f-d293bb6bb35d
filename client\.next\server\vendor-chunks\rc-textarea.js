"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-textarea";
exports.ids = ["vendor-chunks/rc-textarea"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-textarea/es/ResizableTextArea.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./calculateNodeHeight */ \"(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\");\n\n\n\n\n\n\nvar _excluded = [\"prefixCls\", \"defaultValue\", \"value\", \"autoSize\", \"onResize\", \"className\", \"style\", \"disabled\", \"onChange\", \"onInternalAutoSize\"];\n\n\n\n\n\n\n\nvar RESIZE_START = 0;\nvar RESIZE_MEASURING = 1;\nvar RESIZE_STABLE = 2;\nvar ResizableTextArea = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.forwardRef(function (props, ref) {\n  var _ref = props,\n    prefixCls = _ref.prefixCls,\n    defaultValue = _ref.defaultValue,\n    value = _ref.value,\n    autoSize = _ref.autoSize,\n    onResize = _ref.onResize,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    onChange = _ref.onChange,\n    onInternalAutoSize = _ref.onInternalAutoSize,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n\n  // =============================== Value ================================\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(defaultValue, {\n      value: value,\n      postState: function postState(val) {\n        return val !== null && val !== void 0 ? val : '';\n      }\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setMergedValue = _useMergedState2[1];\n  var onInternalChange = function onInternalChange(event) {\n    setMergedValue(event.target.value);\n    onChange === null || onChange === void 0 || onChange(event);\n  };\n\n  // ================================ Ref =================================\n  var textareaRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n  react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle(ref, function () {\n    return {\n      textArea: textareaRef.current\n    };\n  });\n\n  // ============================== AutoSize ==============================\n  var _React$useMemo = react__WEBPACK_IMPORTED_MODULE_11__.useMemo(function () {\n      if (autoSize && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(autoSize) === 'object') {\n        return [autoSize.minRows, autoSize.maxRows];\n      }\n      return [];\n    }, [autoSize]),\n    _React$useMemo2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useMemo, 2),\n    minRows = _React$useMemo2[0],\n    maxRows = _React$useMemo2[1];\n  var needAutoSize = !!autoSize;\n\n  // =============================== Scroll ===============================\n  // https://github.com/ant-design/ant-design/issues/21870\n  var fixFirefoxAutoScroll = function fixFirefoxAutoScroll() {\n    try {\n      // FF has bug with jump of scroll to top. We force back here.\n      if (document.activeElement === textareaRef.current) {\n        var _textareaRef$current = textareaRef.current,\n          selectionStart = _textareaRef$current.selectionStart,\n          selectionEnd = _textareaRef$current.selectionEnd,\n          scrollTop = _textareaRef$current.scrollTop;\n\n        // Fix Safari bug which not rollback when break line\n        // This makes Chinese IME can't input. Do not fix this\n        // const { value: tmpValue } = textareaRef.current;\n        // textareaRef.current.value = '';\n        // textareaRef.current.value = tmpValue;\n\n        textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n        textareaRef.current.scrollTop = scrollTop;\n      }\n    } catch (e) {\n      // Fix error in Chrome:\n      // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n      // http://stackoverflow.com/q/21177489/3040605\n    }\n  };\n\n  // =============================== Resize ===============================\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_11__.useState(RESIZE_STABLE),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2),\n    resizeState = _React$useState2[0],\n    setResizeState = _React$useState2[1];\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11__.useState(),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2),\n    autoSizeStyle = _React$useState4[0],\n    setAutoSizeStyle = _React$useState4[1];\n  var startResize = function startResize() {\n    setResizeState(RESIZE_START);\n    if (false) {}\n  };\n\n  // Change to trigger resize measure\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function () {\n    if (needAutoSize) {\n      startResize();\n    }\n  }, [value, minRows, maxRows, needAutoSize]);\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function () {\n    if (resizeState === RESIZE_START) {\n      setResizeState(RESIZE_MEASURING);\n    } else if (resizeState === RESIZE_MEASURING) {\n      var textareaStyles = (0,_calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(textareaRef.current, false, minRows, maxRows);\n\n      // Safari has bug that text will keep break line on text cut when it's prev is break line.\n      // ZombieJ: This not often happen. So we just skip it.\n      // const { selectionStart, selectionEnd, scrollTop } = textareaRef.current;\n      // const { value: tmpValue } = textareaRef.current;\n      // textareaRef.current.value = '';\n      // textareaRef.current.value = tmpValue;\n\n      // if (document.activeElement === textareaRef.current) {\n      //   textareaRef.current.scrollTop = scrollTop;\n      //   textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n      // }\n\n      setResizeState(RESIZE_STABLE);\n      setAutoSizeStyle(textareaStyles);\n    } else {\n      fixFirefoxAutoScroll();\n    }\n  }, [resizeState]);\n\n  // We lock resize trigger by raf to avoid Safari warning\n  var resizeRafRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n  var cleanRaf = function cleanRaf() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"].cancel(resizeRafRef.current);\n  };\n  var onInternalResize = function onInternalResize(size) {\n    if (resizeState === RESIZE_STABLE) {\n      onResize === null || onResize === void 0 || onResize(size);\n      if (autoSize) {\n        cleanRaf();\n        resizeRafRef.current = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function () {\n          startResize();\n        });\n      }\n    }\n  };\n  react__WEBPACK_IMPORTED_MODULE_11__.useEffect(function () {\n    return cleanRaf;\n  }, []);\n\n  // =============================== Render ===============================\n  var mergedAutoSizeStyle = needAutoSize ? autoSizeStyle : null;\n  var mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), mergedAutoSizeStyle);\n  if (resizeState === RESIZE_START || resizeState === RESIZE_MEASURING) {\n    mergedStyle.overflowY = 'hidden';\n    mergedStyle.overflowX = 'hidden';\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n    onResize: onInternalResize,\n    disabled: !(autoSize || onResize)\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps, {\n    ref: textareaRef,\n    style: mergedStyle,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n    disabled: disabled,\n    value: mergedValue,\n    onChange: onInternalChange\n  })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResizableTextArea);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/TextArea.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-textarea/es/TextArea.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-input */ \"(ssr)/./node_modules/rc-input/es/index.js\");\n/* harmony import */ var rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-input/es/hooks/useCount */ \"(ssr)/./node_modules/rc-input/es/hooks/useCount.js\");\n/* harmony import */ var rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-input/es/utils/commonUtils */ \"(ssr)/./node_modules/rc-input/es/utils/commonUtils.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n\n\n\n\nvar _excluded = [\"defaultValue\", \"value\", \"onFocus\", \"onBlur\", \"onChange\", \"allowClear\", \"maxLength\", \"onCompositionStart\", \"onCompositionEnd\", \"suffix\", \"prefixCls\", \"showCount\", \"count\", \"className\", \"style\", \"disabled\", \"hidden\", \"classNames\", \"styles\", \"onResize\", \"onClear\", \"onPressEnter\", \"readOnly\", \"autoSize\", \"onKeyDown\"];\n\n\n\n\n\n\n\nvar TextArea = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().forwardRef(function (_ref, ref) {\n  var _countConfig$max;\n  var defaultValue = _ref.defaultValue,\n    customValue = _ref.value,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    onChange = _ref.onChange,\n    allowClear = _ref.allowClear,\n    maxLength = _ref.maxLength,\n    onCompositionStart = _ref.onCompositionStart,\n    onCompositionEnd = _ref.onCompositionEnd,\n    suffix = _ref.suffix,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-textarea' : _ref$prefixCls,\n    showCount = _ref.showCount,\n    count = _ref.count,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    hidden = _ref.hidden,\n    classNames = _ref.classNames,\n    styles = _ref.styles,\n    onResize = _ref.onResize,\n    onClear = _ref.onClear,\n    onPressEnter = _ref.onPressEnter,\n    readOnly = _ref.readOnly,\n    autoSize = _ref.autoSize,\n    onKeyDown = _ref.onKeyDown,\n    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(defaultValue, {\n      value: customValue,\n      defaultValue: defaultValue\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var formatValue = value === undefined || value === null ? '' : String(value);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_11___default().useState(false),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var compositionRef = react__WEBPACK_IMPORTED_MODULE_11___default().useRef(false);\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2),\n    textareaResized = _React$useState4[0],\n    setTextareaResized = _React$useState4[1];\n\n  // =============================== Ref ================================\n  var holderRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var resizableTextAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var getTextArea = function getTextArea() {\n    var _resizableTextAreaRef;\n    return (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea;\n  };\n  var focus = function focus() {\n    getTextArea().focus();\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle)(ref, function () {\n    var _holderRef$current;\n    return {\n      resizableTextArea: resizableTextAreaRef.current,\n      focus: focus,\n      blur: function blur() {\n        getTextArea().blur();\n      },\n      nativeElement: ((_holderRef$current = holderRef.current) === null || _holderRef$current === void 0 ? void 0 : _holderRef$current.nativeElement) || getTextArea()\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {\n    setFocused(function (prev) {\n      return !disabled && prev;\n    });\n  }, [disabled]);\n\n  // =========================== Select Range ===========================\n  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null),\n    _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState5, 2),\n    selection = _React$useState6[0],\n    setSelection = _React$useState6[1];\n  react__WEBPACK_IMPORTED_MODULE_11___default().useEffect(function () {\n    if (selection) {\n      var _getTextArea;\n      (_getTextArea = getTextArea()).setSelectionRange.apply(_getTextArea, (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(selection));\n    }\n  }, [selection]);\n\n  // ============================== Count ===============================\n  var countConfig = (0,rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(count, showCount);\n  var mergedMax = (_countConfig$max = countConfig.max) !== null && _countConfig$max !== void 0 ? _countConfig$max : maxLength;\n\n  // Max length value\n  var hasMaxLength = Number(mergedMax) > 0;\n  var valueLength = countConfig.strategy(formatValue);\n  var isOutOfRange = !!mergedMax && valueLength > mergedMax;\n\n  // ============================== Change ==============================\n  var triggerChange = function triggerChange(e, currentValue) {\n    var cutValue = currentValue;\n    if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {\n      cutValue = countConfig.exceedFormatter(currentValue, {\n        max: countConfig.max\n      });\n      if (currentValue !== cutValue) {\n        setSelection([getTextArea().selectionStart || 0, getTextArea().selectionEnd || 0]);\n      }\n    }\n    setValue(cutValue);\n    (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(e.currentTarget, e, onChange, cutValue);\n  };\n\n  // =========================== Value Update ===========================\n  var onInternalCompositionStart = function onInternalCompositionStart(e) {\n    compositionRef.current = true;\n    onCompositionStart === null || onCompositionStart === void 0 || onCompositionStart(e);\n  };\n  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n    compositionRef.current = false;\n    triggerChange(e, e.currentTarget.value);\n    onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);\n  };\n  var onInternalChange = function onInternalChange(e) {\n    triggerChange(e, e.target.value);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    if (e.key === 'Enter' && onPressEnter) {\n      onPressEnter(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);\n  };\n  var handleFocus = function handleFocus(e) {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 || onFocus(e);\n  };\n  var handleBlur = function handleBlur(e) {\n    setFocused(false);\n    onBlur === null || onBlur === void 0 || onBlur(e);\n  };\n\n  // ============================== Reset ===============================\n  var handleReset = function handleReset(e) {\n    setValue('');\n    focus();\n    (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(getTextArea(), e, onChange);\n  };\n  var suffixNode = suffix;\n  var dataCount;\n  if (countConfig.show) {\n    if (countConfig.showFormatter) {\n      dataCount = countConfig.showFormatter({\n        value: formatValue,\n        count: valueLength,\n        maxLength: mergedMax\n      });\n    } else {\n      dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(mergedMax) : '');\n    }\n    suffixNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement((react__WEBPACK_IMPORTED_MODULE_11___default().Fragment), null, suffixNode, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(\"span\", {\n      className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"\".concat(prefixCls, \"-data-count\"), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n      style: styles === null || styles === void 0 ? void 0 : styles.count\n    }, dataCount));\n  }\n  var handleResize = function handleResize(size) {\n    var _getTextArea2;\n    onResize === null || onResize === void 0 || onResize(size);\n    if ((_getTextArea2 = getTextArea()) !== null && _getTextArea2 !== void 0 && _getTextArea2.style.height) {\n      setTextareaResized(true);\n    }\n  };\n  var isPureTextArea = !autoSize && !showCount && !allowClear;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(rc_input__WEBPACK_IMPORTED_MODULE_7__.BaseInput, {\n    ref: holderRef,\n    value: formatValue,\n    allowClear: allowClear,\n    handleReset: handleReset,\n    suffix: suffixNode,\n    prefixCls: prefixCls,\n    classNames: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, classNames), {}, {\n      affixWrapper: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-show-count\"), showCount), \"\".concat(prefixCls, \"-textarea-allow-clear\"), allowClear))\n    }),\n    disabled: disabled,\n    focused: focused,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(className, isOutOfRange && \"\".concat(prefixCls, \"-out-of-range\")),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), textareaResized && !isPureTextArea ? {\n      height: 'auto'\n    } : {}),\n    dataAttrs: {\n      affixWrapper: {\n        'data-count': typeof dataCount === 'string' ? dataCount : undefined\n      }\n    },\n    hidden: hidden,\n    readOnly: readOnly,\n    onClear: onClear\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(_ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, rest, {\n    autoSize: autoSize,\n    maxLength: maxLength,\n    onKeyDown: handleKeyDown,\n    onChange: onInternalChange,\n    onFocus: handleFocus,\n    onBlur: handleBlur,\n    onCompositionStart: onInternalCompositionStart,\n    onCompositionEnd: onInternalCompositionEnd,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.textarea),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {\n      resize: style === null || style === void 0 ? void 0 : style.resize\n    }),\n    disabled: disabled,\n    prefixCls: prefixCls,\n    onResize: handleResize,\n    ref: resizableTextAreaRef,\n    readOnly: readOnly\n  })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TextArea);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/TextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-textarea/es/calculateNodeHeight.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateNodeStyling: () => (/* binding */ calculateNodeStyling),\n/* harmony export */   \"default\": () => (/* binding */ calculateAutoSizeStyle)\n/* harmony export */ });\n// Thanks to https://github.com/andreypopp/react-textarea-autosize/\n\n/**\n * calculateNodeHeight(uiTextNode, useCache = false)\n */\n\nvar HIDDEN_TEXTAREA_STYLE = \"\\n  min-height:0 !important;\\n  max-height:none !important;\\n  height:0 !important;\\n  visibility:hidden !important;\\n  overflow:hidden !important;\\n  position:absolute !important;\\n  z-index:-1000 !important;\\n  top:0 !important;\\n  right:0 !important;\\n  pointer-events: none !important;\\n\";\nvar SIZING_STYLE = ['letter-spacing', 'line-height', 'padding-top', 'padding-bottom', 'font-family', 'font-weight', 'font-size', 'font-variant', 'text-rendering', 'text-transform', 'width', 'text-indent', 'padding-left', 'padding-right', 'border-width', 'box-sizing', 'word-break', 'white-space'];\nvar computedStyleCache = {};\nvar hiddenTextarea;\nfunction calculateNodeStyling(node) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var nodeRef = node.getAttribute('id') || node.getAttribute('data-reactid') || node.getAttribute('name');\n  if (useCache && computedStyleCache[nodeRef]) {\n    return computedStyleCache[nodeRef];\n  }\n  var style = window.getComputedStyle(node);\n  var boxSizing = style.getPropertyValue('box-sizing') || style.getPropertyValue('-moz-box-sizing') || style.getPropertyValue('-webkit-box-sizing');\n  var paddingSize = parseFloat(style.getPropertyValue('padding-bottom')) + parseFloat(style.getPropertyValue('padding-top'));\n  var borderSize = parseFloat(style.getPropertyValue('border-bottom-width')) + parseFloat(style.getPropertyValue('border-top-width'));\n  var sizingStyle = SIZING_STYLE.map(function (name) {\n    return \"\".concat(name, \":\").concat(style.getPropertyValue(name));\n  }).join(';');\n  var nodeInfo = {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize,\n    boxSizing: boxSizing\n  };\n  if (useCache && nodeRef) {\n    computedStyleCache[nodeRef] = nodeInfo;\n  }\n  return nodeInfo;\n}\nfunction calculateAutoSizeStyle(uiTextNode) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var minRows = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var maxRows = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tab-index', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    // fix: A form field element should have an id or name attribute\n    // A form field element has neither an id nor a name attribute. This might prevent the browser from correctly autofilling the form.\n    // https://developer.mozilla.org/en-US/docs/Web/HTML/Element/textarea\n    hiddenTextarea.setAttribute('name', 'hiddenTextarea');\n    document.body.appendChild(hiddenTextarea);\n  }\n\n  // Fix wrap=\"off\" issue\n  // https://github.com/ant-design/ant-design/issues/6577\n  if (uiTextNode.getAttribute('wrap')) {\n    hiddenTextarea.setAttribute('wrap', uiTextNode.getAttribute('wrap'));\n  } else {\n    hiddenTextarea.removeAttribute('wrap');\n  }\n\n  // Copy all CSS properties that have an impact on the height of the content in\n  // the textbox\n  var _calculateNodeStyling = calculateNodeStyling(uiTextNode, useCache),\n    paddingSize = _calculateNodeStyling.paddingSize,\n    borderSize = _calculateNodeStyling.borderSize,\n    boxSizing = _calculateNodeStyling.boxSizing,\n    sizingStyle = _calculateNodeStyling.sizingStyle;\n\n  // Need to have the overflow attribute to hide the scrollbar otherwise\n  // text-lines will not calculated properly as the shadow will technically be\n  // narrower for content\n  hiddenTextarea.setAttribute('style', \"\".concat(sizingStyle, \";\").concat(HIDDEN_TEXTAREA_STYLE));\n  hiddenTextarea.value = uiTextNode.value || uiTextNode.placeholder || '';\n  var minHeight = undefined;\n  var maxHeight = undefined;\n  var overflowY;\n  var height = hiddenTextarea.scrollHeight;\n  if (boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    height += borderSize;\n  } else if (boxSizing === 'content-box') {\n    // remove padding, since height = content\n    height -= paddingSize;\n  }\n  if (minRows !== null || maxRows !== null) {\n    // measure height of a textarea with a single row\n    hiddenTextarea.value = ' ';\n    var singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n    if (minRows !== null) {\n      minHeight = singleRowHeight * minRows;\n      if (boxSizing === 'border-box') {\n        minHeight = minHeight + paddingSize + borderSize;\n      }\n      height = Math.max(minHeight, height);\n    }\n    if (maxRows !== null) {\n      maxHeight = singleRowHeight * maxRows;\n      if (boxSizing === 'border-box') {\n        maxHeight = maxHeight + paddingSize + borderSize;\n      }\n      overflowY = height > maxHeight ? '' : 'hidden';\n      height = Math.min(maxHeight, height);\n    }\n  }\n  var style = {\n    height: height,\n    overflowY: overflowY,\n    resize: 'none'\n  };\n  if (minHeight) {\n    style.minHeight = minHeight;\n  }\n  if (maxHeight) {\n    style.maxHeight = maxHeight;\n  }\n  return style;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-textarea/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResizableTextArea: () => (/* reexport safe */ _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _TextArea__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextArea */ \"(ssr)/./node_modules/rc-textarea/es/TextArea.js\");\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_TextArea__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGV4dGFyZWEvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrQztBQUNpQztBQUNuRSxpRUFBZSxpREFBUSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcZmVuZXJiYWhjZS1mb3JtXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtdGV4dGFyZWFcXGVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgVGV4dEFyZWEgZnJvbSBcIi4vVGV4dEFyZWFcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUmVzaXphYmxlVGV4dEFyZWEgfSBmcm9tIFwiLi9SZXNpemFibGVUZXh0QXJlYVwiO1xuZXhwb3J0IGRlZmF1bHQgVGV4dEFyZWE7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/index.js\n");

/***/ })

};
;